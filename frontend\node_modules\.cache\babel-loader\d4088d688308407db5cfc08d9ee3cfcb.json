{"ast": null, "code": "import { mix } from './mix.es.js';\nimport { noopReturn } from './noop.es.js';\nimport { fillOffset, defaultOffset } from './offset.es.js';\nimport { progress } from './progress.es.js';\nimport { getEasingForSegment } from './easing.es.js';\nimport { clamp } from './clamp.es.js';\nfunction interpolate(output, input = defaultOffset(output.length), easing = noopReturn) {\n  const length = output.length;\n  /**\n   * If the input length is lower than the output we\n   * fill the input to match. This currently assumes the input\n   * is an animation progress value so is a good candidate for\n   * moving outside the function.\n   */\n  const remainder = length - input.length;\n  remainder > 0 && fillOffset(input, remainder);\n  return t => {\n    let i = 0;\n    for (; i < length - 2; i++) {\n      if (t < input[i + 1]) break;\n    }\n    let progressInRange = clamp(0, 1, progress(input[i], input[i + 1], t));\n    const segmentEasing = getEasingForSegment(easing, i);\n    progressInRange = segmentEasing(progressInRange);\n    return mix(output[i], output[i + 1], progressInRange);\n  };\n}\nexport { interpolate };", "map": {"version": 3, "names": ["mix", "noopReturn", "fillOffset", "defaultOffset", "progress", "getEasingForSegment", "clamp", "interpolate", "output", "input", "length", "easing", "remainder", "t", "i", "progressInRange", "segmentEasing"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/interpolate.es.js"], "sourcesContent": ["import { mix } from './mix.es.js';\nimport { noopReturn } from './noop.es.js';\nimport { fillOffset, defaultOffset } from './offset.es.js';\nimport { progress } from './progress.es.js';\nimport { getEasingForSegment } from './easing.es.js';\nimport { clamp } from './clamp.es.js';\n\nfunction interpolate(output, input = defaultOffset(output.length), easing = noopReturn) {\n    const length = output.length;\n    /**\n     * If the input length is lower than the output we\n     * fill the input to match. This currently assumes the input\n     * is an animation progress value so is a good candidate for\n     * moving outside the function.\n     */\n    const remainder = length - input.length;\n    remainder > 0 && fillOffset(input, remainder);\n    return (t) => {\n        let i = 0;\n        for (; i < length - 2; i++) {\n            if (t < input[i + 1])\n                break;\n        }\n        let progressInRange = clamp(0, 1, progress(input[i], input[i + 1], t));\n        const segmentEasing = getEasingForSegment(easing, i);\n        progressInRange = segmentEasing(progressInRange);\n        return mix(output[i], output[i + 1], progressInRange);\n    };\n}\n\nexport { interpolate };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,UAAU,EAAEC,aAAa,QAAQ,gBAAgB;AAC1D,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,KAAK,QAAQ,eAAe;AAErC,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,GAAGN,aAAa,CAACK,MAAM,CAACE,MAAM,CAAC,EAAEC,MAAM,GAAGV,UAAU,EAAE;EACpF,MAAMS,MAAM,GAAGF,MAAM,CAACE,MAAM;EAC5B;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,SAAS,GAAGF,MAAM,GAAGD,KAAK,CAACC,MAAM;EACvCE,SAAS,GAAG,CAAC,IAAIV,UAAU,CAACO,KAAK,EAAEG,SAAS,CAAC;EAC7C,OAAQC,CAAC,IAAK;IACV,IAAIC,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGJ,MAAM,GAAG,CAAC,EAAEI,CAAC,EAAE,EAAE;MACxB,IAAID,CAAC,GAAGJ,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,EAChB;IACR;IACA,IAAIC,eAAe,GAAGT,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEF,QAAQ,CAACK,KAAK,CAACK,CAAC,CAAC,EAAEL,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC;IACtE,MAAMG,aAAa,GAAGX,mBAAmB,CAACM,MAAM,EAAEG,CAAC,CAAC;IACpDC,eAAe,GAAGC,aAAa,CAACD,eAAe,CAAC;IAChD,OAAOf,GAAG,CAACQ,MAAM,CAACM,CAAC,CAAC,EAAEN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,EAAEC,eAAe,CAAC;EACzD,CAAC;AACL;AAEA,SAASR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}