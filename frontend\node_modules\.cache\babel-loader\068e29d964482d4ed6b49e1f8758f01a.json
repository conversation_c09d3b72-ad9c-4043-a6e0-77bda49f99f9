{"ast": null, "code": "import { cubicBezier, steps } from '@motionone/easing';\nimport { isFunction, isCubicBezier, noopReturn } from '@motionone/utils';\nconst namedEasings = {\n  ease: cubicBezier(0.25, 0.1, 0.25, 1.0),\n  \"ease-in\": cubicBezier(0.42, 0.0, 1.0, 1.0),\n  \"ease-in-out\": cubicBezier(0.42, 0.0, 0.58, 1.0),\n  \"ease-out\": cubicBezier(0.0, 0.0, 0.58, 1.0)\n};\nconst functionArgsRegex = /\\((.*?)\\)/;\nfunction getEasingFunction(definition) {\n  // If already an easing function, return\n  if (isFunction(definition)) return definition;\n  // If an easing curve definition, return bezier function\n  if (isCubicBezier(definition)) return cubicBezier(...definition);\n  // If we have a predefined easing function, return\n  const namedEasing = namedEasings[definition];\n  if (namedEasing) return namedEasing;\n  // If this is a steps function, attempt to create easing curve\n  if (definition.startsWith(\"steps\")) {\n    const args = functionArgsRegex.exec(definition);\n    if (args) {\n      const argsArray = args[1].split(\",\");\n      return steps(parseFloat(argsArray[0]), argsArray[1].trim());\n    }\n  }\n  return noopReturn;\n}\nexport { getEasingFunction };", "map": {"version": 3, "names": ["cubicBezier", "steps", "isFunction", "isCubicBezier", "noopReturn", "namedEasings", "ease", "functionArgsRegex", "getEasingFunction", "definition", "namedEasing", "startsWith", "args", "exec", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON>", "split", "parseFloat", "trim"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/animation/dist/utils/easing.es.js"], "sourcesContent": ["import { cubicBezier, steps } from '@motionone/easing';\nimport { isFunction, isCubicBezier, noopReturn } from '@motionone/utils';\n\nconst namedEasings = {\n    ease: cubicBezier(0.25, 0.1, 0.25, 1.0),\n    \"ease-in\": cubicBezier(0.42, 0.0, 1.0, 1.0),\n    \"ease-in-out\": cubicBezier(0.42, 0.0, 0.58, 1.0),\n    \"ease-out\": cubicBezier(0.0, 0.0, 0.58, 1.0),\n};\nconst functionArgsRegex = /\\((.*?)\\)/;\nfunction getEasingFunction(definition) {\n    // If already an easing function, return\n    if (isFunction(definition))\n        return definition;\n    // If an easing curve definition, return bezier function\n    if (isCubicBezier(definition))\n        return cubicBezier(...definition);\n    // If we have a predefined easing function, return\n    const namedEasing = namedEasings[definition];\n    if (namedEasing)\n        return namedEasing;\n    // If this is a steps function, attempt to create easing curve\n    if (definition.startsWith(\"steps\")) {\n        const args = functionArgsRegex.exec(definition);\n        if (args) {\n            const argsArray = args[1].split(\",\");\n            return steps(parseFloat(argsArray[0]), argsArray[1].trim());\n        }\n    }\n    return noopReturn;\n}\n\nexport { getEasingFunction };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,KAAK,QAAQ,mBAAmB;AACtD,SAASC,UAAU,EAAEC,aAAa,EAAEC,UAAU,QAAQ,kBAAkB;AAExE,MAAMC,YAAY,GAAG;EACjBC,IAAI,EAAEN,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EACvC,SAAS,EAAEA,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3C,aAAa,EAAEA,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EAChD,UAAU,EAAEA,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;AAC/C,CAAC;AACD,MAAMO,iBAAiB,GAAG,WAAW;AACrC,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACnC;EACA,IAAIP,UAAU,CAACO,UAAU,CAAC,EACtB,OAAOA,UAAU;EACrB;EACA,IAAIN,aAAa,CAACM,UAAU,CAAC,EACzB,OAAOT,WAAW,CAAC,GAAGS,UAAU,CAAC;EACrC;EACA,MAAMC,WAAW,GAAGL,YAAY,CAACI,UAAU,CAAC;EAC5C,IAAIC,WAAW,EACX,OAAOA,WAAW;EACtB;EACA,IAAID,UAAU,CAACE,UAAU,CAAC,OAAO,CAAC,EAAE;IAChC,MAAMC,IAAI,GAAGL,iBAAiB,CAACM,IAAI,CAACJ,UAAU,CAAC;IAC/C,IAAIG,IAAI,EAAE;MACN,MAAME,SAAS,GAAGF,IAAI,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,GAAG,CAAC;MACpC,OAAOd,KAAK,CAACe,UAAU,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;IAC/D;EACJ;EACA,OAAOb,UAAU;AACrB;AAEA,SAASI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}