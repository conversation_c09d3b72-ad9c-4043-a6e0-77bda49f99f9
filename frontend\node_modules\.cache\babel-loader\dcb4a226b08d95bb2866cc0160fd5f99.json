{"ast": null, "code": "import { animate } from './index.mjs';\nimport { velocityPerSecond } from '../../utils/velocity-per-second.mjs';\nimport { frameData } from '../../frameloop/data.mjs';\nfunction inertia({\n  keyframes,\n  velocity = 0,\n  min,\n  max,\n  power = 0.8,\n  timeConstant = 750,\n  bounceStiffness = 500,\n  bounceDamping = 10,\n  restDelta = 1,\n  modifyTarget,\n  driver,\n  onUpdate,\n  onComplete,\n  onStop\n}) {\n  const origin = keyframes[0];\n  let currentAnimation;\n  function isOutOfBounds(v) {\n    return min !== undefined && v < min || max !== undefined && v > max;\n  }\n  function findNearestBoundary(v) {\n    if (min === undefined) return max;\n    if (max === undefined) return min;\n    return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n  }\n  function startAnimation(options) {\n    currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop();\n    currentAnimation = animate({\n      keyframes: [0, 1],\n      velocity: 0,\n      ...options,\n      driver,\n      onUpdate: v => {\n        var _a;\n        onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(v);\n        (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, v);\n      },\n      onComplete,\n      onStop\n    });\n  }\n  function startSpring(options) {\n    startAnimation({\n      type: \"spring\",\n      stiffness: bounceStiffness,\n      damping: bounceDamping,\n      restDelta,\n      ...options\n    });\n  }\n  if (isOutOfBounds(origin)) {\n    // Start the animation with spring if outside the defined boundaries\n    startSpring({\n      velocity,\n      keyframes: [origin, findNearestBoundary(origin)]\n    });\n  } else {\n    /**\n     * Or if the value is out of bounds, simulate the inertia movement\n     * with the decay animation.\n     *\n     * Pre-calculate the target so we can detect if it's out-of-bounds.\n     * If it is, we want to check per frame when to switch to a spring\n     * animation\n     */\n    let target = power * velocity + origin;\n    if (typeof modifyTarget !== \"undefined\") target = modifyTarget(target);\n    const boundary = findNearestBoundary(target);\n    const heading = boundary === min ? -1 : 1;\n    let prev;\n    let current;\n    const checkBoundary = v => {\n      prev = current;\n      current = v;\n      velocity = velocityPerSecond(v - prev, frameData.delta);\n      if (heading === 1 && v > boundary || heading === -1 && v < boundary) {\n        startSpring({\n          keyframes: [v, boundary],\n          velocity\n        });\n      }\n    };\n    startAnimation({\n      type: \"decay\",\n      keyframes: [origin, 0],\n      velocity,\n      timeConstant,\n      power,\n      restDelta,\n      modifyTarget,\n      onUpdate: isOutOfBounds(target) ? checkBoundary : undefined\n    });\n  }\n  return {\n    stop: () => currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop()\n  };\n}\nexport { inertia };", "map": {"version": 3, "names": ["animate", "velocityPerSecond", "frameData", "inertia", "keyframes", "velocity", "min", "max", "power", "timeConstant", "bounceStiffness", "bounceDamping", "restDelta", "modifyTarget", "driver", "onUpdate", "onComplete", "onStop", "origin", "currentAnimation", "isOutOfBounds", "v", "undefined", "findNearestBoundary", "Math", "abs", "startAnimation", "options", "stop", "_a", "call", "startSpring", "type", "stiffness", "damping", "target", "boundary", "heading", "prev", "current", "checkBoundary", "delta"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/legacy-popmotion/inertia.mjs"], "sourcesContent": ["import { animate } from './index.mjs';\nimport { velocityPerSecond } from '../../utils/velocity-per-second.mjs';\nimport { frameData } from '../../frameloop/data.mjs';\n\nfunction inertia({ keyframes, velocity = 0, min, max, power = 0.8, timeConstant = 750, bounceStiffness = 500, bounceDamping = 10, restDelta = 1, modifyTarget, driver, onUpdate, onComplete, onStop, }) {\n    const origin = keyframes[0];\n    let currentAnimation;\n    function isOutOfBounds(v) {\n        return (min !== undefined && v < min) || (max !== undefined && v > max);\n    }\n    function findNearestBoundary(v) {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    }\n    function startAnimation(options) {\n        currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop();\n        currentAnimation = animate({\n            keyframes: [0, 1],\n            velocity: 0,\n            ...options,\n            driver,\n            onUpdate: (v) => {\n                var _a;\n                onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(v);\n                (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, v);\n            },\n            onComplete,\n            onStop,\n        });\n    }\n    function startSpring(options) {\n        startAnimation({\n            type: \"spring\",\n            stiffness: bounceStiffness,\n            damping: bounceDamping,\n            restDelta,\n            ...options,\n        });\n    }\n    if (isOutOfBounds(origin)) {\n        // Start the animation with spring if outside the defined boundaries\n        startSpring({\n            velocity,\n            keyframes: [origin, findNearestBoundary(origin)],\n        });\n    }\n    else {\n        /**\n         * Or if the value is out of bounds, simulate the inertia movement\n         * with the decay animation.\n         *\n         * Pre-calculate the target so we can detect if it's out-of-bounds.\n         * If it is, we want to check per frame when to switch to a spring\n         * animation\n         */\n        let target = power * velocity + origin;\n        if (typeof modifyTarget !== \"undefined\")\n            target = modifyTarget(target);\n        const boundary = findNearestBoundary(target);\n        const heading = boundary === min ? -1 : 1;\n        let prev;\n        let current;\n        const checkBoundary = (v) => {\n            prev = current;\n            current = v;\n            velocity = velocityPerSecond(v - prev, frameData.delta);\n            if ((heading === 1 && v > boundary) ||\n                (heading === -1 && v < boundary)) {\n                startSpring({ keyframes: [v, boundary], velocity });\n            }\n        };\n        startAnimation({\n            type: \"decay\",\n            keyframes: [origin, 0],\n            velocity,\n            timeConstant,\n            power,\n            restDelta,\n            modifyTarget,\n            onUpdate: isOutOfBounds(target) ? checkBoundary : undefined,\n        });\n    }\n    return {\n        stop: () => currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop(),\n    };\n}\n\nexport { inertia };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,OAAOA,CAAC;EAAEC,SAAS;EAAEC,QAAQ,GAAG,CAAC;EAAEC,GAAG;EAAEC,GAAG;EAAEC,KAAK,GAAG,GAAG;EAAEC,YAAY,GAAG,GAAG;EAAEC,eAAe,GAAG,GAAG;EAAEC,aAAa,GAAG,EAAE;EAAEC,SAAS,GAAG,CAAC;EAAEC,YAAY;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAQ,CAAC,EAAE;EACpM,MAAMC,MAAM,GAAGd,SAAS,CAAC,CAAC,CAAC;EAC3B,IAAIe,gBAAgB;EACpB,SAASC,aAAaA,CAACC,CAAC,EAAE;IACtB,OAAQf,GAAG,KAAKgB,SAAS,IAAID,CAAC,GAAGf,GAAG,IAAMC,GAAG,KAAKe,SAAS,IAAID,CAAC,GAAGd,GAAI;EAC3E;EACA,SAASgB,mBAAmBA,CAACF,CAAC,EAAE;IAC5B,IAAIf,GAAG,KAAKgB,SAAS,EACjB,OAAOf,GAAG;IACd,IAAIA,GAAG,KAAKe,SAAS,EACjB,OAAOhB,GAAG;IACd,OAAOkB,IAAI,CAACC,GAAG,CAACnB,GAAG,GAAGe,CAAC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAAClB,GAAG,GAAGc,CAAC,CAAC,GAAGf,GAAG,GAAGC,GAAG;EAC5D;EACA,SAASmB,cAAcA,CAACC,OAAO,EAAE;IAC7BR,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACS,IAAI,CAAC,CAAC;IAC3FT,gBAAgB,GAAGnB,OAAO,CAAC;MACvBI,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACjBC,QAAQ,EAAE,CAAC;MACX,GAAGsB,OAAO;MACVb,MAAM;MACNC,QAAQ,EAAGM,CAAC,IAAK;QACb,IAAIQ,EAAE;QACNd,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACM,CAAC,CAAC;QAC/D,CAACQ,EAAE,GAAGF,OAAO,CAACZ,QAAQ,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACH,OAAO,EAAEN,CAAC,CAAC;MACpF,CAAC;MACDL,UAAU;MACVC;IACJ,CAAC,CAAC;EACN;EACA,SAASc,WAAWA,CAACJ,OAAO,EAAE;IAC1BD,cAAc,CAAC;MACXM,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAEvB,eAAe;MAC1BwB,OAAO,EAAEvB,aAAa;MACtBC,SAAS;MACT,GAAGe;IACP,CAAC,CAAC;EACN;EACA,IAAIP,aAAa,CAACF,MAAM,CAAC,EAAE;IACvB;IACAa,WAAW,CAAC;MACR1B,QAAQ;MACRD,SAAS,EAAE,CAACc,MAAM,EAAEK,mBAAmB,CAACL,MAAM,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,MACI;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIiB,MAAM,GAAG3B,KAAK,GAAGH,QAAQ,GAAGa,MAAM;IACtC,IAAI,OAAOL,YAAY,KAAK,WAAW,EACnCsB,MAAM,GAAGtB,YAAY,CAACsB,MAAM,CAAC;IACjC,MAAMC,QAAQ,GAAGb,mBAAmB,CAACY,MAAM,CAAC;IAC5C,MAAME,OAAO,GAAGD,QAAQ,KAAK9B,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IACzC,IAAIgC,IAAI;IACR,IAAIC,OAAO;IACX,MAAMC,aAAa,GAAInB,CAAC,IAAK;MACzBiB,IAAI,GAAGC,OAAO;MACdA,OAAO,GAAGlB,CAAC;MACXhB,QAAQ,GAAGJ,iBAAiB,CAACoB,CAAC,GAAGiB,IAAI,EAAEpC,SAAS,CAACuC,KAAK,CAAC;MACvD,IAAKJ,OAAO,KAAK,CAAC,IAAIhB,CAAC,GAAGe,QAAQ,IAC7BC,OAAO,KAAK,CAAC,CAAC,IAAIhB,CAAC,GAAGe,QAAS,EAAE;QAClCL,WAAW,CAAC;UAAE3B,SAAS,EAAE,CAACiB,CAAC,EAAEe,QAAQ,CAAC;UAAE/B;QAAS,CAAC,CAAC;MACvD;IACJ,CAAC;IACDqB,cAAc,CAAC;MACXM,IAAI,EAAE,OAAO;MACb5B,SAAS,EAAE,CAACc,MAAM,EAAE,CAAC,CAAC;MACtBb,QAAQ;MACRI,YAAY;MACZD,KAAK;MACLI,SAAS;MACTC,YAAY;MACZE,QAAQ,EAAEK,aAAa,CAACe,MAAM,CAAC,GAAGK,aAAa,GAAGlB;IACtD,CAAC,CAAC;EACN;EACA,OAAO;IACHM,IAAI,EAAEA,CAAA,KAAMT,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACS,IAAI,CAAC;EAC1G,CAAC;AACL;AAEA,SAASzB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}