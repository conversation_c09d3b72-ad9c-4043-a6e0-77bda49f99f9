{"ast": null, "code": "import { addDomEvent, useDomEvent } from './use-dom-event.mjs';\nimport { wrapHandler } from './event-info.mjs';\nimport { supportsPointerEvents, supportsTouchEvents, supportsMouseEvents } from './utils.mjs';\nconst mouseEventNames = {\n  pointerdown: \"mousedown\",\n  pointermove: \"mousemove\",\n  pointerup: \"mouseup\",\n  pointercancel: \"mousecancel\",\n  pointerover: \"mouseover\",\n  pointerout: \"mouseout\",\n  pointerenter: \"mouseenter\",\n  pointerleave: \"mouseleave\"\n};\nconst touchEventNames = {\n  pointerdown: \"touchstart\",\n  pointermove: \"touchmove\",\n  pointerup: \"touchend\",\n  pointercancel: \"touchcancel\"\n};\nfunction getPointerEventName(name) {\n  if (supportsPointerEvents()) {\n    return name;\n  } else if (supportsTouchEvents()) {\n    return touchEventNames[name];\n  } else if (supportsMouseEvents()) {\n    return mouseEventNames[name];\n  }\n  return name;\n}\nfunction addPointerEvent(target, eventName, handler, options) {\n  return addDomEvent(target, getPointerEventName(eventName), wrapHandler(handler, eventName === \"pointerdown\"), options);\n}\nfunction usePointerEvent(ref, eventName, handler, options) {\n  return useDomEvent(ref, getPointerEventName(eventName), handler && wrapHandler(handler, eventName === \"pointerdown\"), options);\n}\nexport { addPointerEvent, usePointerEvent };", "map": {"version": 3, "names": ["addDomEvent", "useDomEvent", "wrapHandler", "supportsPointerEvents", "supportsTouchEvents", "supportsMouseEvents", "mouseEventNames", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerover", "pointerout", "pointerenter", "pointerleave", "touchEventNames", "getPointerEventName", "name", "addPointerEvent", "target", "eventName", "handler", "options", "usePointerEvent", "ref"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/events/use-pointer-event.mjs"], "sourcesContent": ["import { addDomEvent, useDomEvent } from './use-dom-event.mjs';\nimport { wrapHandler } from './event-info.mjs';\nimport { supportsPointerEvents, supportsTouchEvents, supportsMouseEvents } from './utils.mjs';\n\nconst mouseEventNames = {\n    pointerdown: \"mousedown\",\n    pointermove: \"mousemove\",\n    pointerup: \"mouseup\",\n    pointercancel: \"mousecancel\",\n    pointerover: \"mouseover\",\n    pointerout: \"mouseout\",\n    pointerenter: \"mouseenter\",\n    pointerleave: \"mouseleave\",\n};\nconst touchEventNames = {\n    pointerdown: \"touchstart\",\n    pointermove: \"touchmove\",\n    pointerup: \"touchend\",\n    pointercancel: \"touchcancel\",\n};\nfunction getPointerEventName(name) {\n    if (supportsPointerEvents()) {\n        return name;\n    }\n    else if (supportsTouchEvents()) {\n        return touchEventNames[name];\n    }\n    else if (supportsMouseEvents()) {\n        return mouseEventNames[name];\n    }\n    return name;\n}\nfunction addPointerEvent(target, eventName, handler, options) {\n    return addDomEvent(target, getPointerEventName(eventName), wrapHandler(handler, eventName === \"pointerdown\"), options);\n}\nfunction usePointerEvent(ref, eventName, handler, options) {\n    return useDomEvent(ref, getPointerEventName(eventName), handler && wrapHandler(handler, eventName === \"pointerdown\"), options);\n}\n\nexport { addPointerEvent, usePointerEvent };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,qBAAqB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,aAAa;AAE7F,MAAMC,eAAe,GAAG;EACpBC,WAAW,EAAE,WAAW;EACxBC,WAAW,EAAE,WAAW;EACxBC,SAAS,EAAE,SAAS;EACpBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,WAAW;EACxBC,UAAU,EAAE,UAAU;EACtBC,YAAY,EAAE,YAAY;EAC1BC,YAAY,EAAE;AAClB,CAAC;AACD,MAAMC,eAAe,GAAG;EACpBR,WAAW,EAAE,YAAY;EACzBC,WAAW,EAAE,WAAW;EACxBC,SAAS,EAAE,UAAU;EACrBC,aAAa,EAAE;AACnB,CAAC;AACD,SAASM,mBAAmBA,CAACC,IAAI,EAAE;EAC/B,IAAId,qBAAqB,CAAC,CAAC,EAAE;IACzB,OAAOc,IAAI;EACf,CAAC,MACI,IAAIb,mBAAmB,CAAC,CAAC,EAAE;IAC5B,OAAOW,eAAe,CAACE,IAAI,CAAC;EAChC,CAAC,MACI,IAAIZ,mBAAmB,CAAC,CAAC,EAAE;IAC5B,OAAOC,eAAe,CAACW,IAAI,CAAC;EAChC;EACA,OAAOA,IAAI;AACf;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC1D,OAAOtB,WAAW,CAACmB,MAAM,EAAEH,mBAAmB,CAACI,SAAS,CAAC,EAAElB,WAAW,CAACmB,OAAO,EAAED,SAAS,KAAK,aAAa,CAAC,EAAEE,OAAO,CAAC;AAC1H;AACA,SAASC,eAAeA,CAACC,GAAG,EAAEJ,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACvD,OAAOrB,WAAW,CAACuB,GAAG,EAAER,mBAAmB,CAACI,SAAS,CAAC,EAAEC,OAAO,IAAInB,WAAW,CAACmB,OAAO,EAAED,SAAS,KAAK,aAAa,CAAC,EAAEE,OAAO,CAAC;AAClI;AAEA,SAASJ,eAAe,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}