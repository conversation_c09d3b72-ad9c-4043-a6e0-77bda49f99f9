[{"D:\\Projects\\cable_project\\customer-management\\frontend\\src\\index.js": "1", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\App.jsx": "2", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\PaymentTracker.jsx": "3", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\PlanManager.jsx": "4", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\CustomerList.jsx": "5", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\pages\\Reports.jsx": "6", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\pages\\Dashboard.jsx": "7", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\Navbar.jsx": "8", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\pages\\Login.jsx": "9", "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\config\\api.js": "10"}, {"size": 228, "mtime": 1749155140958, "results": "11", "hashOfConfig": "12"}, {"size": 1451, "mtime": 1749160559664, "results": "13", "hashOfConfig": "12"}, {"size": 10989, "mtime": 1749250040725, "results": "14", "hashOfConfig": "12"}, {"size": 9283, "mtime": 1749160532930, "results": "15", "hashOfConfig": "12"}, {"size": 11563, "mtime": 1749160268591, "results": "16", "hashOfConfig": "12"}, {"size": 3021, "mtime": 1749160493531, "results": "17", "hashOfConfig": "12"}, {"size": 2561, "mtime": 1749160300644, "results": "18", "hashOfConfig": "12"}, {"size": 3738, "mtime": 1749160573666, "results": "19", "hashOfConfig": "12"}, {"size": 3593, "mtime": 1749160330333, "results": "20", "hashOfConfig": "12"}, {"size": 815, "mtime": 1749160230720, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1t38nmn", {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\index.js", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\App.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\PaymentTracker.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\PlanManager.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\CustomerList.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\pages\\Reports.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\pages\\Dashboard.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\components\\Navbar.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\pages\\Login.jsx", [], "D:\\Projects\\cable_project\\customer-management\\frontend\\src\\config\\api.js", []]