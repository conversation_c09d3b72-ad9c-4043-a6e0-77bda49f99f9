{"ast": null, "code": "export { Animation } from './Animation.es.js';\nexport { getEasingFunction } from './utils/easing.es.js';", "map": {"version": 3, "names": ["Animation", "getEasingFunction"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/animation/dist/index.es.js"], "sourcesContent": ["export { Animation } from './Animation.es.js';\nexport { getEasingFunction } from './utils/easing.es.js';\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,iBAAiB,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}