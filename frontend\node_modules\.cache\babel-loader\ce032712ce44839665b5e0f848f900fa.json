{"ast": null, "code": "'use strict';\n\nfunction hasChanged(a, b) {\n  if (typeof a !== typeof b) return true;\n  if (Array.isArray(a) && Array.isArray(b)) return !shallowCompare(a, b);\n  return a !== b;\n}\nfunction shallowCompare(next, prev) {\n  const prevLength = prev.length;\n  if (prevLength !== next.length) return false;\n  for (let i = 0; i < prevLength; i++) {\n    if (prev[i] !== next[i]) return false;\n  }\n  return true;\n}\nexports.hasChanged = hasChanged;\nexports.shallowCompare = shallowCompare;", "map": {"version": 3, "names": ["has<PERSON><PERSON>ed", "a", "b", "Array", "isArray", "shallowCompare", "next", "prev", "prevLength", "length", "i", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/utils/has-changed.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction hasChanged(a, b) {\n    if (typeof a !== typeof b)\n        return true;\n    if (Array.isArray(a) && Array.isArray(b))\n        return !shallowCompare(a, b);\n    return a !== b;\n}\nfunction shallowCompare(next, prev) {\n    const prevLength = prev.length;\n    if (prevLength !== next.length)\n        return false;\n    for (let i = 0; i < prevLength; i++) {\n        if (prev[i] !== next[i])\n            return false;\n    }\n    return true;\n}\n\nexports.hasChanged = hasChanged;\nexports.shallowCompare = shallowCompare;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAI,OAAOD,CAAC,KAAK,OAAOC,CAAC,EACrB,OAAO,IAAI;EACf,IAAIC,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,IAAIE,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EACpC,OAAO,CAACG,cAAc,CAACJ,CAAC,EAAEC,CAAC,CAAC;EAChC,OAAOD,CAAC,KAAKC,CAAC;AAClB;AACA,SAASG,cAAcA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAChC,MAAMC,UAAU,GAAGD,IAAI,CAACE,MAAM;EAC9B,IAAID,UAAU,KAAKF,IAAI,CAACG,MAAM,EAC1B,OAAO,KAAK;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACjC,IAAIH,IAAI,CAACG,CAAC,CAAC,KAAKJ,IAAI,CAACI,CAAC,CAAC,EACnB,OAAO,KAAK;EACpB;EACA,OAAO,IAAI;AACf;AAEAC,OAAO,CAACX,UAAU,GAAGA,UAAU;AAC/BW,OAAO,CAACN,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script"}