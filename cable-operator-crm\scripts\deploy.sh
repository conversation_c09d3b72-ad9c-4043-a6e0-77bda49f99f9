#!/bin/bash

# Cable Operator CRM - Production Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="cable-operator-crm"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p logs backups docker/ssl docker/nginx docker/postgres
    success "Directories created"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if required files exist
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
    fi
    
    success "Prerequisites check passed"
}

# Setup environment files
setup_environment() {
    log "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        log "Creating backend .env file from example..."
        cp backend/.env.example backend/.env
        warning "Please update backend/.env with your production settings"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        log "Creating frontend .env file from example..."
        cp frontend/.env.example frontend/.env
        warning "Please update frontend/.env with your production settings"
    fi
    
    success "Environment files setup complete"
}

# Generate SSL certificates (self-signed for development)
generate_ssl() {
    log "Generating SSL certificates..."
    
    if [ ! -f "docker/ssl/cert.pem" ]; then
        openssl req -x509 -newkey rsa:4096 -keyout docker/ssl/key.pem -out docker/ssl/cert.pem -days 365 -nodes \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        success "SSL certificates generated"
    else
        log "SSL certificates already exist"
    fi
}

# Create nginx configuration
create_nginx_config() {
    log "Creating nginx configuration..."
    
    cat > docker/nginx/default.conf << 'EOF'
upstream backend {
    server backend:8000;
}

upstream frontend {
    server frontend:80;
}

server {
    listen 80;
    server_name localhost;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name localhost;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Frontend
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # Static files
    location /static/ {
        alias /var/www/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    success "Nginx configuration created"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    if docker-compose ps | grep -q "Up"; then
        BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
        docker-compose exec -T postgres pg_dump -U cable_user cable_operator_db > "$BACKUP_FILE"
        success "Database backup created: $BACKUP_FILE"
    else
        log "No running containers found, skipping backup"
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Pull latest images
    log "Pulling latest images..."
    docker-compose pull
    
    # Build custom images
    log "Building custom images..."
    docker-compose build --no-cache
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose down
    
    # Start new containers
    log "Starting new containers..."
    docker-compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Run database migrations
    log "Running database migrations..."
    docker-compose exec backend alembic upgrade head
    
    # Create default admin user if not exists
    log "Creating default admin user..."
    docker-compose exec backend python -c "
from app.core.database import SessionLocal
from app.models.user import User
from app.models.company import Company
from app.core.security import SecurityManager

db = SessionLocal()

# Check if admin user exists
admin_user = db.query(User).filter(User.email == '<EMAIL>').first()
if not admin_user:
    # Create default company
    company = Company(
        name='Default Company',
        email='<EMAIL>',
        phone='******-123-4567',
        address_line1='123 Main St',
        city='Anytown',
        state='State',
        postal_code='12345',
        country='United States'
    )
    db.add(company)
    db.flush()
    
    # Create admin user
    admin_user = User(
        first_name='System',
        last_name='Administrator',
        email='<EMAIL>',
        password_hash=SecurityManager.get_password_hash('admin123'),
        role='super_admin',
        company_id=company.id,
        is_active=True,
        is_verified=True
    )
    db.add(admin_user)
    db.commit()
    print('Default admin user created: <EMAIL> / admin123')
else:
    print('Admin user already exists')

db.close()
"
    
    success "Deployment completed successfully!"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check if all services are running
    if docker-compose ps | grep -q "Exit"; then
        error "Some services failed to start"
    fi
    
    # Check backend health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        success "Backend is healthy"
    else
        warning "Backend health check failed"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        success "Frontend is accessible"
    else
        warning "Frontend health check failed"
    fi
    
    success "Health check completed"
}

# Show status
show_status() {
    log "Current system status:"
    docker-compose ps
    
    echo ""
    log "Access URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8000"
    echo "  API Documentation: http://localhost:8000/docs"
    echo "  Admin Login: <EMAIL> / admin123"
    
    echo ""
    log "Useful commands:"
    echo "  View logs: docker-compose logs -f"
    echo "  Stop services: docker-compose down"
    echo "  Restart services: docker-compose restart"
    echo "  Update: ./scripts/deploy.sh"
}

# Main execution
main() {
    log "Starting Cable Operator CRM deployment..."
    
    create_directories
    check_prerequisites
    setup_environment
    generate_ssl
    create_nginx_config
    
    if [ "$1" = "--backup" ]; then
        backup_data
    fi
    
    deploy
    health_check
    show_status
    
    success "Deployment completed successfully!"
    warning "Please update the default admin password after first login"
}

# Handle script arguments
case "$1" in
    "backup")
        backup_data
        ;;
    "health")
        health_check
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "stop")
        docker-compose down
        ;;
    "restart")
        docker-compose restart
        ;;
    *)
        main "$@"
        ;;
esac
