{"ast": null, "code": "'use strict';\n\nvar heyListen = require('hey-listen');\nvar animateStyle = require('./animate-style.cjs.js');\nvar options = require('./utils/options.cjs.js');\nvar resolveElements = require('../utils/resolve-elements.cjs.js');\nvar controls = require('./utils/controls.cjs.js');\nvar stagger = require('../utils/stagger.cjs.js');\nfunction createAnimate(AnimatePolyfill) {\n  return function animate(elements, keyframes, options$1 = {}) {\n    elements = resolveElements.resolveElements(elements);\n    const numElements = elements.length;\n    heyListen.invariant(Boolean(numElements), \"No valid element provided.\");\n    heyListen.invariant(Boolean(keyframes), \"No keyframes defined.\");\n    /**\n     * Create and start new animations\n     */\n    const animationFactories = [];\n    for (let i = 0; i < numElements; i++) {\n      const element = elements[i];\n      for (const key in keyframes) {\n        const valueOptions = options.getOptions(options$1, key);\n        valueOptions.delay = stagger.resolveOption(valueOptions.delay, i, numElements);\n        const animation = animateStyle.animateStyle(element, key, keyframes[key], valueOptions, AnimatePolyfill);\n        animationFactories.push(animation);\n      }\n    }\n    return controls.withControls(animationFactories, options$1,\n    /**\n     * TODO:\n     * If easing is set to spring or glide, duration will be dynamically\n     * generated. Ideally we would dynamically generate this from\n     * animation.effect.getComputedTiming().duration but this isn't\n     * supported in iOS13 or our number polyfill. Perhaps it's possible\n     * to Proxy animations returned from animateStyle that has duration\n     * as a getter.\n     */\n    options$1.duration);\n  };\n}\nexports.createAnimate = createAnimate;", "map": {"version": 3, "names": ["heyListen", "require", "animateStyle", "options", "resolveElements", "controls", "stagger", "createAnimate", "AnimatePolyfill", "animate", "elements", "keyframes", "options$1", "numElements", "length", "invariant", "Boolean", "animationFactories", "i", "element", "key", "valueOptions", "getOptions", "delay", "resolveOption", "animation", "push", "withControls", "duration", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/create-animate.cjs.js"], "sourcesContent": ["'use strict';\n\nvar heyListen = require('hey-listen');\nvar animateStyle = require('./animate-style.cjs.js');\nvar options = require('./utils/options.cjs.js');\nvar resolveElements = require('../utils/resolve-elements.cjs.js');\nvar controls = require('./utils/controls.cjs.js');\nvar stagger = require('../utils/stagger.cjs.js');\n\nfunction createAnimate(AnimatePolyfill) {\n    return function animate(elements, keyframes, options$1 = {}) {\n        elements = resolveElements.resolveElements(elements);\n        const numElements = elements.length;\n        heyListen.invariant(Boolean(numElements), \"No valid element provided.\");\n        heyListen.invariant(Boolean(keyframes), \"No keyframes defined.\");\n        /**\n         * Create and start new animations\n         */\n        const animationFactories = [];\n        for (let i = 0; i < numElements; i++) {\n            const element = elements[i];\n            for (const key in keyframes) {\n                const valueOptions = options.getOptions(options$1, key);\n                valueOptions.delay = stagger.resolveOption(valueOptions.delay, i, numElements);\n                const animation = animateStyle.animateStyle(element, key, keyframes[key], valueOptions, AnimatePolyfill);\n                animationFactories.push(animation);\n            }\n        }\n        return controls.withControls(animationFactories, options$1, \n        /**\n         * TODO:\n         * If easing is set to spring or glide, duration will be dynamically\n         * generated. Ideally we would dynamically generate this from\n         * animation.effect.getComputedTiming().duration but this isn't\n         * supported in iOS13 or our number polyfill. Perhaps it's possible\n         * to Proxy animations returned from animateStyle that has duration\n         * as a getter.\n         */\n        options$1.duration);\n    };\n}\n\nexports.createAnimate = createAnimate;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,YAAY,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACpD,IAAIE,OAAO,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAC/C,IAAIG,eAAe,GAAGH,OAAO,CAAC,kCAAkC,CAAC;AACjE,IAAII,QAAQ,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AACjD,IAAIK,OAAO,GAAGL,OAAO,CAAC,yBAAyB,CAAC;AAEhD,SAASM,aAAaA,CAACC,eAAe,EAAE;EACpC,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,GAAG,CAAC,CAAC,EAAE;IACzDF,QAAQ,GAAGN,eAAe,CAACA,eAAe,CAACM,QAAQ,CAAC;IACpD,MAAMG,WAAW,GAAGH,QAAQ,CAACI,MAAM;IACnCd,SAAS,CAACe,SAAS,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE,4BAA4B,CAAC;IACvEb,SAAS,CAACe,SAAS,CAACC,OAAO,CAACL,SAAS,CAAC,EAAE,uBAAuB,CAAC;IAChE;AACR;AACA;IACQ,MAAMM,kBAAkB,GAAG,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,EAAEK,CAAC,EAAE,EAAE;MAClC,MAAMC,OAAO,GAAGT,QAAQ,CAACQ,CAAC,CAAC;MAC3B,KAAK,MAAME,GAAG,IAAIT,SAAS,EAAE;QACzB,MAAMU,YAAY,GAAGlB,OAAO,CAACmB,UAAU,CAACV,SAAS,EAAEQ,GAAG,CAAC;QACvDC,YAAY,CAACE,KAAK,GAAGjB,OAAO,CAACkB,aAAa,CAACH,YAAY,CAACE,KAAK,EAAEL,CAAC,EAAEL,WAAW,CAAC;QAC9E,MAAMY,SAAS,GAAGvB,YAAY,CAACA,YAAY,CAACiB,OAAO,EAAEC,GAAG,EAAET,SAAS,CAACS,GAAG,CAAC,EAAEC,YAAY,EAAEb,eAAe,CAAC;QACxGS,kBAAkB,CAACS,IAAI,CAACD,SAAS,CAAC;MACtC;IACJ;IACA,OAAOpB,QAAQ,CAACsB,YAAY,CAACV,kBAAkB,EAAEL,SAAS;IAC1D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQA,SAAS,CAACgB,QAAQ,CAAC;EACvB,CAAC;AACL;AAEAC,OAAO,CAACtB,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}