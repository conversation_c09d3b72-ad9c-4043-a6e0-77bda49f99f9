{"ast": null, "code": "import { delay } from '../utils/delay.mjs';\nfunction createInstantAnimation({\n  keyframes,\n  elapsed,\n  onUpdate,\n  onComplete\n}) {\n  const setValue = () => {\n    onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n    onComplete && onComplete();\n    return () => {};\n  };\n  return elapsed ? delay(setValue, -elapsed) : setValue();\n}\nexport { createInstantAnimation };", "map": {"version": 3, "names": ["delay", "createInstantAnimation", "keyframes", "elapsed", "onUpdate", "onComplete", "setValue", "length"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/create-instant-animation.mjs"], "sourcesContent": ["import { delay } from '../utils/delay.mjs';\n\nfunction createInstantAnimation({ keyframes, elapsed, onUpdate, onComplete, }) {\n    const setValue = () => {\n        onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n        onComplete && onComplete();\n        return () => { };\n    };\n    return elapsed ? delay(setValue, -elapsed) : setValue();\n}\n\nexport { createInstantAnimation };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAE1C,SAASC,sBAAsBA,CAAC;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ;EAAEC;AAAY,CAAC,EAAE;EAC3E,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnBF,QAAQ,IAAIA,QAAQ,CAACF,SAAS,CAACA,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC;IACrDF,UAAU,IAAIA,UAAU,CAAC,CAAC;IAC1B,OAAO,MAAM,CAAE,CAAC;EACpB,CAAC;EACD,OAAOF,OAAO,GAAGH,KAAK,CAACM,QAAQ,EAAE,CAACH,OAAO,CAAC,GAAGG,QAAQ,CAAC,CAAC;AAC3D;AAEA,SAASL,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}