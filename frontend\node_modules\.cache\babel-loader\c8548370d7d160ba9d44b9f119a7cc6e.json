{"ast": null, "code": "import { useContext, useRef } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useVisualElementContext } from '../../context/MotionContext/index.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nfunction useVisualElement(Component, visualState, props, createVisualElement) {\n  const parent = useVisualElementContext();\n  const lazyContext = useContext(LazyContext);\n  const presenceContext = useContext(PresenceContext);\n  const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n  const visualElementRef = useRef();\n  /**\n   * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n   */\n  createVisualElement = createVisualElement || lazyContext.renderer;\n  if (!visualElementRef.current && createVisualElement) {\n    visualElementRef.current = createVisualElement(Component, {\n      visualState,\n      parent,\n      props,\n      presenceId: presenceContext ? presenceContext.id : undefined,\n      blockInitialAnimation: presenceContext ? presenceContext.initial === false : false,\n      reducedMotionConfig\n    });\n  }\n  const visualElement = visualElementRef.current;\n  useIsomorphicLayoutEffect(() => {\n    visualElement && visualElement.render();\n  });\n  /**\n   * If we have optimised appear animations to handoff from, trigger animateChanges\n   * from a synchronous useLayoutEffect to ensure there's no flash of incorrectly\n   * styled component in the event of a hydration error.\n   */\n  useIsomorphicLayoutEffect(() => {\n    if (visualElement && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n  });\n  useIsomorphicLayoutEffect(() => () => visualElement && visualElement.notify(\"Unmount\"), []);\n  return visualElement;\n}\nexport { useVisualElement };", "map": {"version": 3, "names": ["useContext", "useRef", "PresenceContext", "useVisualElementContext", "useIsomorphicLayoutEffect", "LazyContext", "MotionConfigContext", "useVisualElement", "Component", "visualState", "props", "createVisualElement", "parent", "lazyContext", "presenceContext", "reducedMotionConfig", "reducedMotion", "visualElementRef", "renderer", "current", "presenceId", "id", "undefined", "blockInitialAnimation", "initial", "visualElement", "render", "animationState", "animateChanges", "notify"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs"], "sourcesContent": ["import { useContext, useRef } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useVisualElementContext } from '../../context/MotionContext/index.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\n\nfunction useVisualElement(Component, visualState, props, createVisualElement) {\n    const parent = useVisualElementContext();\n    const lazyContext = useContext(LazyContext);\n    const presenceContext = useContext(PresenceContext);\n    const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n    const visualElementRef = useRef();\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceId: presenceContext ? presenceContext.id : undefined,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    useIsomorphicLayoutEffect(() => {\n        visualElement && visualElement.render();\n    });\n    /**\n     * If we have optimised appear animations to handoff from, trigger animateChanges\n     * from a synchronous useLayoutEffect to ensure there's no flash of incorrectly\n     * styled component in the event of a hydration error.\n     */\n    useIsomorphicLayoutEffect(() => {\n        if (visualElement && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    useIsomorphicLayoutEffect(() => () => visualElement && visualElement.notify(\"Unmount\"), []);\n    return visualElement;\n}\n\nexport { useVisualElement };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC1C,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uCAAuC;AAE3E,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,mBAAmB,EAAE;EAC1E,MAAMC,MAAM,GAAGT,uBAAuB,CAAC,CAAC;EACxC,MAAMU,WAAW,GAAGb,UAAU,CAACK,WAAW,CAAC;EAC3C,MAAMS,eAAe,GAAGd,UAAU,CAACE,eAAe,CAAC;EACnD,MAAMa,mBAAmB,GAAGf,UAAU,CAACM,mBAAmB,CAAC,CAACU,aAAa;EACzE,MAAMC,gBAAgB,GAAGhB,MAAM,CAAC,CAAC;EACjC;AACJ;AACA;EACIU,mBAAmB,GAAGA,mBAAmB,IAAIE,WAAW,CAACK,QAAQ;EACjE,IAAI,CAACD,gBAAgB,CAACE,OAAO,IAAIR,mBAAmB,EAAE;IAClDM,gBAAgB,CAACE,OAAO,GAAGR,mBAAmB,CAACH,SAAS,EAAE;MACtDC,WAAW;MACXG,MAAM;MACNF,KAAK;MACLU,UAAU,EAAEN,eAAe,GAAGA,eAAe,CAACO,EAAE,GAAGC,SAAS;MAC5DC,qBAAqB,EAAET,eAAe,GAChCA,eAAe,CAACU,OAAO,KAAK,KAAK,GACjC,KAAK;MACXT;IACJ,CAAC,CAAC;EACN;EACA,MAAMU,aAAa,GAAGR,gBAAgB,CAACE,OAAO;EAC9Cf,yBAAyB,CAAC,MAAM;IAC5BqB,aAAa,IAAIA,aAAa,CAACC,MAAM,CAAC,CAAC;EAC3C,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;EACItB,yBAAyB,CAAC,MAAM;IAC5B,IAAIqB,aAAa,IAAIA,aAAa,CAACE,cAAc,EAAE;MAC/CF,aAAa,CAACE,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;EACJ,CAAC,CAAC;EACFxB,yBAAyB,CAAC,MAAM,MAAMqB,aAAa,IAAIA,aAAa,CAACI,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;EAC3F,OAAOJ,aAAa;AACxB;AAEA,SAASlB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}