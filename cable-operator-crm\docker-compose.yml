version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cable_crm_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cable_operator_db
      POSTGRES_USER: cable_user
      POSTGRES_PASSWORD: cable_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - cable_crm_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cable_user -d cable_operator_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cable_crm_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password_2024
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cable_crm_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: cable_crm_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=*********************************************************/cable_operator_db
      - REDIS_URL=redis://:redis_password_2024@redis:6379/0
      - SECRET_KEY=your-super-secret-production-key-change-this
      - ENVIRONMENT=production
      - DEBUG=false
      - ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=your-app-password
      - UPLOAD_DIR=/app/uploads
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
      - backend_backups:/app/backups
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cable_crm_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
      args:
        - REACT_APP_API_URL=http://localhost:8000/api/v1
        - REACT_APP_ENVIRONMENT=production
    container_name: cable_crm_frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - cable_crm_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cable_crm_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./docker/ssl:/etc/nginx/ssl
      - backend_uploads:/var/www/uploads
    depends_on:
      - frontend
      - backend
    networks:
      - cable_crm_network

  # Background Task Worker (Celery)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: cable_crm_worker
    restart: unless-stopped
    command: celery -A app.worker worker --loglevel=info
    environment:
      - DATABASE_URL=*********************************************************/cable_operator_db
      - REDIS_URL=redis://:redis_password_2024@redis:6379/0
      - SECRET_KEY=your-super-secret-production-key-change-this
      - ENVIRONMENT=production
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
      - backend_backups:/app/backups
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cable_crm_network

  # Task Scheduler (Celery Beat)
  scheduler:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: cable_crm_scheduler
    restart: unless-stopped
    command: celery -A app.worker beat --loglevel=info
    environment:
      - DATABASE_URL=*********************************************************/cable_operator_db
      - REDIS_URL=redis://:redis_password_2024@redis:6379/0
      - SECRET_KEY=your-super-secret-production-key-change-this
      - ENVIRONMENT=production
    volumes:
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cable_crm_network

  # Monitoring - Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: cable_crm_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cable_crm_network
    profiles:
      - monitoring

  # Monitoring - Grafana (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: cable_crm_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - cable_crm_network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  backend_backups:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cable_crm_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
