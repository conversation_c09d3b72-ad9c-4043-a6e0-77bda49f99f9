{"ast": null, "code": "'use strict';\n\nvar generators = require('@motionone/generators');\nvar utils = require('@motionone/utils');\nvar getUnit = require('../animate/utils/get-unit.cjs.js');\nvar transforms = require('../animate/utils/transforms.cjs.js');\nvar getStyleName = require('../animate/utils/get-style-name.cjs.js');\nfunction canGenerate(value) {\n  return utils.isNumber(value) && !isNaN(value);\n}\nfunction getAsNumber(value) {\n  return utils.isString(value) ? parseFloat(value) : value;\n}\nfunction createGeneratorEasing(createGenerator) {\n  const keyframesCache = new WeakMap();\n  return (options = {}) => {\n    const generatorCache = new Map();\n    const getGenerator = (from = 0, to = 100, velocity = 0, isScale = false) => {\n      const key = `${from}-${to}-${velocity}-${isScale}`;\n      if (!generatorCache.has(key)) {\n        generatorCache.set(key, createGenerator(Object.assign({\n          from,\n          to,\n          velocity\n        }, options)));\n      }\n      return generatorCache.get(key);\n    };\n    const getKeyframes = (generator, toUnit) => {\n      if (!keyframesCache.has(generator)) {\n        keyframesCache.set(generator, generators.pregenerateKeyframes(generator, toUnit));\n      }\n      return keyframesCache.get(generator);\n    };\n    return {\n      createAnimation: (keyframes, shouldGenerate = true, getOrigin, name, motionValue) => {\n        let settings;\n        let origin;\n        let target;\n        let velocity = 0;\n        let toUnit = utils.noopReturn;\n        const numKeyframes = keyframes.length;\n        /**\n         * If we should generate an animation for this value, run some preperation\n         * like resolving target/origin, finding a unit (if any) and determine if\n         * it is actually possible to generate.\n         */\n        if (shouldGenerate) {\n          toUnit = getUnit.getUnitConverter(keyframes, name ? transforms.transformDefinitions.get(getStyleName.getStyleName(name)) : undefined);\n          const targetDefinition = keyframes[numKeyframes - 1];\n          target = getAsNumber(targetDefinition);\n          if (numKeyframes > 1 && keyframes[0] !== null) {\n            /**\n             * If we have multiple keyframes, take the initial keyframe as the origin.\n             */\n            origin = getAsNumber(keyframes[0]);\n          } else {\n            const prevGenerator = motionValue === null || motionValue === void 0 ? void 0 : motionValue.generator;\n            /**\n             * If we have an existing generator for this value we can use it to resolve\n             * the animation's current value and velocity.\n             */\n            if (prevGenerator) {\n              /**\n               * If we have a generator for this value we can use it to resolve\n               * the animations's current value and velocity.\n               */\n              const {\n                animation,\n                generatorStartTime\n              } = motionValue;\n              const startTime = (animation === null || animation === void 0 ? void 0 : animation.startTime) || generatorStartTime || 0;\n              const currentTime = (animation === null || animation === void 0 ? void 0 : animation.currentTime) || performance.now() - startTime;\n              const prevGeneratorCurrent = prevGenerator(currentTime).current;\n              origin = prevGeneratorCurrent;\n              velocity = generators.calcGeneratorVelocity(t => prevGenerator(t).current, currentTime, prevGeneratorCurrent);\n            } else if (getOrigin) {\n              /**\n               * As a last resort, read the origin from the DOM.\n               */\n              origin = getAsNumber(getOrigin());\n            }\n          }\n        }\n        /**\n         * If we've determined it is possible to generate an animation, do so.\n         */\n        if (canGenerate(origin) && canGenerate(target)) {\n          const generator = getGenerator(origin, target, velocity, name === null || name === void 0 ? void 0 : name.includes(\"scale\"));\n          settings = Object.assign(Object.assign({}, getKeyframes(generator, toUnit)), {\n            easing: \"linear\"\n          });\n          // TODO Add test for this\n          if (motionValue) {\n            motionValue.generator = generator;\n            motionValue.generatorStartTime = performance.now();\n          }\n        }\n        /**\n         * If by now we haven't generated a set of keyframes, create a generic generator\n         * based on the provided props that animates from 0-100 to fetch a rough\n         * \"overshootDuration\" - the moment when the generator first hits the animation target.\n         * Then return animation settings that will run a normal animation for that duration.\n         */\n        if (!settings) {\n          const keyframesMetadata = getKeyframes(getGenerator(0, 100));\n          settings = {\n            easing: \"ease\",\n            duration: keyframesMetadata.overshootDuration\n          };\n        }\n        return settings;\n      }\n    };\n  };\n}\nexports.createGeneratorEasing = createGeneratorEasing;", "map": {"version": 3, "names": ["generators", "require", "utils", "getUnit", "transforms", "getStyleName", "canGenerate", "value", "isNumber", "isNaN", "getAsNumber", "isString", "parseFloat", "createGeneratorEasing", "createGenerator", "keyframesCache", "WeakMap", "options", "generatorCache", "Map", "getGenerator", "from", "to", "velocity", "isScale", "key", "has", "set", "Object", "assign", "get", "getKeyframes", "generator", "toUnit", "pregenerateKeyframes", "createAnimation", "keyframes", "shouldGenerate", "<PERSON><PERSON><PERSON><PERSON>", "name", "motionValue", "settings", "origin", "target", "noopReturn", "numKeyframes", "length", "getUnitConverter", "transformDefinitions", "undefined", "targetDefinition", "prevGenerator", "animation", "generatorStartTime", "startTime", "currentTime", "performance", "now", "prevGeneratorCurrent", "current", "calcGeneratorVelocity", "t", "includes", "easing", "keyframesMetadata", "duration", "overshootDuration", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/easing/create-generator-easing.cjs.js"], "sourcesContent": ["'use strict';\n\nvar generators = require('@motionone/generators');\nvar utils = require('@motionone/utils');\nvar getUnit = require('../animate/utils/get-unit.cjs.js');\nvar transforms = require('../animate/utils/transforms.cjs.js');\nvar getStyleName = require('../animate/utils/get-style-name.cjs.js');\n\nfunction canGenerate(value) {\n    return utils.isNumber(value) && !isNaN(value);\n}\nfunction getAsNumber(value) {\n    return utils.isString(value) ? parseFloat(value) : value;\n}\nfunction createGeneratorEasing(createGenerator) {\n    const keyframesCache = new WeakMap();\n    return (options = {}) => {\n        const generatorCache = new Map();\n        const getGenerator = (from = 0, to = 100, velocity = 0, isScale = false) => {\n            const key = `${from}-${to}-${velocity}-${isScale}`;\n            if (!generatorCache.has(key)) {\n                generatorCache.set(key, createGenerator(Object.assign({ from,\n                    to,\n                    velocity }, options)));\n            }\n            return generatorCache.get(key);\n        };\n        const getKeyframes = (generator, toUnit) => {\n            if (!keyframesCache.has(generator)) {\n                keyframesCache.set(generator, generators.pregenerateKeyframes(generator, toUnit));\n            }\n            return keyframesCache.get(generator);\n        };\n        return {\n            createAnimation: (keyframes, shouldGenerate = true, getOrigin, name, motionValue) => {\n                let settings;\n                let origin;\n                let target;\n                let velocity = 0;\n                let toUnit = utils.noopReturn;\n                const numKeyframes = keyframes.length;\n                /**\n                 * If we should generate an animation for this value, run some preperation\n                 * like resolving target/origin, finding a unit (if any) and determine if\n                 * it is actually possible to generate.\n                 */\n                if (shouldGenerate) {\n                    toUnit = getUnit.getUnitConverter(keyframes, name ? transforms.transformDefinitions.get(getStyleName.getStyleName(name)) : undefined);\n                    const targetDefinition = keyframes[numKeyframes - 1];\n                    target = getAsNumber(targetDefinition);\n                    if (numKeyframes > 1 && keyframes[0] !== null) {\n                        /**\n                         * If we have multiple keyframes, take the initial keyframe as the origin.\n                         */\n                        origin = getAsNumber(keyframes[0]);\n                    }\n                    else {\n                        const prevGenerator = motionValue === null || motionValue === void 0 ? void 0 : motionValue.generator;\n                        /**\n                         * If we have an existing generator for this value we can use it to resolve\n                         * the animation's current value and velocity.\n                         */\n                        if (prevGenerator) {\n                            /**\n                             * If we have a generator for this value we can use it to resolve\n                             * the animations's current value and velocity.\n                             */\n                            const { animation, generatorStartTime } = motionValue;\n                            const startTime = (animation === null || animation === void 0 ? void 0 : animation.startTime) || generatorStartTime || 0;\n                            const currentTime = (animation === null || animation === void 0 ? void 0 : animation.currentTime) || performance.now() - startTime;\n                            const prevGeneratorCurrent = prevGenerator(currentTime).current;\n                            origin = prevGeneratorCurrent;\n                            velocity = generators.calcGeneratorVelocity((t) => prevGenerator(t).current, currentTime, prevGeneratorCurrent);\n                        }\n                        else if (getOrigin) {\n                            /**\n                             * As a last resort, read the origin from the DOM.\n                             */\n                            origin = getAsNumber(getOrigin());\n                        }\n                    }\n                }\n                /**\n                 * If we've determined it is possible to generate an animation, do so.\n                 */\n                if (canGenerate(origin) && canGenerate(target)) {\n                    const generator = getGenerator(origin, target, velocity, name === null || name === void 0 ? void 0 : name.includes(\"scale\"));\n                    settings = Object.assign(Object.assign({}, getKeyframes(generator, toUnit)), { easing: \"linear\" });\n                    // TODO Add test for this\n                    if (motionValue) {\n                        motionValue.generator = generator;\n                        motionValue.generatorStartTime = performance.now();\n                    }\n                }\n                /**\n                 * If by now we haven't generated a set of keyframes, create a generic generator\n                 * based on the provided props that animates from 0-100 to fetch a rough\n                 * \"overshootDuration\" - the moment when the generator first hits the animation target.\n                 * Then return animation settings that will run a normal animation for that duration.\n                 */\n                if (!settings) {\n                    const keyframesMetadata = getKeyframes(getGenerator(0, 100));\n                    settings = {\n                        easing: \"ease\",\n                        duration: keyframesMetadata.overshootDuration,\n                    };\n                }\n                return settings;\n            },\n        };\n    };\n}\n\nexports.createGeneratorEasing = createGeneratorEasing;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AACjD,IAAIC,KAAK,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIE,OAAO,GAAGF,OAAO,CAAC,kCAAkC,CAAC;AACzD,IAAIG,UAAU,GAAGH,OAAO,CAAC,oCAAoC,CAAC;AAC9D,IAAII,YAAY,GAAGJ,OAAO,CAAC,wCAAwC,CAAC;AAEpE,SAASK,WAAWA,CAACC,KAAK,EAAE;EACxB,OAAOL,KAAK,CAACM,QAAQ,CAACD,KAAK,CAAC,IAAI,CAACE,KAAK,CAACF,KAAK,CAAC;AACjD;AACA,SAASG,WAAWA,CAACH,KAAK,EAAE;EACxB,OAAOL,KAAK,CAACS,QAAQ,CAACJ,KAAK,CAAC,GAAGK,UAAU,CAACL,KAAK,CAAC,GAAGA,KAAK;AAC5D;AACA,SAASM,qBAAqBA,CAACC,eAAe,EAAE;EAC5C,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;EACpC,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;IACrB,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChC,MAAMC,YAAY,GAAGA,CAACC,IAAI,GAAG,CAAC,EAAEC,EAAE,GAAG,GAAG,EAAEC,QAAQ,GAAG,CAAC,EAAEC,OAAO,GAAG,KAAK,KAAK;MACxE,MAAMC,GAAG,GAAG,GAAGJ,IAAI,IAAIC,EAAE,IAAIC,QAAQ,IAAIC,OAAO,EAAE;MAClD,IAAI,CAACN,cAAc,CAACQ,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BP,cAAc,CAACS,GAAG,CAACF,GAAG,EAAEX,eAAe,CAACc,MAAM,CAACC,MAAM,CAAC;UAAER,IAAI;UACxDC,EAAE;UACFC;QAAS,CAAC,EAAEN,OAAO,CAAC,CAAC,CAAC;MAC9B;MACA,OAAOC,cAAc,CAACY,GAAG,CAACL,GAAG,CAAC;IAClC,CAAC;IACD,MAAMM,YAAY,GAAGA,CAACC,SAAS,EAAEC,MAAM,KAAK;MACxC,IAAI,CAAClB,cAAc,CAACW,GAAG,CAACM,SAAS,CAAC,EAAE;QAChCjB,cAAc,CAACY,GAAG,CAACK,SAAS,EAAEhC,UAAU,CAACkC,oBAAoB,CAACF,SAAS,EAAEC,MAAM,CAAC,CAAC;MACrF;MACA,OAAOlB,cAAc,CAACe,GAAG,CAACE,SAAS,CAAC;IACxC,CAAC;IACD,OAAO;MACHG,eAAe,EAAEA,CAACC,SAAS,EAAEC,cAAc,GAAG,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,WAAW,KAAK;QACjF,IAAIC,QAAQ;QACZ,IAAIC,MAAM;QACV,IAAIC,MAAM;QACV,IAAIpB,QAAQ,GAAG,CAAC;QAChB,IAAIU,MAAM,GAAG/B,KAAK,CAAC0C,UAAU;QAC7B,MAAMC,YAAY,GAAGT,SAAS,CAACU,MAAM;QACrC;AAChB;AACA;AACA;AACA;QACgB,IAAIT,cAAc,EAAE;UAChBJ,MAAM,GAAG9B,OAAO,CAAC4C,gBAAgB,CAACX,SAAS,EAAEG,IAAI,GAAGnC,UAAU,CAAC4C,oBAAoB,CAAClB,GAAG,CAACzB,YAAY,CAACA,YAAY,CAACkC,IAAI,CAAC,CAAC,GAAGU,SAAS,CAAC;UACrI,MAAMC,gBAAgB,GAAGd,SAAS,CAACS,YAAY,GAAG,CAAC,CAAC;UACpDF,MAAM,GAAGjC,WAAW,CAACwC,gBAAgB,CAAC;UACtC,IAAIL,YAAY,GAAG,CAAC,IAAIT,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC3C;AACxB;AACA;YACwBM,MAAM,GAAGhC,WAAW,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,MACI;YACD,MAAMe,aAAa,GAAGX,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACR,SAAS;YACrG;AACxB;AACA;AACA;YACwB,IAAImB,aAAa,EAAE;cACf;AAC5B;AACA;AACA;cAC4B,MAAM;gBAAEC,SAAS;gBAAEC;cAAmB,CAAC,GAAGb,WAAW;cACrD,MAAMc,SAAS,GAAG,CAACF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,SAAS,KAAKD,kBAAkB,IAAI,CAAC;cACxH,MAAME,WAAW,GAAG,CAACH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,WAAW,KAAKC,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGH,SAAS;cAClI,MAAMI,oBAAoB,GAAGP,aAAa,CAACI,WAAW,CAAC,CAACI,OAAO;cAC/DjB,MAAM,GAAGgB,oBAAoB;cAC7BnC,QAAQ,GAAGvB,UAAU,CAAC4D,qBAAqB,CAAEC,CAAC,IAAKV,aAAa,CAACU,CAAC,CAAC,CAACF,OAAO,EAAEJ,WAAW,EAAEG,oBAAoB,CAAC;YACnH,CAAC,MACI,IAAIpB,SAAS,EAAE;cAChB;AAC5B;AACA;cAC4BI,MAAM,GAAGhC,WAAW,CAAC4B,SAAS,CAAC,CAAC,CAAC;YACrC;UACJ;QACJ;QACA;AAChB;AACA;QACgB,IAAIhC,WAAW,CAACoC,MAAM,CAAC,IAAIpC,WAAW,CAACqC,MAAM,CAAC,EAAE;UAC5C,MAAMX,SAAS,GAAGZ,YAAY,CAACsB,MAAM,EAAEC,MAAM,EAAEpB,QAAQ,EAAEgB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACuB,QAAQ,CAAC,OAAO,CAAC,CAAC;UAC5HrB,QAAQ,GAAGb,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,YAAY,CAACC,SAAS,EAAEC,MAAM,CAAC,CAAC,EAAE;YAAE8B,MAAM,EAAE;UAAS,CAAC,CAAC;UAClG;UACA,IAAIvB,WAAW,EAAE;YACbA,WAAW,CAACR,SAAS,GAAGA,SAAS;YACjCQ,WAAW,CAACa,kBAAkB,GAAGG,WAAW,CAACC,GAAG,CAAC,CAAC;UACtD;QACJ;QACA;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAI,CAAChB,QAAQ,EAAE;UACX,MAAMuB,iBAAiB,GAAGjC,YAAY,CAACX,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;UAC5DqB,QAAQ,GAAG;YACPsB,MAAM,EAAE,MAAM;YACdE,QAAQ,EAAED,iBAAiB,CAACE;UAChC,CAAC;QACL;QACA,OAAOzB,QAAQ;MACnB;IACJ,CAAC;EACL,CAAC;AACL;AAEA0B,OAAO,CAACtD,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}