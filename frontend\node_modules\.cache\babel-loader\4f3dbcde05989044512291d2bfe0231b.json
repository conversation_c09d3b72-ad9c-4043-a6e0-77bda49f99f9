{"ast": null, "code": "const defaults = {\n  duration: 0.3,\n  delay: 0,\n  endDelay: 0,\n  repeat: 0,\n  easing: \"ease\"\n};\nexport { defaults };", "map": {"version": 3, "names": ["defaults", "duration", "delay", "endDelay", "repeat", "easing"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/defaults.es.js"], "sourcesContent": ["const defaults = {\n    duration: 0.3,\n    delay: 0,\n    endDelay: 0,\n    repeat: 0,\n    easing: \"ease\",\n};\n\nexport { defaults };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAG;EACbC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,CAAC;EACXC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACZ,CAAC;AAED,SAASL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}