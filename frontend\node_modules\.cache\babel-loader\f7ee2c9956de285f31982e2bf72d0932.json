{"ast": null, "code": "const isString = value => typeof value === \"string\";\nexport { isString };", "map": {"version": 3, "names": ["isString", "value"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/is-string.es.js"], "sourcesContent": ["const isString = (value) => typeof value === \"string\";\n\nexport { isString };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ;AAErD,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}