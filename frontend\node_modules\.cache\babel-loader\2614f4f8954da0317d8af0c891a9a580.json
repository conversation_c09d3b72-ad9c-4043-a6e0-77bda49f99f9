{"ast": null, "code": "'use strict';\n\nfunction stopAnimation(animation, needsCommit = true) {\n  if (!animation || animation.playState === \"finished\") return;\n  // Suppress error thrown by WAAPI\n  try {\n    if (animation.stop) {\n      animation.stop();\n    } else {\n      needsCommit && animation.commitStyles();\n      animation.cancel();\n    }\n  } catch (e) {}\n}\nexports.stopAnimation = stopAnimation;", "map": {"version": 3, "names": ["stopAnimation", "animation", "needsCommit", "playState", "stop", "commitStyles", "cancel", "e", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/stop-animation.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction stopAnimation(animation, needsCommit = true) {\n    if (!animation || animation.playState === \"finished\")\n        return;\n    // Suppress error thrown by WAAPI\n    try {\n        if (animation.stop) {\n            animation.stop();\n        }\n        else {\n            needsCommit && animation.commitStyles();\n            animation.cancel();\n        }\n    }\n    catch (e) { }\n}\n\nexports.stopAnimation = stopAnimation;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAaA,CAACC,SAAS,EAAEC,WAAW,GAAG,IAAI,EAAE;EAClD,IAAI,CAACD,SAAS,IAAIA,SAAS,CAACE,SAAS,KAAK,UAAU,EAChD;EACJ;EACA,IAAI;IACA,IAAIF,SAAS,CAACG,IAAI,EAAE;MAChBH,SAAS,CAACG,IAAI,CAAC,CAAC;IACpB,CAAC,MACI;MACDF,WAAW,IAAID,SAAS,CAACI,YAAY,CAAC,CAAC;MACvCJ,SAAS,CAACK,MAAM,CAAC,CAAC;IACtB;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;AAChB;AAEAC,OAAO,CAACR,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}