{"ast": null, "code": "import { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\nfunction scrapeMotionValuesFromProps(props) {\n  const newValues = scrapeMotionValuesFromProps$1(props);\n  for (const key in props) {\n    if (isMotionValue(props[key])) {\n      const targetKey = key === \"x\" || key === \"y\" ? \"attr\" + key.toUpperCase() : key;\n      newValues[targetKey] = props[key];\n    }\n  }\n  return newValues;\n}\nexport { scrapeMotionValuesFromProps };", "map": {"version": 3, "names": ["isMotionValue", "scrapeMotionValuesFromProps", "scrapeMotionValuesFromProps$1", "props", "newValues", "key", "<PERSON><PERSON><PERSON>", "toUpperCase"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\n\nfunction scrapeMotionValuesFromProps(props) {\n    const newValues = scrapeMotionValuesFromProps$1(props);\n    for (const key in props) {\n        if (isMotionValue(props[key])) {\n            const targetKey = key === \"x\" || key === \"y\" ? \"attr\" + key.toUpperCase() : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,0CAA0C;AACxE,SAASC,2BAA2B,IAAIC,6BAA6B,QAAQ,2CAA2C;AAExH,SAASD,2BAA2BA,CAACE,KAAK,EAAE;EACxC,MAAMC,SAAS,GAAGF,6BAA6B,CAACC,KAAK,CAAC;EACtD,KAAK,MAAME,GAAG,IAAIF,KAAK,EAAE;IACrB,IAAIH,aAAa,CAACG,KAAK,CAACE,GAAG,CAAC,CAAC,EAAE;MAC3B,MAAMC,SAAS,GAAGD,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,GAAG,GAAG,MAAM,GAAGA,GAAG,CAACE,WAAW,CAAC,CAAC,GAAGF,GAAG;MAC/ED,SAAS,CAACE,SAAS,CAAC,GAAGH,KAAK,CAACE,GAAG,CAAC;IACrC;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASH,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}