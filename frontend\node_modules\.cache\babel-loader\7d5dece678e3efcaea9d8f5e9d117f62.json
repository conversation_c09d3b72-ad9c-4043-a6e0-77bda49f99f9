{"ast": null, "code": "const time = {\n  ms: seconds => seconds * 1000,\n  s: milliseconds => milliseconds / 1000\n};\nexport { time };", "map": {"version": 3, "names": ["time", "ms", "seconds", "s", "milliseconds"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/time.es.js"], "sourcesContent": ["const time = {\n    ms: (seconds) => seconds * 1000,\n    s: (milliseconds) => milliseconds / 1000,\n};\n\nexport { time };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,EAAE,EAAGC,OAAO,IAAKA,OAAO,GAAG,IAAI;EAC/BC,CAAC,EAAGC,YAAY,IAAKA,YAAY,GAAG;AACxC,CAAC;AAED,SAASJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}