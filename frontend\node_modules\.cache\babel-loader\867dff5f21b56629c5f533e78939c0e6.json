{"ast": null, "code": "'use strict';\n\nvar tslib = require('tslib');\nvar events = require('../utils/events.cjs.js');\nvar inView$1 = require('../../gestures/in-view.cjs.js');\nconst inView = {\n  isActive: options => <PERSON><PERSON>an(options.inView),\n  subscribe: (element, {\n    enable,\n    disable\n  }, {\n    inViewOptions = {}\n  }) => {\n    const {\n        once\n      } = inViewOptions,\n      viewOptions = tslib.__rest(inViewOptions, [\"once\"]);\n    return inView$1.inView(element, enterEntry => {\n      enable();\n      events.dispatchViewEvent(element, \"viewenter\", enterEntry);\n      if (!once) {\n        return leaveEntry => {\n          disable();\n          events.dispatchViewEvent(element, \"viewleave\", leaveEntry);\n        };\n      }\n    }, viewOptions);\n  }\n};\nexports.inView = inView;", "map": {"version": 3, "names": ["tslib", "require", "events", "inView$1", "inView", "isActive", "options", "Boolean", "subscribe", "element", "enable", "disable", "inViewOptions", "once", "viewOptions", "__rest", "enterEntry", "dispatchViewEvent", "leaveEntry", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/gestures/in-view.cjs.js"], "sourcesContent": ["'use strict';\n\nvar tslib = require('tslib');\nvar events = require('../utils/events.cjs.js');\nvar inView$1 = require('../../gestures/in-view.cjs.js');\n\nconst inView = {\n    isActive: (options) => <PERSON><PERSON>an(options.inView),\n    subscribe: (element, { enable, disable }, { inViewOptions = {} }) => {\n        const { once } = inViewOptions, viewOptions = tslib.__rest(inViewOptions, [\"once\"]);\n        return inView$1.inView(element, (enterEntry) => {\n            enable();\n            events.dispatchViewEvent(element, \"viewenter\", enterEntry);\n            if (!once) {\n                return (leaveEntry) => {\n                    disable();\n                    events.dispatchViewEvent(element, \"viewleave\", leaveEntry);\n                };\n            }\n        }, viewOptions);\n    },\n};\n\nexports.inView = inView;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,MAAM,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAC9C,IAAIE,QAAQ,GAAGF,OAAO,CAAC,+BAA+B,CAAC;AAEvD,MAAMG,MAAM,GAAG;EACXC,QAAQ,EAAGC,OAAO,IAAKC,OAAO,CAACD,OAAO,CAACF,MAAM,CAAC;EAC9CI,SAAS,EAAEA,CAACC,OAAO,EAAE;IAAEC,MAAM;IAAEC;EAAQ,CAAC,EAAE;IAAEC,aAAa,GAAG,CAAC;EAAE,CAAC,KAAK;IACjE,MAAM;QAAEC;MAAK,CAAC,GAAGD,aAAa;MAAEE,WAAW,GAAGd,KAAK,CAACe,MAAM,CAACH,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;IACnF,OAAOT,QAAQ,CAACC,MAAM,CAACK,OAAO,EAAGO,UAAU,IAAK;MAC5CN,MAAM,CAAC,CAAC;MACRR,MAAM,CAACe,iBAAiB,CAACR,OAAO,EAAE,WAAW,EAAEO,UAAU,CAAC;MAC1D,IAAI,CAACH,IAAI,EAAE;QACP,OAAQK,UAAU,IAAK;UACnBP,OAAO,CAAC,CAAC;UACTT,MAAM,CAACe,iBAAiB,CAACR,OAAO,EAAE,WAAW,EAAES,UAAU,CAAC;QAC9D,CAAC;MACL;IACJ,CAAC,EAAEJ,WAAW,CAAC;EACnB;AACJ,CAAC;AAEDK,OAAO,CAACf,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}