#!/bin/bash

# Cable Operator CRM - Local Development Setup Script
# This script sets up the development environment quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js 18+ first."
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        error "Python is not installed. Please install Python 3.9+ first."
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error "npm is not installed. Please install npm first."
    fi
    
    success "Prerequisites check passed"
}

# Setup backend
setup_backend() {
    log "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        log "Creating Python virtual environment..."
        python3 -m venv venv || python -m venv venv
    fi
    
    # Activate virtual environment
    log "Activating virtual environment..."
    source venv/bin/activate || source venv/Scripts/activate
    
    # Install dependencies
    log "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Setup environment file
    if [ ! -f ".env" ]; then
        log "Creating backend .env file..."
        cp .env.example .env
        
        # Update for local development
        sed -i.bak 's|DATABASE_URL=.*|DATABASE_URL=sqlite:///./cable_operator.db|' .env
        sed -i.bak 's|DEBUG=.*|DEBUG=true|' .env
        sed -i.bak 's|ENVIRONMENT=.*|ENVIRONMENT=development|' .env
        rm .env.bak 2>/dev/null || true
    fi
    
    # Initialize database
    log "Initializing database..."
    alembic upgrade head
    
    cd ..
    success "Backend setup completed"
}

# Setup frontend
setup_frontend() {
    log "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    log "Installing Node.js dependencies..."
    npm install
    
    # Setup environment file
    if [ ! -f ".env" ]; then
        log "Creating frontend .env file..."
        cp .env.example .env
        
        # Update for local development
        echo "REACT_APP_API_URL=http://localhost:8000/api/v1" > .env
        echo "REACT_APP_ENVIRONMENT=development" >> .env
        echo "REACT_APP_DEBUG=true" >> .env
    fi
    
    cd ..
    success "Frontend setup completed"
}

# Create sample data
create_sample_data() {
    log "Creating sample data..."
    
    cd backend
    source venv/bin/activate || source venv/Scripts/activate
    
    # Create sample data script
    cat > create_sample_data.py << 'EOF'
import asyncio
from app.core.database import SessionLocal
from app.models.company import Company
from app.models.user import User, UserRole
from app.models.customer import Customer, CustomerStatus, ServiceType
from app.models.plan import Plan, PlanType
from app.models.payment import Payment, PaymentStatus, PaymentMethod
from app.core.security import SecurityManager
from datetime import date, datetime
import random

def create_sample_data():
    db = SessionLocal()
    
    try:
        # Check if data already exists
        if db.query(Company).first():
            print("Sample data already exists")
            return
        
        # Create company
        company = Company(
            name="Demo Cable Company",
            business_name="Demo Cable Company LLC",
            email="<EMAIL>",
            phone="******-123-4567",
            address_line1="123 Main Street",
            city="Demo City",
            state="Demo State",
            postal_code="12345",
            country="United States",
            is_setup_complete=True
        )
        db.add(company)
        db.flush()
        
        # Create admin user
        admin_user = User(
            first_name="Admin",
            last_name="User",
            email="<EMAIL>",
            password_hash=SecurityManager.get_password_hash("admin123"),
            role=UserRole.SUPER_ADMIN,
            company_id=company.id,
            is_active=True,
            is_verified=True
        )
        db.add(admin_user)
        db.flush()
        
        # Create plans
        plans = [
            Plan(
                name="Basic Internet",
                description="Basic internet plan for home users",
                plan_type=PlanType.INTERNET,
                price=29.99,
                internet_speed_download="25 Mbps",
                internet_speed_upload="5 Mbps",
                company_id=company.id,
                is_active=True
            ),
            Plan(
                name="High Speed Internet",
                description="High speed internet for power users",
                plan_type=PlanType.INTERNET,
                price=59.99,
                internet_speed_download="100 Mbps",
                internet_speed_upload="20 Mbps",
                company_id=company.id,
                is_active=True
            ),
            Plan(
                name="Basic Cable",
                description="Basic cable TV package",
                plan_type=PlanType.CABLE,
                price=39.99,
                channel_count=100,
                hd_channels=25,
                company_id=company.id,
                is_active=True
            ),
            Plan(
                name="Premium Bundle",
                description="Internet + Cable bundle",
                plan_type=PlanType.BUNDLE,
                price=89.99,
                internet_speed_download="100 Mbps",
                internet_speed_upload="20 Mbps",
                channel_count=200,
                hd_channels=50,
                company_id=company.id,
                is_active=True
            )
        ]
        
        for plan in plans:
            db.add(plan)
        db.flush()
        
        # Create customers
        customers = []
        for i in range(1, 11):
            customer = Customer(
                first_name=f"Customer{i}",
                last_name=f"LastName{i}",
                email=f"customer{i}@email.com",
                phone=f"******-{100+i:03d}-{1000+i:04d}",
                address_line1=f"{100+i} Demo Street",
                city="Demo City",
                state="Demo State",
                postal_code=f"{12345+i}",
                service_type=random.choice(list(ServiceType)),
                status=CustomerStatus.ACTIVE,
                connection_date=date.today(),
                plan_id=random.choice(plans).id,
                company_id=company.id,
                created_by_id=admin_user.id
            )
            customers.append(customer)
            db.add(customer)
        
        db.flush()
        
        # Create payments
        for customer in customers:
            for month in range(1, 4):  # 3 months of payments
                payment = Payment(
                    amount=customer.plan.price,
                    status=random.choice([PaymentStatus.PAID, PaymentStatus.PENDING, PaymentStatus.OVERDUE]),
                    payment_method=random.choice(list(PaymentMethod)),
                    due_date=date(2024, month, customer.billing_cycle_day),
                    billing_month=month,
                    billing_year=2024,
                    billing_period_start=date(2024, month, 1),
                    billing_period_end=date(2024, month, 28),
                    customer_id=customer.id,
                    plan_id=customer.plan_id,
                    company_id=company.id,
                    base_amount=customer.plan.price,
                    created_by_id=admin_user.id
                )
                
                if payment.status == PaymentStatus.PAID:
                    payment.paid_date = payment.due_date
                    payment.partial_amount_paid = payment.amount
                
                payment.update_amount()
                db.add(payment)
        
        db.commit()
        print("Sample data created successfully!")
        print("Login with: <EMAIL> / admin123")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating sample data: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_data()
EOF
    
    python create_sample_data.py
    rm create_sample_data.py
    
    cd ..
    success "Sample data created"
}

# Start development servers
start_servers() {
    log "Starting development servers..."
    
    # Start backend
    log "Starting backend server..."
    cd backend
    source venv/bin/activate || source venv/Scripts/activate
    
    # Start backend in background
    nohup uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend
    log "Starting frontend server..."
    cd frontend
    
    # Start frontend in background
    nohup npm start > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    
    cd ..
    
    success "Development servers started!"
    
    # Show access information
    echo ""
    log "🚀 Development servers are running:"
    echo "  📱 Frontend: http://localhost:3000"
    echo "  🔧 Backend API: http://localhost:8000"
    echo "  📚 API Docs: http://localhost:8000/docs"
    echo "  👤 Login: <EMAIL> / admin123"
    echo ""
    log "📋 Useful commands:"
    echo "  View backend logs: tail -f logs/backend.log"
    echo "  View frontend logs: tail -f logs/frontend.log"
    echo "  Stop servers: ./scripts/dev-setup.sh stop"
    echo ""
}

# Stop development servers
stop_servers() {
    log "Stopping development servers..."
    
    # Stop backend
    if [ -f "logs/backend.pid" ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            rm logs/backend.pid
            success "Backend server stopped"
        fi
    fi
    
    # Stop frontend
    if [ -f "logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            rm logs/frontend.pid
            success "Frontend server stopped"
        fi
    fi
    
    # Kill any remaining processes
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    pkill -f "npm start" 2>/dev/null || true
    
    success "All development servers stopped"
}

# Show status
show_status() {
    log "Development server status:"
    
    # Check backend
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "  ✅ Backend: Running (http://localhost:8000)"
    else
        echo "  ❌ Backend: Not running"
    fi
    
    # Check frontend
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "  ✅ Frontend: Running (http://localhost:3000)"
    else
        echo "  ❌ Frontend: Not running"
    fi
}

# Main execution
main() {
    # Create logs directory
    mkdir -p logs
    
    case "$1" in
        "setup")
            check_prerequisites
            setup_backend
            setup_frontend
            create_sample_data
            success "Development environment setup completed!"
            ;;
        "start")
            start_servers
            ;;
        "stop")
            stop_servers
            ;;
        "status")
            show_status
            ;;
        "restart")
            stop_servers
            sleep 2
            start_servers
            ;;
        *)
            log "Cable Operator CRM - Development Setup"
            echo ""
            echo "Usage: $0 {setup|start|stop|status|restart}"
            echo ""
            echo "Commands:"
            echo "  setup   - Setup development environment"
            echo "  start   - Start development servers"
            echo "  stop    - Stop development servers"
            echo "  status  - Show server status"
            echo "  restart - Restart development servers"
            echo ""
            echo "Quick start:"
            echo "  1. $0 setup"
            echo "  2. $0 start"
            echo "  3. Open http://localhost:3000"
            ;;
    esac
}

main "$@"
