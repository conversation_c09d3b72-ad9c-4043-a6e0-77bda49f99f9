{"ast": null, "code": "const frameData = {\n  delta: 0,\n  timestamp: 0\n};\nexport { frameData };", "map": {"version": 3, "names": ["frameData", "delta", "timestamp"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/frameloop/data.mjs"], "sourcesContent": ["const frameData = {\n    delta: 0,\n    timestamp: 0,\n};\n\nexport { frameData };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG;EACdC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE;AACf,CAAC;AAED,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}