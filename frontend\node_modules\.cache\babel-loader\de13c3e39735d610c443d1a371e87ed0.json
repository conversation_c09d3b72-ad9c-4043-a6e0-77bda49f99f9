{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports.warning = function () {};\nexports.invariant = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  exports.warning = function (check, message) {\n    if (!check && typeof console !== 'undefined') {\n      console.warn(message);\n    }\n  };\n  exports.invariant = function (check, message) {\n    if (!check) {\n      throw new Error(message);\n    }\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "warning", "invariant", "process", "env", "NODE_ENV", "check", "message", "console", "warn", "Error"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/hey-listen/dist/index.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nexports.warning = function () { };\r\nexports.invariant = function () { };\r\nif (process.env.NODE_ENV !== 'production') {\r\n    exports.warning = function (check, message) {\r\n        if (!check && typeof console !== 'undefined') {\r\n            console.warn(message);\r\n        }\r\n    };\r\n    exports.invariant = function (check, message) {\r\n        if (!check) {\r\n            throw new Error(message);\r\n        }\r\n    };\r\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAE7DD,OAAO,CAACE,OAAO,GAAG,YAAY,CAAE,CAAC;AACjCF,OAAO,CAACG,SAAS,GAAG,YAAY,CAAE,CAAC;AACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACvCN,OAAO,CAACE,OAAO,GAAG,UAAUK,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAACD,KAAK,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAC1CA,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;IACzB;EACJ,CAAC;EACDR,OAAO,CAACG,SAAS,GAAG,UAAUI,KAAK,EAAEC,OAAO,EAAE;IAC1C,IAAI,CAACD,KAAK,EAAE;MACR,MAAM,IAAII,KAAK,CAACH,OAAO,CAAC;IAC5B;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "script"}