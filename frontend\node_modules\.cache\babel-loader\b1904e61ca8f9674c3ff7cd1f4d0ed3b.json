{"ast": null, "code": "import { noopReturn } from '@motionone/utils';\nconst timeStep = 10;\nconst maxDuration = 10000;\nfunction pregenerateKeyframes(generator, toUnit = noopReturn) {\n  let overshootDuration = undefined;\n  let timestamp = timeStep;\n  let state = generator(0);\n  const keyframes = [toUnit(state.current)];\n  while (!state.done && timestamp < maxDuration) {\n    state = generator(timestamp);\n    keyframes.push(toUnit(state.done ? state.target : state.current));\n    if (overshootDuration === undefined && state.hasReachedTarget) {\n      overshootDuration = timestamp;\n    }\n    timestamp += timeStep;\n  }\n  const duration = timestamp - timeStep;\n  /**\n   * If generating an animation that didn't actually move,\n   * generate a second keyframe so we have an origin and target.\n   */\n  if (keyframes.length === 1) keyframes.push(state.current);\n  return {\n    keyframes,\n    duration: duration / 1000,\n    overshootDuration: (overshootDuration !== null && overshootDuration !== void 0 ? overshootDuration : duration) / 1000\n  };\n}\nexport { pregenerateKeyframes };", "map": {"version": 3, "names": ["noopReturn", "timeStep", "maxDuration", "pregenerateKeyframes", "generator", "toUnit", "overshootDuration", "undefined", "timestamp", "state", "keyframes", "current", "done", "push", "target", "hasReached<PERSON><PERSON><PERSON>", "duration", "length"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/generators/dist/utils/pregenerate-keyframes.es.js"], "sourcesContent": ["import { noopReturn } from '@motionone/utils';\n\nconst timeStep = 10;\nconst maxDuration = 10000;\nfunction pregenerateKeyframes(generator, toUnit = noopReturn) {\n    let overshootDuration = undefined;\n    let timestamp = timeStep;\n    let state = generator(0);\n    const keyframes = [toUnit(state.current)];\n    while (!state.done && timestamp < maxDuration) {\n        state = generator(timestamp);\n        keyframes.push(toUnit(state.done ? state.target : state.current));\n        if (overshootDuration === undefined && state.hasReachedTarget) {\n            overshootDuration = timestamp;\n        }\n        timestamp += timeStep;\n    }\n    const duration = timestamp - timeStep;\n    /**\n     * If generating an animation that didn't actually move,\n     * generate a second keyframe so we have an origin and target.\n     */\n    if (keyframes.length === 1)\n        keyframes.push(state.current);\n    return {\n        keyframes,\n        duration: duration / 1000,\n        overshootDuration: (overshootDuration !== null && overshootDuration !== void 0 ? overshootDuration : duration) / 1000,\n    };\n}\n\nexport { pregenerateKeyframes };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,WAAW,GAAG,KAAK;AACzB,SAASC,oBAAoBA,CAACC,SAAS,EAAEC,MAAM,GAAGL,UAAU,EAAE;EAC1D,IAAIM,iBAAiB,GAAGC,SAAS;EACjC,IAAIC,SAAS,GAAGP,QAAQ;EACxB,IAAIQ,KAAK,GAAGL,SAAS,CAAC,CAAC,CAAC;EACxB,MAAMM,SAAS,GAAG,CAACL,MAAM,CAACI,KAAK,CAACE,OAAO,CAAC,CAAC;EACzC,OAAO,CAACF,KAAK,CAACG,IAAI,IAAIJ,SAAS,GAAGN,WAAW,EAAE;IAC3CO,KAAK,GAAGL,SAAS,CAACI,SAAS,CAAC;IAC5BE,SAAS,CAACG,IAAI,CAACR,MAAM,CAACI,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACK,MAAM,GAAGL,KAAK,CAACE,OAAO,CAAC,CAAC;IACjE,IAAIL,iBAAiB,KAAKC,SAAS,IAAIE,KAAK,CAACM,gBAAgB,EAAE;MAC3DT,iBAAiB,GAAGE,SAAS;IACjC;IACAA,SAAS,IAAIP,QAAQ;EACzB;EACA,MAAMe,QAAQ,GAAGR,SAAS,GAAGP,QAAQ;EACrC;AACJ;AACA;AACA;EACI,IAAIS,SAAS,CAACO,MAAM,KAAK,CAAC,EACtBP,SAAS,CAACG,IAAI,CAACJ,KAAK,CAACE,OAAO,CAAC;EACjC,OAAO;IACHD,SAAS;IACTM,QAAQ,EAAEA,QAAQ,GAAG,IAAI;IACzBV,iBAAiB,EAAE,CAACA,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGU,QAAQ,IAAI;EACrH,CAAC;AACL;AAEA,SAASb,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}