{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar info = require('./info.cjs.js');\nvar index = require('./offsets/index.cjs.js');\nfunction measure(container, target = container, info) {\n  /**\n   * Find inset of target within scrollable container\n   */\n  info.x.targetOffset = 0;\n  info.y.targetOffset = 0;\n  if (target !== container) {\n    let node = target;\n    while (node && node != container) {\n      info.x.targetOffset += node.offsetLeft;\n      info.y.targetOffset += node.offsetTop;\n      node = node.offsetParent;\n    }\n  }\n  info.x.targetLength = target === container ? target.scrollWidth : target.clientWidth;\n  info.y.targetLength = target === container ? target.scrollHeight : target.clientHeight;\n  info.x.containerLength = container.clientWidth;\n  info.y.containerLength = container.clientHeight;\n}\nfunction createOnScrollHandler(element, onScroll, info$1, options = {}) {\n  const axis = options.axis || \"y\";\n  return {\n    measure: () => measure(element, options.target, info$1),\n    update: time => {\n      info.updateScrollInfo(element, info$1, time);\n      if (options.offset || options.target) {\n        index.resolveOffsets(element, info$1, options);\n      }\n    },\n    notify: utils.isFunction(onScroll) ? () => onScroll(info$1) : scrubAnimation(onScroll, info$1[axis])\n  };\n}\nfunction scrubAnimation(controls, axisInfo) {\n  controls.pause();\n  controls.forEachNative((animation, {\n    easing\n  }) => {\n    var _a, _b;\n    if (animation.updateDuration) {\n      if (!easing) animation.easing = utils.noopReturn;\n      animation.updateDuration(1);\n    } else {\n      const timingOptions = {\n        duration: 1000\n      };\n      if (!easing) timingOptions.easing = \"linear\";\n      (_b = (_a = animation.effect) === null || _a === void 0 ? void 0 : _a.updateTiming) === null || _b === void 0 ? void 0 : _b.call(_a, timingOptions);\n    }\n  });\n  return () => {\n    controls.currentTime = axisInfo.progress;\n  };\n}\nexports.createOnScrollHandler = createOnScrollHandler;", "map": {"version": 3, "names": ["utils", "require", "info", "index", "measure", "container", "target", "x", "targetOffset", "y", "node", "offsetLeft", "offsetTop", "offsetParent", "targetLength", "scrollWidth", "clientWidth", "scrollHeight", "clientHeight", "containerLength", "createOnScrollHandler", "element", "onScroll", "info$1", "options", "axis", "update", "time", "updateScrollInfo", "offset", "resolveOffsets", "notify", "isFunction", "scrubAnimation", "controls", "axisInfo", "pause", "forEachNative", "animation", "easing", "_a", "_b", "updateDuration", "noopReturn", "timingOptions", "duration", "effect", "updateTiming", "call", "currentTime", "progress", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/on-scroll-handler.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar info = require('./info.cjs.js');\nvar index = require('./offsets/index.cjs.js');\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node != container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n}\nfunction createOnScrollHandler(element, onScroll, info$1, options = {}) {\n    const axis = options.axis || \"y\";\n    return {\n        measure: () => measure(element, options.target, info$1),\n        update: (time) => {\n            info.updateScrollInfo(element, info$1, time);\n            if (options.offset || options.target) {\n                index.resolveOffsets(element, info$1, options);\n            }\n        },\n        notify: utils.isFunction(onScroll)\n            ? () => onScroll(info$1)\n            : scrubAnimation(onScroll, info$1[axis]),\n    };\n}\nfunction scrubAnimation(controls, axisInfo) {\n    controls.pause();\n    controls.forEachNative((animation, { easing }) => {\n        var _a, _b;\n        if (animation.updateDuration) {\n            if (!easing)\n                animation.easing = utils.noopReturn;\n            animation.updateDuration(1);\n        }\n        else {\n            const timingOptions = { duration: 1000 };\n            if (!easing)\n                timingOptions.easing = \"linear\";\n            (_b = (_a = animation.effect) === null || _a === void 0 ? void 0 : _a.updateTiming) === null || _b === void 0 ? void 0 : _b.call(_a, timingOptions);\n        }\n    });\n    return () => {\n        controls.currentTime = axisInfo.progress;\n    };\n}\n\nexports.createOnScrollHandler = createOnScrollHandler;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,IAAI,GAAGD,OAAO,CAAC,eAAe,CAAC;AACnC,IAAIE,KAAK,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAE7C,SAASG,OAAOA,CAACC,SAAS,EAAEC,MAAM,GAAGD,SAAS,EAAEH,IAAI,EAAE;EAClD;AACJ;AACA;EACIA,IAAI,CAACK,CAAC,CAACC,YAAY,GAAG,CAAC;EACvBN,IAAI,CAACO,CAAC,CAACD,YAAY,GAAG,CAAC;EACvB,IAAIF,MAAM,KAAKD,SAAS,EAAE;IACtB,IAAIK,IAAI,GAAGJ,MAAM;IACjB,OAAOI,IAAI,IAAIA,IAAI,IAAIL,SAAS,EAAE;MAC9BH,IAAI,CAACK,CAAC,CAACC,YAAY,IAAIE,IAAI,CAACC,UAAU;MACtCT,IAAI,CAACO,CAAC,CAACD,YAAY,IAAIE,IAAI,CAACE,SAAS;MACrCF,IAAI,GAAGA,IAAI,CAACG,YAAY;IAC5B;EACJ;EACAX,IAAI,CAACK,CAAC,CAACO,YAAY,GACfR,MAAM,KAAKD,SAAS,GAAGC,MAAM,CAACS,WAAW,GAAGT,MAAM,CAACU,WAAW;EAClEd,IAAI,CAACO,CAAC,CAACK,YAAY,GACfR,MAAM,KAAKD,SAAS,GAAGC,MAAM,CAACW,YAAY,GAAGX,MAAM,CAACY,YAAY;EACpEhB,IAAI,CAACK,CAAC,CAACY,eAAe,GAAGd,SAAS,CAACW,WAAW;EAC9Cd,IAAI,CAACO,CAAC,CAACU,eAAe,GAAGd,SAAS,CAACa,YAAY;AACnD;AACA,SAASE,qBAAqBA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpE,MAAMC,IAAI,GAAGD,OAAO,CAACC,IAAI,IAAI,GAAG;EAChC,OAAO;IACHrB,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACiB,OAAO,EAAEG,OAAO,CAAClB,MAAM,EAAEiB,MAAM,CAAC;IACvDG,MAAM,EAAGC,IAAI,IAAK;MACdzB,IAAI,CAAC0B,gBAAgB,CAACP,OAAO,EAAEE,MAAM,EAAEI,IAAI,CAAC;MAC5C,IAAIH,OAAO,CAACK,MAAM,IAAIL,OAAO,CAAClB,MAAM,EAAE;QAClCH,KAAK,CAAC2B,cAAc,CAACT,OAAO,EAAEE,MAAM,EAAEC,OAAO,CAAC;MAClD;IACJ,CAAC;IACDO,MAAM,EAAE/B,KAAK,CAACgC,UAAU,CAACV,QAAQ,CAAC,GAC5B,MAAMA,QAAQ,CAACC,MAAM,CAAC,GACtBU,cAAc,CAACX,QAAQ,EAAEC,MAAM,CAACE,IAAI,CAAC;EAC/C,CAAC;AACL;AACA,SAASQ,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACxCD,QAAQ,CAACE,KAAK,CAAC,CAAC;EAChBF,QAAQ,CAACG,aAAa,CAAC,CAACC,SAAS,EAAE;IAAEC;EAAO,CAAC,KAAK;IAC9C,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAIH,SAAS,CAACI,cAAc,EAAE;MAC1B,IAAI,CAACH,MAAM,EACPD,SAAS,CAACC,MAAM,GAAGvC,KAAK,CAAC2C,UAAU;MACvCL,SAAS,CAACI,cAAc,CAAC,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAME,aAAa,GAAG;QAAEC,QAAQ,EAAE;MAAK,CAAC;MACxC,IAAI,CAACN,MAAM,EACPK,aAAa,CAACL,MAAM,GAAG,QAAQ;MACnC,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGF,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,YAAY,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAACR,EAAE,EAAEI,aAAa,CAAC;IACvJ;EACJ,CAAC,CAAC;EACF,OAAO,MAAM;IACTV,QAAQ,CAACe,WAAW,GAAGd,QAAQ,CAACe,QAAQ;EAC5C,CAAC;AACL;AAEAC,OAAO,CAAC/B,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}