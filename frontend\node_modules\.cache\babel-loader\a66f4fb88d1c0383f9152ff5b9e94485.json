{"ast": null, "code": "'use strict';\n\nvar isVariant = require('./is-variant.cjs.js');\nfunction resolveVariant(definition, variants) {\n  if (isVariant.isVariant(definition)) {\n    return definition;\n  } else if (definition && variants) {\n    return variants[definition];\n  }\n}\nexports.resolveVariant = resolveVariant;", "map": {"version": 3, "names": ["isVariant", "require", "resolveV<PERSON>t", "definition", "variants", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/utils/resolve-variant.cjs.js"], "sourcesContent": ["'use strict';\n\nvar isVariant = require('./is-variant.cjs.js');\n\nfunction resolveVariant(definition, variants) {\n    if (isVariant.isVariant(definition)) {\n        return definition;\n    }\n    else if (definition && variants) {\n        return variants[definition];\n    }\n}\n\nexports.resolveVariant = resolveVariant;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE9C,SAASC,cAAcA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAC1C,IAAIJ,SAAS,CAACA,SAAS,CAACG,UAAU,CAAC,EAAE;IACjC,OAAOA,UAAU;EACrB,CAAC,MACI,IAAIA,UAAU,IAAIC,QAAQ,EAAE;IAC7B,OAAOA,QAAQ,CAACD,UAAU,CAAC;EAC/B;AACJ;AAEAE,OAAO,CAACH,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script"}