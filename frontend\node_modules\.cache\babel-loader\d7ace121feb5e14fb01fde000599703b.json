{"ast": null, "code": "const createDefinition = propNames => ({\n  isEnabled: props => propNames.some(name => !!props[name])\n});\nconst featureDefinitions = {\n  measureLayout: createDefinition([\"layout\", \"layoutId\", \"drag\"]),\n  animation: createDefinition([\"animate\", \"exit\", \"variants\", \"whileHover\", \"whileTap\", \"whileFocus\", \"whileDrag\", \"whileInView\"]),\n  exit: createDefinition([\"exit\"]),\n  drag: createDefinition([\"drag\", \"dragControls\"]),\n  focus: createDefinition([\"whileFocus\"]),\n  hover: createDefinition([\"whileHover\", \"onHoverStart\", \"onHoverEnd\"]),\n  tap: createDefinition([\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"]),\n  pan: createDefinition([\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"]),\n  inView: createDefinition([\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"])\n};\nexport { featureDefinitions };", "map": {"version": 3, "names": ["createDefinition", "propNames", "isEnabled", "props", "some", "name", "featureDefinitions", "measureLayout", "animation", "exit", "drag", "focus", "hover", "tap", "pan", "inView"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/definitions.mjs"], "sourcesContent": ["const createDefinition = (propNames) => ({\n    isEnabled: (props) => propNames.some((name) => !!props[name]),\n});\nconst featureDefinitions = {\n    measureLayout: createDefinition([\"layout\", \"layoutId\", \"drag\"]),\n    animation: createDefinition([\n        \"animate\",\n        \"exit\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"whileFocus\",\n        \"whileDrag\",\n        \"whileInView\",\n    ]),\n    exit: createDefinition([\"exit\"]),\n    drag: createDefinition([\"drag\", \"dragControls\"]),\n    focus: createDefinition([\"whileFocus\"]),\n    hover: createDefinition([\"whileHover\", \"onHoverStart\", \"onHoverEnd\"]),\n    tap: createDefinition([\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"]),\n    pan: createDefinition([\n        \"onPan\",\n        \"onPanStart\",\n        \"onPanSessionStart\",\n        \"onPanEnd\",\n    ]),\n    inView: createDefinition([\n        \"whileInView\",\n        \"onViewportEnter\",\n        \"onViewportLeave\",\n    ]),\n};\n\nexport { featureDefinitions };\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAIC,SAAS,KAAM;EACrCC,SAAS,EAAGC,KAAK,IAAKF,SAAS,CAACG,IAAI,CAAEC,IAAI,IAAK,CAAC,CAACF,KAAK,CAACE,IAAI,CAAC;AAChE,CAAC,CAAC;AACF,MAAMC,kBAAkB,GAAG;EACvBC,aAAa,EAAEP,gBAAgB,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;EAC/DQ,SAAS,EAAER,gBAAgB,CAAC,CACxB,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,WAAW,EACX,aAAa,CAChB,CAAC;EACFS,IAAI,EAAET,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC;EAChCU,IAAI,EAAEV,gBAAgB,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;EAChDW,KAAK,EAAEX,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC;EACvCY,KAAK,EAAEZ,gBAAgB,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;EACrEa,GAAG,EAAEb,gBAAgB,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;EACzEc,GAAG,EAAEd,gBAAgB,CAAC,CAClB,OAAO,EACP,YAAY,EACZ,mBAAmB,EACnB,UAAU,CACb,CAAC;EACFe,MAAM,EAAEf,gBAAgB,CAAC,CACrB,aAAa,EACb,iBAAiB,EACjB,iBAAiB,CACpB;AACL,CAAC;AAED,SAASM,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}