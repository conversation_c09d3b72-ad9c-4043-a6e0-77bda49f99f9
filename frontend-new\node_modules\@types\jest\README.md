# Installation
> `npm install --save @types/jest`

# Summary
This package contains type definitions for Jest (https://jestjs.io/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jest.

### Additional Details
 * Last updated: Wed, 01 Jun 2022 18:01:29 GMT
 * Dependencies: [@types/jest-matcher-utils](https://npmjs.com/package/@types/jest-matcher-utils), [@types/pretty-format](https://npmjs.com/package/@types/pretty-format)
 * Global values: `afterAll`, `afterEach`, `beforeAll`, `beforeEach`, `describe`, `expect`, `fail`, `fdescribe`, `fit`, `it`, `jasmine`, `jest`, `pending`, `spyOn`, `test`, `xdescribe`, `xit`, `xtest`

# Credits
These definitions were written by [<PERSON><PERSON> (https://asana.com)
//                 Ivo Stratev](https://github.com/NoHomey), [jwbay](https://github.com/jwbay), [<PERSON><PERSON>](https://github.com/asvet<PERSON><PERSON>), [<PERSON>](https://github.com/alexjoverm), [Allan Lukwago](https://github.com/epicallan), [Ika](https://github.com/ikatyang), [Waseem Dahman](https://github.com/wsmd), [Jamie Mason](https://github.com/JamieMason), [Douglas Duteil](https://github.com/douglasduteil), [Ahn](https://github.com/ahnpnl), [Jeff Lau](https://github.com/UselessPickles), [Andrew Makarov](https://github.com/r3nya), [Martin Hochel](https://github.com/hotell), [Sebastian Sebald](https://github.com/sebald), [Andy](https://github.com/andys8), [Antoine Brault](https://github.com/antoinebrault), [Gregor Stamać](https://github.com/gstamac), [ExE Boss](https://github.com/ExE-Boss), [Alex Bolenok](https://github.com/quassnoi), [Mario Beltrán Alarcón](https://github.com/Belco90), [Tony Hallett](https://github.com/tonyhallett), [Jason Yu](https://github.com/ycmjason), [Pawel Fajfer](https://github.com/pawfa), [Regev Brody](https://github.com/regevbr), [Alexandre Germain](https://github.com/gerkindev), and [Adam Jones](https://github.com/domdomegg).
