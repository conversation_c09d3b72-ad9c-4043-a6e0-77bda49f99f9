{"ast": null, "code": "'use strict';\n\nvar tslib = require('tslib');\nvar index = require('../resize/index.cjs.js');\nvar info = require('./info.cjs.js');\nvar onScrollHandler = require('./on-scroll-handler.cjs.js');\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.documentElement ? window : element;\nfunction scroll(onScroll, _a = {}) {\n  var {\n      container = document.documentElement\n    } = _a,\n    options = tslib.__rest(_a, [\"container\"]);\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info$1 = info.createScrollInfo();\n  const containerHandler = onScrollHandler.createOnScrollHandler(container, onScroll, info$1, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const listener = () => {\n      const time = performance.now();\n      for (const handler of containerHandlers) handler.measure();\n      for (const handler of containerHandlers) handler.update(time);\n      for (const handler of containerHandlers) handler.notify();\n    };\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, index.resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n  }\n  const listener = scrollListeners.get(container);\n  const onLoadProcesss = requestAnimationFrame(listener);\n  return () => {\n    var _a;\n    if (typeof onScroll !== \"function\") onScroll.stop();\n    cancelAnimationFrame(onLoadProcesss);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const containerHandlers = onScrollHandlers.get(container);\n    if (!containerHandlers) return;\n    containerHandlers.delete(containerHandler);\n    if (containerHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const listener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (listener) {\n      getEventTarget(container).removeEventListener(\"scroll\", listener);\n      (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n      window.removeEventListener(\"resize\", listener);\n    }\n  };\n}\nexports.scroll = scroll;", "map": {"version": 3, "names": ["tslib", "require", "index", "info", "onScrollHandler", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "documentElement", "window", "scroll", "onScroll", "_a", "container", "options", "__rest", "containerHandlers", "get", "Set", "set", "info$1", "createScrollInfo", "containerHandler", "createOnScrollHandler", "add", "has", "listener", "time", "performance", "now", "handler", "measure", "update", "notify", "target", "addEventListener", "passive", "resize", "onLoadProcesss", "requestAnimationFrame", "stop", "cancelAnimationFrame", "delete", "size", "removeEventListener", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar tslib = require('tslib');\nvar index = require('../resize/index.cjs.js');\nvar info = require('./info.cjs.js');\nvar onScrollHandler = require('./on-scroll-handler.cjs.js');\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scroll(onScroll, _a = {}) {\n    var { container = document.documentElement } = _a, options = tslib.__rest(_a, [\"container\"]);\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info$1 = info.createScrollInfo();\n    const containerHandler = onScrollHandler.createOnScrollHandler(container, onScroll, info$1, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const listener = () => {\n            const time = performance.now();\n            for (const handler of containerHandlers)\n                handler.measure();\n            for (const handler of containerHandlers)\n                handler.update(time);\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, index.resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    const onLoadProcesss = requestAnimationFrame(listener);\n    return () => {\n        var _a;\n        if (typeof onScroll !== \"function\")\n            onScroll.stop();\n        cancelAnimationFrame(onLoadProcesss);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const containerHandlers = onScrollHandlers.get(container);\n        if (!containerHandlers)\n            return;\n        containerHandlers.delete(containerHandler);\n        if (containerHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const listener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (listener) {\n            getEventTarget(container).removeEventListener(\"scroll\", listener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", listener);\n        }\n    };\n}\n\nexports.scroll = scroll;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,KAAK,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAC7C,IAAIE,IAAI,GAAGF,OAAO,CAAC,eAAe,CAAC;AACnC,IAAIG,eAAe,GAAGH,OAAO,CAAC,4BAA4B,CAAC;AAE3D,MAAMI,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,eAAe,GAAGC,MAAM,GAAGH,OAAO;AAC3F,SAASI,MAAMA,CAACC,QAAQ,EAAEC,EAAE,GAAG,CAAC,CAAC,EAAE;EAC/B,IAAI;MAAEC,SAAS,GAAGN,QAAQ,CAACC;IAAgB,CAAC,GAAGI,EAAE;IAAEE,OAAO,GAAGlB,KAAK,CAACmB,MAAM,CAACH,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;EAC5F,IAAII,iBAAiB,GAAGZ,gBAAgB,CAACa,GAAG,CAACJ,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACG,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC7Bd,gBAAgB,CAACe,GAAG,CAACN,SAAS,EAAEG,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,MAAM,GAAGrB,IAAI,CAACsB,gBAAgB,CAAC,CAAC;EACtC,MAAMC,gBAAgB,GAAGtB,eAAe,CAACuB,qBAAqB,CAACV,SAAS,EAAEF,QAAQ,EAAES,MAAM,EAAEN,OAAO,CAAC;EACpGE,iBAAiB,CAACQ,GAAG,CAACF,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAACrB,eAAe,CAACwB,GAAG,CAACZ,SAAS,CAAC,EAAE;IACjC,MAAMa,QAAQ,GAAGA,CAAA,KAAM;MACnB,MAAMC,IAAI,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAC9B,KAAK,MAAMC,OAAO,IAAId,iBAAiB,EACnCc,OAAO,CAACC,OAAO,CAAC,CAAC;MACrB,KAAK,MAAMD,OAAO,IAAId,iBAAiB,EACnCc,OAAO,CAACE,MAAM,CAACL,IAAI,CAAC;MACxB,KAAK,MAAMG,OAAO,IAAId,iBAAiB,EACnCc,OAAO,CAACG,MAAM,CAAC,CAAC;IACxB,CAAC;IACDhC,eAAe,CAACkB,GAAG,CAACN,SAAS,EAAEa,QAAQ,CAAC;IACxC,MAAMQ,MAAM,GAAG7B,cAAc,CAACQ,SAAS,CAAC;IACxCJ,MAAM,CAAC0B,gBAAgB,CAAC,QAAQ,EAAET,QAAQ,EAAE;MAAEU,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAIvB,SAAS,KAAKN,QAAQ,CAACC,eAAe,EAAE;MACxCL,eAAe,CAACgB,GAAG,CAACN,SAAS,EAAEf,KAAK,CAACuC,MAAM,CAACxB,SAAS,EAAEa,QAAQ,CAAC,CAAC;IACrE;IACAQ,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAET,QAAQ,EAAE;MAAEU,OAAO,EAAE;IAAK,CAAC,CAAC;EAClE;EACA,MAAMV,QAAQ,GAAGzB,eAAe,CAACgB,GAAG,CAACJ,SAAS,CAAC;EAC/C,MAAMyB,cAAc,GAAGC,qBAAqB,CAACb,QAAQ,CAAC;EACtD,OAAO,MAAM;IACT,IAAId,EAAE;IACN,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAC9BA,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IACnBC,oBAAoB,CAACH,cAAc,CAAC;IACpC;AACR;AACA;IACQ,MAAMtB,iBAAiB,GAAGZ,gBAAgB,CAACa,GAAG,CAACJ,SAAS,CAAC;IACzD,IAAI,CAACG,iBAAiB,EAClB;IACJA,iBAAiB,CAAC0B,MAAM,CAACpB,gBAAgB,CAAC;IAC1C,IAAIN,iBAAiB,CAAC2B,IAAI,EACtB;IACJ;AACR;AACA;IACQ,MAAMjB,QAAQ,GAAGzB,eAAe,CAACgB,GAAG,CAACJ,SAAS,CAAC;IAC/CZ,eAAe,CAACyC,MAAM,CAAC7B,SAAS,CAAC;IACjC,IAAIa,QAAQ,EAAE;MACVrB,cAAc,CAACQ,SAAS,CAAC,CAAC+B,mBAAmB,CAAC,QAAQ,EAAElB,QAAQ,CAAC;MACjE,CAACd,EAAE,GAAGT,eAAe,CAACc,GAAG,CAACJ,SAAS,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC;MAC/EH,MAAM,CAACmC,mBAAmB,CAAC,QAAQ,EAAElB,QAAQ,CAAC;IAClD;EACJ,CAAC;AACL;AAEAmB,OAAO,CAACnC,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}