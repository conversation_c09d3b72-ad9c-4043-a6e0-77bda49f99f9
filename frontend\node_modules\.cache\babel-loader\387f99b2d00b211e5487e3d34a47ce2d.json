{"ast": null, "code": "'use strict';\n\nfunction calcInset(element, container) {\n  let inset = {\n    x: 0,\n    y: 0\n  };\n  let current = element;\n  while (current && current !== container) {\n    if (current instanceof HTMLElement) {\n      inset.x += current.offsetLeft;\n      inset.y += current.offsetTop;\n      current = current.offsetParent;\n    } else if (current instanceof SVGGraphicsElement && \"getBBox\" in current) {\n      const {\n        top,\n        left\n      } = current.getBBox();\n      inset.x += left;\n      inset.y += top;\n      /**\n       * Assign the next parent element as the <svg /> tag.\n       */\n      while (current && current.tagName !== \"svg\") {\n        current = current.parentNode;\n      }\n    }\n  }\n  return inset;\n}\nexports.calcInset = calcInset;", "map": {"version": 3, "names": ["calcInset", "element", "container", "inset", "x", "y", "current", "HTMLElement", "offsetLeft", "offsetTop", "offsetParent", "SVGGraphicsElement", "top", "left", "getBBox", "tagName", "parentNode", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/offsets/inset.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction calcInset(element, container) {\n    let inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (current instanceof HTMLElement) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current instanceof SVGGraphicsElement && \"getBBox\" in current) {\n            const { top, left } = current.getBBox();\n            inset.x += left;\n            inset.y += top;\n            /**\n             * Assign the next parent element as the <svg /> tag.\n             */\n            while (current && current.tagName !== \"svg\") {\n                current = current.parentNode;\n            }\n        }\n    }\n    return inset;\n}\n\nexports.calcInset = calcInset;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAASA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACnC,IAAIC,KAAK,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAC1B,IAAIC,OAAO,GAAGL,OAAO;EACrB,OAAOK,OAAO,IAAIA,OAAO,KAAKJ,SAAS,EAAE;IACrC,IAAII,OAAO,YAAYC,WAAW,EAAE;MAChCJ,KAAK,CAACC,CAAC,IAAIE,OAAO,CAACE,UAAU;MAC7BL,KAAK,CAACE,CAAC,IAAIC,OAAO,CAACG,SAAS;MAC5BH,OAAO,GAAGA,OAAO,CAACI,YAAY;IAClC,CAAC,MACI,IAAIJ,OAAO,YAAYK,kBAAkB,IAAI,SAAS,IAAIL,OAAO,EAAE;MACpE,MAAM;QAAEM,GAAG;QAAEC;MAAK,CAAC,GAAGP,OAAO,CAACQ,OAAO,CAAC,CAAC;MACvCX,KAAK,CAACC,CAAC,IAAIS,IAAI;MACfV,KAAK,CAACE,CAAC,IAAIO,GAAG;MACd;AACZ;AACA;MACY,OAAON,OAAO,IAAIA,OAAO,CAACS,OAAO,KAAK,KAAK,EAAE;QACzCT,OAAO,GAAGA,OAAO,CAACU,UAAU;MAChC;IACJ;EACJ;EACA,OAAOb,KAAK;AAChB;AAEAc,OAAO,CAACjB,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}