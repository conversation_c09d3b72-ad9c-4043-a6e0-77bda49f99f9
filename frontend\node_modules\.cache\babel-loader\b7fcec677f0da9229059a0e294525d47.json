{"ast": null, "code": "/**\n * The MotionValue tracks the state of a single animatable\n * value. Currently, updatedAt and current are unused. The\n * long term idea is to use this to minimise the number\n * of DOM reads, and to abstract the DOM interactions here.\n */\nclass MotionValue {\n  setAnimation(animation) {\n    this.animation = animation;\n    animation === null || animation === void 0 ? void 0 : animation.finished.then(() => this.clearAnimation()).catch(() => {});\n  }\n  clearAnimation() {\n    this.animation = this.generator = undefined;\n  }\n}\nexport { MotionValue };", "map": {"version": 3, "names": ["MotionValue", "setAnimation", "animation", "finished", "then", "clearAnimation", "catch", "generator", "undefined"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/types/dist/MotionValue.es.js"], "sourcesContent": ["/**\n * The MotionValue tracks the state of a single animatable\n * value. Currently, updatedAt and current are unused. The\n * long term idea is to use this to minimise the number\n * of DOM reads, and to abstract the DOM interactions here.\n */\nclass MotionValue {\n    setAnimation(animation) {\n        this.animation = animation;\n        animation === null || animation === void 0 ? void 0 : animation.finished.then(() => this.clearAnimation()).catch(() => { });\n    }\n    clearAnimation() {\n        this.animation = this.generator = undefined;\n    }\n}\n\nexport { MotionValue };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,WAAW,CAAC;EACdC,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1BA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EAC/H;EACAD,cAAcA,CAAA,EAAG;IACb,IAAI,CAACH,SAAS,GAAG,IAAI,CAACK,SAAS,GAAGC,SAAS;EAC/C;AACJ;AAEA,SAASR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}