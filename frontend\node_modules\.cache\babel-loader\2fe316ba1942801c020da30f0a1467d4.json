{"ast": null, "code": "import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nfunction makeState({\n  scrapeMotionValuesFromProps,\n  createRenderState,\n  onMount\n}, props, context, presenceContext) {\n  const state = {\n    latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n    renderState: createRenderState()\n  };\n  if (onMount) {\n    state.mount = instance => onMount(props, instance, state);\n  }\n  return state;\n}\nconst makeUseVisualState = config => (props, isStatic) => {\n  const context = useContext(MotionContext);\n  const presenceContext = useContext(PresenceContext);\n  const make = () => makeState(config, props, context, presenceContext);\n  return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n  const values = {};\n  const motionValues = scrapeMotionValues(props);\n  for (const key in motionValues) {\n    values[key] = resolveMotionValue(motionValues[key]);\n  }\n  let {\n    initial,\n    animate\n  } = props;\n  const isControllingVariants$1 = isControllingVariants(props);\n  const isVariantNode$1 = isVariantNode(props);\n  if (context && isVariantNode$1 && !isControllingVariants$1 && props.inherit !== false) {\n    if (initial === undefined) initial = context.initial;\n    if (animate === undefined) animate = context.animate;\n  }\n  let isInitialAnimationBlocked = presenceContext ? presenceContext.initial === false : false;\n  isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n  const variantToSet = isInitialAnimationBlocked ? animate : initial;\n  if (variantToSet && typeof variantToSet !== \"boolean\" && !isAnimationControls(variantToSet)) {\n    const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n    list.forEach(definition => {\n      const resolved = resolveVariantFromProps(props, definition);\n      if (!resolved) return;\n      const {\n        transitionEnd,\n        transition,\n        ...target\n      } = resolved;\n      for (const key in target) {\n        let valueTarget = target[key];\n        if (Array.isArray(valueTarget)) {\n          /**\n           * Take final keyframe if the initial animation is blocked because\n           * we want to initialise at the end of that blocked animation.\n           */\n          const index = isInitialAnimationBlocked ? valueTarget.length - 1 : 0;\n          valueTarget = valueTarget[index];\n        }\n        if (valueTarget !== null) {\n          values[key] = valueTarget;\n        }\n      }\n      for (const key in transitionEnd) values[key] = transitionEnd[key];\n    });\n  }\n  return values;\n}\nexport { makeUseVisualState };", "map": {"version": 3, "names": ["useContext", "isAnimationControls", "PresenceContext", "resolveVariantFromProps", "useConstant", "resolveMotionValue", "MotionContext", "isControllingVariants", "isVariantNode", "makeState", "scrapeMotionValuesFromProps", "createRenderState", "onMount", "props", "context", "presenceContext", "state", "latestValues", "makeLatestValues", "renderState", "mount", "instance", "makeUseVisualState", "config", "isStatic", "make", "scrapeMotionValues", "values", "motionValues", "key", "initial", "animate", "isControllingVariants$1", "isVariantNode$1", "inherit", "undefined", "isInitialAnimationBlocked", "variantToSet", "list", "Array", "isArray", "for<PERSON>ach", "definition", "resolved", "transitionEnd", "transition", "target", "valueTarget", "index", "length"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, onMount, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    if (onMount) {\n        state.mount = (instance) => onMount(props, instance, state);\n    }\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = useContext(MotionContext);\n    const presenceContext = useContext(PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props);\n    for (const key in motionValues) {\n        values[key] = resolveMotionValue(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = isControllingVariants(props);\n    const isVariantNode$1 = isVariantNode(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !isAnimationControls(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        list.forEach((definition) => {\n            const resolved = resolveVariantFromProps(props, definition);\n            if (!resolved)\n                return;\n            const { transitionEnd, transition, ...target } = resolved;\n            for (const key in target) {\n                let valueTarget = target[key];\n                if (Array.isArray(valueTarget)) {\n                    /**\n                     * Take final keyframe if the initial animation is blocked because\n                     * we want to initialise at the end of that blocked animation.\n                     */\n                    const index = isInitialAnimationBlocked\n                        ? valueTarget.length - 1\n                        : 0;\n                    valueTarget = valueTarget[index];\n                }\n                if (valueTarget !== null) {\n                    values[key] = valueTarget;\n                }\n            }\n            for (const key in transitionEnd)\n                values[key] = transitionEnd[key];\n        });\n    }\n    return values;\n}\n\nexport { makeUseVisualState };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,gDAAgD;AAErG,SAASC,SAASA,CAAC;EAAEC,2BAA2B;EAAEC,iBAAiB;EAAEC;AAAS,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAE;EAC9G,MAAMC,KAAK,GAAG;IACVC,YAAY,EAAEC,gBAAgB,CAACL,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEL,2BAA2B,CAAC;IAC5FS,WAAW,EAAER,iBAAiB,CAAC;EACnC,CAAC;EACD,IAAIC,OAAO,EAAE;IACTI,KAAK,CAACI,KAAK,GAAIC,QAAQ,IAAKT,OAAO,CAACC,KAAK,EAAEQ,QAAQ,EAAEL,KAAK,CAAC;EAC/D;EACA,OAAOA,KAAK;AAChB;AACA,MAAMM,kBAAkB,GAAIC,MAAM,IAAK,CAACV,KAAK,EAAEW,QAAQ,KAAK;EACxD,MAAMV,OAAO,GAAGd,UAAU,CAACM,aAAa,CAAC;EACzC,MAAMS,eAAe,GAAGf,UAAU,CAACE,eAAe,CAAC;EACnD,MAAMuB,IAAI,GAAGA,CAAA,KAAMhB,SAAS,CAACc,MAAM,EAAEV,KAAK,EAAEC,OAAO,EAAEC,eAAe,CAAC;EACrE,OAAOS,QAAQ,GAAGC,IAAI,CAAC,CAAC,GAAGrB,WAAW,CAACqB,IAAI,CAAC;AAChD,CAAC;AACD,SAASP,gBAAgBA,CAACL,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEW,kBAAkB,EAAE;EAC3E,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAGF,kBAAkB,CAACb,KAAK,CAAC;EAC9C,KAAK,MAAMgB,GAAG,IAAID,YAAY,EAAE;IAC5BD,MAAM,CAACE,GAAG,CAAC,GAAGxB,kBAAkB,CAACuB,YAAY,CAACC,GAAG,CAAC,CAAC;EACvD;EACA,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGlB,KAAK;EAChC,MAAMmB,uBAAuB,GAAGzB,qBAAqB,CAACM,KAAK,CAAC;EAC5D,MAAMoB,eAAe,GAAGzB,aAAa,CAACK,KAAK,CAAC;EAC5C,IAAIC,OAAO,IACPmB,eAAe,IACf,CAACD,uBAAuB,IACxBnB,KAAK,CAACqB,OAAO,KAAK,KAAK,EAAE;IACzB,IAAIJ,OAAO,KAAKK,SAAS,EACrBL,OAAO,GAAGhB,OAAO,CAACgB,OAAO;IAC7B,IAAIC,OAAO,KAAKI,SAAS,EACrBJ,OAAO,GAAGjB,OAAO,CAACiB,OAAO;EACjC;EACA,IAAIK,yBAAyB,GAAGrB,eAAe,GACzCA,eAAe,CAACe,OAAO,KAAK,KAAK,GACjC,KAAK;EACXM,yBAAyB,GAAGA,yBAAyB,IAAIN,OAAO,KAAK,KAAK;EAC1E,MAAMO,YAAY,GAAGD,yBAAyB,GAAGL,OAAO,GAAGD,OAAO;EAClE,IAAIO,YAAY,IACZ,OAAOA,YAAY,KAAK,SAAS,IACjC,CAACpC,mBAAmB,CAACoC,YAAY,CAAC,EAAE;IACpC,MAAMC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IACxEC,IAAI,CAACG,OAAO,CAAEC,UAAU,IAAK;MACzB,MAAMC,QAAQ,GAAGxC,uBAAuB,CAACU,KAAK,EAAE6B,UAAU,CAAC;MAC3D,IAAI,CAACC,QAAQ,EACT;MACJ,MAAM;QAAEC,aAAa;QAAEC,UAAU;QAAE,GAAGC;MAAO,CAAC,GAAGH,QAAQ;MACzD,KAAK,MAAMd,GAAG,IAAIiB,MAAM,EAAE;QACtB,IAAIC,WAAW,GAAGD,MAAM,CAACjB,GAAG,CAAC;QAC7B,IAAIU,KAAK,CAACC,OAAO,CAACO,WAAW,CAAC,EAAE;UAC5B;AACpB;AACA;AACA;UACoB,MAAMC,KAAK,GAAGZ,yBAAyB,GACjCW,WAAW,CAACE,MAAM,GAAG,CAAC,GACtB,CAAC;UACPF,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC;QACpC;QACA,IAAID,WAAW,KAAK,IAAI,EAAE;UACtBpB,MAAM,CAACE,GAAG,CAAC,GAAGkB,WAAW;QAC7B;MACJ;MACA,KAAK,MAAMlB,GAAG,IAAIe,aAAa,EAC3BjB,MAAM,CAACE,GAAG,CAAC,GAAGe,aAAa,CAACf,GAAG,CAAC;IACxC,CAAC,CAAC;EACN;EACA,OAAOF,MAAM;AACjB;AAEA,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}