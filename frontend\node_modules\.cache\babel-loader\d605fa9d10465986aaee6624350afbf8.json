{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar stopAnimation = require('./stop-animation.cjs.js');\nconst createAnimation = factory => factory();\nconst withControls = (animationFactory, options, duration = utils.defaults.duration) => {\n  return new Proxy({\n    animations: animationFactory.map(createAnimation).filter(Boolean),\n    duration,\n    options\n  }, controls);\n};\n/**\n * TODO:\n * Currently this returns the first animation, ideally it would return\n * the first active animation.\n */\nconst getActiveAnimation = state => state.animations[0];\nconst controls = {\n  get: (target, key) => {\n    const activeAnimation = getActiveAnimation(target);\n    switch (key) {\n      case \"duration\":\n        return target.duration;\n      case \"currentTime\":\n        return utils.time.s((activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) || 0);\n      case \"playbackRate\":\n      case \"playState\":\n        return activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key];\n      case \"finished\":\n        if (!target.finished) {\n          target.finished = Promise.all(target.animations.map(selectFinished)).catch(utils.noop);\n        }\n        return target.finished;\n      case \"stop\":\n        return () => {\n          target.animations.forEach(animation => stopAnimation.stopAnimation(animation));\n        };\n      case \"forEachNative\":\n        /**\n         * This is for internal use only, fire a callback for each\n         * underlying animation.\n         */\n        return callback => {\n          target.animations.forEach(animation => callback(animation, target));\n        };\n      default:\n        return typeof (activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) === \"undefined\" ? undefined : () => target.animations.forEach(animation => animation[key]());\n    }\n  },\n  set: (target, key, value) => {\n    switch (key) {\n      case \"currentTime\":\n        value = utils.time.ms(value);\n      // Fall-through\n      case \"playbackRate\":\n        for (let i = 0; i < target.animations.length; i++) {\n          target.animations[i][key] = value;\n        }\n        return true;\n    }\n    return false;\n  }\n};\nconst selectFinished = animation => animation.finished;\nexports.controls = controls;\nexports.withControls = withControls;", "map": {"version": 3, "names": ["utils", "require", "stopAnimation", "createAnimation", "factory", "withControls", "animationFactory", "options", "duration", "defaults", "Proxy", "animations", "map", "filter", "Boolean", "controls", "getActiveAnimation", "state", "get", "target", "key", "activeAnimation", "time", "s", "finished", "Promise", "all", "selectFinished", "catch", "noop", "for<PERSON>ach", "animation", "callback", "undefined", "set", "value", "ms", "i", "length", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/controls.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar stopAnimation = require('./stop-animation.cjs.js');\n\nconst createAnimation = (factory) => factory();\nconst withControls = (animationFactory, options, duration = utils.defaults.duration) => {\n    return new Proxy({\n        animations: animationFactory.map(createAnimation).filter(Boolean),\n        duration,\n        options,\n    }, controls);\n};\n/**\n * TODO:\n * Currently this returns the first animation, ideally it would return\n * the first active animation.\n */\nconst getActiveAnimation = (state) => state.animations[0];\nconst controls = {\n    get: (target, key) => {\n        const activeAnimation = getActiveAnimation(target);\n        switch (key) {\n            case \"duration\":\n                return target.duration;\n            case \"currentTime\":\n                return utils.time.s((activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) || 0);\n            case \"playbackRate\":\n            case \"playState\":\n                return activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key];\n            case \"finished\":\n                if (!target.finished) {\n                    target.finished = Promise.all(target.animations.map(selectFinished)).catch(utils.noop);\n                }\n                return target.finished;\n            case \"stop\":\n                return () => {\n                    target.animations.forEach((animation) => stopAnimation.stopAnimation(animation));\n                };\n            case \"forEachNative\":\n                /**\n                 * This is for internal use only, fire a callback for each\n                 * underlying animation.\n                 */\n                return (callback) => {\n                    target.animations.forEach((animation) => callback(animation, target));\n                };\n            default:\n                return typeof (activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) ===\n                    \"undefined\"\n                    ? undefined\n                    : () => target.animations.forEach((animation) => animation[key]());\n        }\n    },\n    set: (target, key, value) => {\n        switch (key) {\n            case \"currentTime\":\n                value = utils.time.ms(value);\n            // Fall-through\n            case \"playbackRate\":\n                for (let i = 0; i < target.animations.length; i++) {\n                    target.animations[i][key] = value;\n                }\n                return true;\n        }\n        return false;\n    },\n};\nconst selectFinished = (animation) => animation.finished;\n\nexports.controls = controls;\nexports.withControls = withControls;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,aAAa,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAEtD,MAAME,eAAe,GAAIC,OAAO,IAAKA,OAAO,CAAC,CAAC;AAC9C,MAAMC,YAAY,GAAGA,CAACC,gBAAgB,EAAEC,OAAO,EAAEC,QAAQ,GAAGR,KAAK,CAACS,QAAQ,CAACD,QAAQ,KAAK;EACpF,OAAO,IAAIE,KAAK,CAAC;IACbC,UAAU,EAAEL,gBAAgB,CAACM,GAAG,CAACT,eAAe,CAAC,CAACU,MAAM,CAACC,OAAO,CAAC;IACjEN,QAAQ;IACRD;EACJ,CAAC,EAAEQ,QAAQ,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAKA,KAAK,CAACN,UAAU,CAAC,CAAC,CAAC;AACzD,MAAMI,QAAQ,GAAG;EACbG,GAAG,EAAEA,CAACC,MAAM,EAAEC,GAAG,KAAK;IAClB,MAAMC,eAAe,GAAGL,kBAAkB,CAACG,MAAM,CAAC;IAClD,QAAQC,GAAG;MACP,KAAK,UAAU;QACX,OAAOD,MAAM,CAACX,QAAQ;MAC1B,KAAK,aAAa;QACd,OAAOR,KAAK,CAACsB,IAAI,CAACC,CAAC,CAAC,CAACF,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;MACtH,KAAK,cAAc;MACnB,KAAK,WAAW;QACZ,OAAOC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACD,GAAG,CAAC;MACjG,KAAK,UAAU;QACX,IAAI,CAACD,MAAM,CAACK,QAAQ,EAAE;UAClBL,MAAM,CAACK,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACP,MAAM,CAACR,UAAU,CAACC,GAAG,CAACe,cAAc,CAAC,CAAC,CAACC,KAAK,CAAC5B,KAAK,CAAC6B,IAAI,CAAC;QAC1F;QACA,OAAOV,MAAM,CAACK,QAAQ;MAC1B,KAAK,MAAM;QACP,OAAO,MAAM;UACTL,MAAM,CAACR,UAAU,CAACmB,OAAO,CAAEC,SAAS,IAAK7B,aAAa,CAACA,aAAa,CAAC6B,SAAS,CAAC,CAAC;QACpF,CAAC;MACL,KAAK,eAAe;QAChB;AAChB;AACA;AACA;QACgB,OAAQC,QAAQ,IAAK;UACjBb,MAAM,CAACR,UAAU,CAACmB,OAAO,CAAEC,SAAS,IAAKC,QAAQ,CAACD,SAAS,EAAEZ,MAAM,CAAC,CAAC;QACzE,CAAC;MACL;QACI,OAAO,QAAQE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACD,GAAG,CAAC,CAAC,KAClG,WAAW,GACTa,SAAS,GACT,MAAMd,MAAM,CAACR,UAAU,CAACmB,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAACX,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9E;EACJ,CAAC;EACDc,GAAG,EAAEA,CAACf,MAAM,EAAEC,GAAG,EAAEe,KAAK,KAAK;IACzB,QAAQf,GAAG;MACP,KAAK,aAAa;QACde,KAAK,GAAGnC,KAAK,CAACsB,IAAI,CAACc,EAAE,CAACD,KAAK,CAAC;MAChC;MACA,KAAK,cAAc;QACf,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,MAAM,CAACR,UAAU,CAAC2B,MAAM,EAAED,CAAC,EAAE,EAAE;UAC/ClB,MAAM,CAACR,UAAU,CAAC0B,CAAC,CAAC,CAACjB,GAAG,CAAC,GAAGe,KAAK;QACrC;QACA,OAAO,IAAI;IACnB;IACA,OAAO,KAAK;EAChB;AACJ,CAAC;AACD,MAAMR,cAAc,GAAII,SAAS,IAAKA,SAAS,CAACP,QAAQ;AAExDe,OAAO,CAACxB,QAAQ,GAAGA,QAAQ;AAC3BwB,OAAO,CAAClC,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}