{"ast": null, "code": "import { useEffect, useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { usePresence } from '../../components/AnimatePresence/use-presence.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { createAnimationState } from '../../render/utils/animation-state.mjs';\nimport { AnimationType } from '../../render/utils/types.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\nconst animations = {\n  animation: makeRenderlessComponent(({\n    visualElement,\n    animate\n  }) => {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    visualElement.animationState || (visualElement.animationState = createAnimationState(visualElement));\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    if (isAnimationControls(animate)) {\n      useEffect(() => animate.subscribe(visualElement), [animate]);\n    }\n  }),\n  exit: makeRenderlessComponent(props => {\n    const {\n      custom,\n      visualElement\n    } = props;\n    const [isPresent, safeToRemove] = usePresence();\n    const presenceContext = useContext(PresenceContext);\n    useEffect(() => {\n      visualElement.isPresent = isPresent;\n      const animation = visualElement.animationState && visualElement.animationState.setActive(AnimationType.Exit, !isPresent, {\n        custom: presenceContext && presenceContext.custom || custom\n      });\n      if (animation && !isPresent) {\n        animation.then(safeToRemove);\n      }\n    }, [isPresent]);\n  })\n};\nexport { animations };", "map": {"version": 3, "names": ["useEffect", "useContext", "isAnimationControls", "usePresence", "PresenceContext", "createAnimationState", "AnimationType", "makeRenderlessComponent", "animations", "animation", "visualElement", "animate", "animationState", "subscribe", "exit", "props", "custom", "isPresent", "safeToRemove", "presenceContext", "setActive", "Exit", "then"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/animations.mjs"], "sourcesContent": ["import { useEffect, useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { usePresence } from '../../components/AnimatePresence/use-presence.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { createAnimationState } from '../../render/utils/animation-state.mjs';\nimport { AnimationType } from '../../render/utils/types.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\n\nconst animations = {\n    animation: makeRenderlessComponent(({ visualElement, animate }) => {\n        /**\n         * We dynamically generate the AnimationState manager as it contains a reference\n         * to the underlying animation library. We only want to load that if we load this,\n         * so people can optionally code split it out using the `m` component.\n         */\n        visualElement.animationState || (visualElement.animationState = createAnimationState(visualElement));\n        /**\n         * Subscribe any provided AnimationControls to the component's VisualElement\n         */\n        if (isAnimationControls(animate)) {\n            useEffect(() => animate.subscribe(visualElement), [animate]);\n        }\n    }),\n    exit: makeRenderlessComponent((props) => {\n        const { custom, visualElement } = props;\n        const [isPresent, safeToRemove] = usePresence();\n        const presenceContext = useContext(PresenceContext);\n        useEffect(() => {\n            visualElement.isPresent = isPresent;\n            const animation = visualElement.animationState &&\n                visualElement.animationState.setActive(AnimationType.Exit, !isPresent, {\n                    custom: (presenceContext && presenceContext.custom) ||\n                        custom,\n                });\n            if (animation && !isPresent) {\n                animation.then(safeToRemove);\n            }\n        }, [isPresent]);\n    }),\n};\n\nexport { animations };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC7C,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,WAAW,QAAQ,mDAAmD;AAC/E,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,MAAMC,UAAU,GAAG;EACfC,SAAS,EAAEF,uBAAuB,CAAC,CAAC;IAAEG,aAAa;IAAEC;EAAQ,CAAC,KAAK;IAC/D;AACR;AACA;AACA;AACA;IACQD,aAAa,CAACE,cAAc,KAAKF,aAAa,CAACE,cAAc,GAAGP,oBAAoB,CAACK,aAAa,CAAC,CAAC;IACpG;AACR;AACA;IACQ,IAAIR,mBAAmB,CAACS,OAAO,CAAC,EAAE;MAC9BX,SAAS,CAAC,MAAMW,OAAO,CAACE,SAAS,CAACH,aAAa,CAAC,EAAE,CAACC,OAAO,CAAC,CAAC;IAChE;EACJ,CAAC,CAAC;EACFG,IAAI,EAAEP,uBAAuB,CAAEQ,KAAK,IAAK;IACrC,MAAM;MAAEC,MAAM;MAAEN;IAAc,CAAC,GAAGK,KAAK;IACvC,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGf,WAAW,CAAC,CAAC;IAC/C,MAAMgB,eAAe,GAAGlB,UAAU,CAACG,eAAe,CAAC;IACnDJ,SAAS,CAAC,MAAM;MACZU,aAAa,CAACO,SAAS,GAAGA,SAAS;MACnC,MAAMR,SAAS,GAAGC,aAAa,CAACE,cAAc,IAC1CF,aAAa,CAACE,cAAc,CAACQ,SAAS,CAACd,aAAa,CAACe,IAAI,EAAE,CAACJ,SAAS,EAAE;QACnED,MAAM,EAAGG,eAAe,IAAIA,eAAe,CAACH,MAAM,IAC9CA;MACR,CAAC,CAAC;MACN,IAAIP,SAAS,IAAI,CAACQ,SAAS,EAAE;QACzBR,SAAS,CAACa,IAAI,CAACJ,YAAY,CAAC;MAChC;IACJ,CAAC,EAAE,CAACD,SAAS,CAAC,CAAC;EACnB,CAAC;AACL,CAAC;AAED,SAAST,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}