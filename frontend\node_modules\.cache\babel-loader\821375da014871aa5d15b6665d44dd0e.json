{"ast": null, "code": "import { color } from '../color/index.mjs';\nimport { number } from '../numbers/index.mjs';\nimport { isString, floatRegex, colorRegex, sanitize } from '../utils.mjs';\nconst colorToken = \"${c}\";\nconst numberToken = \"${n}\";\nfunction test(v) {\n  var _a, _b;\n  return isNaN(v) && isString(v) && (((_a = v.match(floatRegex)) === null || _a === void 0 ? void 0 : _a.length) || 0) + (((_b = v.match(colorRegex)) === null || _b === void 0 ? void 0 : _b.length) || 0) > 0;\n}\nfunction analyseComplexValue(v) {\n  if (typeof v === \"number\") v = `${v}`;\n  const values = [];\n  let numColors = 0;\n  let numNumbers = 0;\n  const colors = v.match(colorRegex);\n  if (colors) {\n    numColors = colors.length;\n    // Strip colors from input so they're not picked up by number regex.\n    // There's a better way to combine these regex searches, but its beyond my regex skills\n    v = v.replace(colorRegex, colorToken);\n    values.push(...colors.map(color.parse));\n  }\n  const numbers = v.match(floatRegex);\n  if (numbers) {\n    numNumbers = numbers.length;\n    v = v.replace(floatRegex, numberToken);\n    values.push(...numbers.map(number.parse));\n  }\n  return {\n    values,\n    numColors,\n    numNumbers,\n    tokenised: v\n  };\n}\nfunction parse(v) {\n  return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n  const {\n    values,\n    numColors,\n    tokenised\n  } = analyseComplexValue(source);\n  const numValues = values.length;\n  return v => {\n    let output = tokenised;\n    for (let i = 0; i < numValues; i++) {\n      output = output.replace(i < numColors ? colorToken : numberToken, i < numColors ? color.transform(v[i]) : sanitize(v[i]));\n    }\n    return output;\n  };\n}\nconst convertNumbersToZero = v => typeof v === \"number\" ? 0 : v;\nfunction getAnimatableNone(v) {\n  const parsed = parse(v);\n  const transformer = createTransformer(v);\n  return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n  test,\n  parse,\n  createTransformer,\n  getAnimatableNone\n};\nexport { analyseComplexValue, complex };", "map": {"version": 3, "names": ["color", "number", "isString", "floatRegex", "colorRegex", "sanitize", "colorToken", "numberToken", "test", "v", "_a", "_b", "isNaN", "match", "length", "analyseComplexValue", "values", "numColors", "numNumbers", "colors", "replace", "push", "map", "parse", "numbers", "tokenised", "createTransformer", "source", "numValues", "output", "i", "transform", "convertNumbersToZero", "getAnimatableNone", "parsed", "transformer", "complex"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/value/types/complex/index.mjs"], "sourcesContent": ["import { color } from '../color/index.mjs';\nimport { number } from '../numbers/index.mjs';\nimport { isString, floatRegex, colorRegex, sanitize } from '../utils.mjs';\n\nconst colorToken = \"${c}\";\nconst numberToken = \"${n}\";\nfunction test(v) {\n    var _a, _b;\n    return (isNaN(v) &&\n        isString(v) &&\n        (((_a = v.match(floatRegex)) === null || _a === void 0 ? void 0 : _a.length) || 0) +\n            (((_b = v.match(colorRegex)) === null || _b === void 0 ? void 0 : _b.length) || 0) >\n            0);\n}\nfunction analyseComplexValue(v) {\n    if (typeof v === \"number\")\n        v = `${v}`;\n    const values = [];\n    let numColors = 0;\n    let numNumbers = 0;\n    const colors = v.match(colorRegex);\n    if (colors) {\n        numColors = colors.length;\n        // Strip colors from input so they're not picked up by number regex.\n        // There's a better way to combine these regex searches, but its beyond my regex skills\n        v = v.replace(colorRegex, colorToken);\n        values.push(...colors.map(color.parse));\n    }\n    const numbers = v.match(floatRegex);\n    if (numbers) {\n        numNumbers = numbers.length;\n        v = v.replace(floatRegex, numberToken);\n        values.push(...numbers.map(number.parse));\n    }\n    return { values, numColors, numNumbers, tokenised: v };\n}\nfunction parse(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { values, numColors, tokenised } = analyseComplexValue(source);\n    const numValues = values.length;\n    return (v) => {\n        let output = tokenised;\n        for (let i = 0; i < numValues; i++) {\n            output = output.replace(i < numColors ? colorToken : numberToken, i < numColors\n                ? color.transform(v[i])\n                : sanitize(v[i]));\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : v;\nfunction getAnimatableNone(v) {\n    const parsed = parse(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = { test, parse, createTransformer, getAnimatableNone };\n\nexport { analyseComplexValue, complex };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,cAAc;AAEzE,MAAMC,UAAU,GAAG,MAAM;AACzB,MAAMC,WAAW,GAAG,MAAM;AAC1B,SAASC,IAAIA,CAACC,CAAC,EAAE;EACb,IAAIC,EAAE,EAAEC,EAAE;EACV,OAAQC,KAAK,CAACH,CAAC,CAAC,IACZP,QAAQ,CAACO,CAAC,CAAC,IACX,CAAC,CAAC,CAACC,EAAE,GAAGD,CAAC,CAACI,KAAK,CAACV,UAAU,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM,KAAK,CAAC,KAC5E,CAAC,CAACH,EAAE,GAAGF,CAAC,CAACI,KAAK,CAACT,UAAU,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM,KAAK,CAAC,CAAC,GAClF,CAAC;AACb;AACA,SAASC,mBAAmBA,CAACN,CAAC,EAAE;EAC5B,IAAI,OAAOA,CAAC,KAAK,QAAQ,EACrBA,CAAC,GAAG,GAAGA,CAAC,EAAE;EACd,MAAMO,MAAM,GAAG,EAAE;EACjB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,UAAU,GAAG,CAAC;EAClB,MAAMC,MAAM,GAAGV,CAAC,CAACI,KAAK,CAACT,UAAU,CAAC;EAClC,IAAIe,MAAM,EAAE;IACRF,SAAS,GAAGE,MAAM,CAACL,MAAM;IACzB;IACA;IACAL,CAAC,GAAGA,CAAC,CAACW,OAAO,CAAChB,UAAU,EAAEE,UAAU,CAAC;IACrCU,MAAM,CAACK,IAAI,CAAC,GAAGF,MAAM,CAACG,GAAG,CAACtB,KAAK,CAACuB,KAAK,CAAC,CAAC;EAC3C;EACA,MAAMC,OAAO,GAAGf,CAAC,CAACI,KAAK,CAACV,UAAU,CAAC;EACnC,IAAIqB,OAAO,EAAE;IACTN,UAAU,GAAGM,OAAO,CAACV,MAAM;IAC3BL,CAAC,GAAGA,CAAC,CAACW,OAAO,CAACjB,UAAU,EAAEI,WAAW,CAAC;IACtCS,MAAM,CAACK,IAAI,CAAC,GAAGG,OAAO,CAACF,GAAG,CAACrB,MAAM,CAACsB,KAAK,CAAC,CAAC;EAC7C;EACA,OAAO;IAAEP,MAAM;IAAEC,SAAS;IAAEC,UAAU;IAAEO,SAAS,EAAEhB;EAAE,CAAC;AAC1D;AACA,SAASc,KAAKA,CAACd,CAAC,EAAE;EACd,OAAOM,mBAAmB,CAACN,CAAC,CAAC,CAACO,MAAM;AACxC;AACA,SAASU,iBAAiBA,CAACC,MAAM,EAAE;EAC/B,MAAM;IAAEX,MAAM;IAAEC,SAAS;IAAEQ;EAAU,CAAC,GAAGV,mBAAmB,CAACY,MAAM,CAAC;EACpE,MAAMC,SAAS,GAAGZ,MAAM,CAACF,MAAM;EAC/B,OAAQL,CAAC,IAAK;IACV,IAAIoB,MAAM,GAAGJ,SAAS;IACtB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;MAChCD,MAAM,GAAGA,MAAM,CAACT,OAAO,CAACU,CAAC,GAAGb,SAAS,GAAGX,UAAU,GAAGC,WAAW,EAAEuB,CAAC,GAAGb,SAAS,GACzEjB,KAAK,CAAC+B,SAAS,CAACtB,CAAC,CAACqB,CAAC,CAAC,CAAC,GACrBzB,QAAQ,CAACI,CAAC,CAACqB,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,OAAOD,MAAM;EACjB,CAAC;AACL;AACA,MAAMG,oBAAoB,GAAIvB,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAGA,CAAC;AACjE,SAASwB,iBAAiBA,CAACxB,CAAC,EAAE;EAC1B,MAAMyB,MAAM,GAAGX,KAAK,CAACd,CAAC,CAAC;EACvB,MAAM0B,WAAW,GAAGT,iBAAiB,CAACjB,CAAC,CAAC;EACxC,OAAO0B,WAAW,CAACD,MAAM,CAACZ,GAAG,CAACU,oBAAoB,CAAC,CAAC;AACxD;AACA,MAAMI,OAAO,GAAG;EAAE5B,IAAI;EAAEe,KAAK;EAAEG,iBAAiB;EAAEO;AAAkB,CAAC;AAErE,SAASlB,mBAAmB,EAAEqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}