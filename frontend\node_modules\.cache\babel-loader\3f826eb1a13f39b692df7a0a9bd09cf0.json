{"ast": null, "code": "'use strict';\n\nvar $isNaN = require('./isNaN');\n\n/** @type {import('./sign')} */\nmodule.exports = function sign(number) {\n  if ($isNaN(number) || number === 0) {\n    return number;\n  }\n  return number < 0 ? -1 : +1;\n};", "map": {"version": 3, "names": ["$isNaN", "require", "module", "exports", "sign", "number"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/math-intrinsics/sign.js"], "sourcesContent": ["'use strict';\n\nvar $isNaN = require('./isNaN');\n\n/** @type {import('./sign')} */\nmodule.exports = function sign(number) {\n\tif ($isNaN(number) || number === 0) {\n\t\treturn number;\n\t}\n\treturn number < 0 ? -1 : +1;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAE/B;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,MAAM,EAAE;EACtC,IAAIL,MAAM,CAACK,MAAM,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACnC,OAAOA,MAAM;EACd;EACA,OAAOA,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}