{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nfunction getUnitConverter(keyframes, definition) {\n  var _a;\n  let toUnit = (definition === null || definition === void 0 ? void 0 : definition.toDefaultUnit) || utils.noopReturn;\n  const finalKeyframe = keyframes[keyframes.length - 1];\n  if (utils.isString(finalKeyframe)) {\n    const unit = ((_a = finalKeyframe.match(/(-?[\\d.]+)([a-z%]*)/)) === null || _a === void 0 ? void 0 : _a[2]) || \"\";\n    if (unit) toUnit = value => value + unit;\n  }\n  return toUnit;\n}\nexports.getUnitConverter = getUnitConverter;", "map": {"version": 3, "names": ["utils", "require", "getUnitConverter", "keyframes", "definition", "_a", "toUnit", "toDefaultUnit", "noopReturn", "finalKeyframe", "length", "isString", "unit", "match", "value", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/get-unit.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\n\nfunction getUnitConverter(keyframes, definition) {\n    var _a;\n    let toUnit = (definition === null || definition === void 0 ? void 0 : definition.toDefaultUnit) || utils.noopReturn;\n    const finalKeyframe = keyframes[keyframes.length - 1];\n    if (utils.isString(finalKeyframe)) {\n        const unit = ((_a = finalKeyframe.match(/(-?[\\d.]+)([a-z%]*)/)) === null || _a === void 0 ? void 0 : _a[2]) || \"\";\n        if (unit)\n            toUnit = (value) => value + unit;\n    }\n    return toUnit;\n}\n\nexports.getUnitConverter = getUnitConverter;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,UAAU,EAAE;EAC7C,IAAIC,EAAE;EACN,IAAIC,MAAM,GAAG,CAACF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,aAAa,KAAKP,KAAK,CAACQ,UAAU;EACnH,MAAMC,aAAa,GAAGN,SAAS,CAACA,SAAS,CAACO,MAAM,GAAG,CAAC,CAAC;EACrD,IAAIV,KAAK,CAACW,QAAQ,CAACF,aAAa,CAAC,EAAE;IAC/B,MAAMG,IAAI,GAAG,CAAC,CAACP,EAAE,GAAGI,aAAa,CAACI,KAAK,CAAC,qBAAqB,CAAC,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE;IACjH,IAAIO,IAAI,EACJN,MAAM,GAAIQ,KAAK,IAAKA,KAAK,GAAGF,IAAI;EACxC;EACA,OAAON,MAAM;AACjB;AAEAS,OAAO,CAACb,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}