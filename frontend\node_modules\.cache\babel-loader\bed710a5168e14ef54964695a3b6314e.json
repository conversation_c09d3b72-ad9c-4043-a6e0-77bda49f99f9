{"ast": null, "code": "import { complex } from '../../../value/types/complex/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { getDefaultValueType } from './defaults.mjs';\nfunction getAnimatableNone(key, value) {\n  var _a;\n  let defaultValueType = getDefaultValueType(key);\n  if (defaultValueType !== filter) defaultValueType = complex;\n  // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n  return (_a = defaultValueType.getAnimatableNone) === null || _a === void 0 ? void 0 : _a.call(defaultValueType, value);\n}\nexport { getAnimatableNone };", "map": {"version": 3, "names": ["complex", "filter", "getDefaultValueType", "getAnimatableNone", "key", "value", "_a", "defaultValueType", "call"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs"], "sourcesContent": ["import { complex } from '../../../value/types/complex/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { getDefaultValueType } from './defaults.mjs';\n\nfunction getAnimatableNone(key, value) {\n    var _a;\n    let defaultValueType = getDefaultValueType(key);\n    if (defaultValueType !== filter)\n        defaultValueType = complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return (_a = defaultValueType.getAnimatableNone) === null || _a === void 0 ? void 0 : _a.call(defaultValueType, value);\n}\n\nexport { getAnimatableNone };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wCAAwC;AAChE,SAASC,MAAM,QAAQ,yCAAyC;AAChE,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACnC,IAAIC,EAAE;EACN,IAAIC,gBAAgB,GAAGL,mBAAmB,CAACE,GAAG,CAAC;EAC/C,IAAIG,gBAAgB,KAAKN,MAAM,EAC3BM,gBAAgB,GAAGP,OAAO;EAC9B;EACA,OAAO,CAACM,EAAE,GAAGC,gBAAgB,CAACJ,iBAAiB,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACD,gBAAgB,EAAEF,KAAK,CAAC;AAC1H;AAEA,SAASF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}