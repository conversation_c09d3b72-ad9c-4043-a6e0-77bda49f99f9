{"ast": null, "code": "import { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction useOnChange(value, callback) {\n  useIsomorphicLayoutEffect(() => {\n    if (isMotionValue(value)) {\n      callback(value.get());\n      return value.on(\"change\", callback);\n    }\n  }, [value, callback]);\n}\nfunction useMultiOnChange(values, handler, cleanup) {\n  useIsomorphicLayoutEffect(() => {\n    const subscriptions = values.map(value => value.on(\"change\", handler));\n    return () => {\n      subscriptions.forEach(unsubscribe => unsubscribe());\n      cleanup();\n    };\n  });\n}\nexport { useMultiOnChange, useOnChange };", "map": {"version": 3, "names": ["isMotionValue", "useIsomorphicLayoutEffect", "useOnChange", "value", "callback", "get", "on", "useMultiOnChange", "values", "handler", "cleanup", "subscriptions", "map", "for<PERSON>ach", "unsubscribe"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/value/use-on-change.mjs"], "sourcesContent": ["import { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction useOnChange(value, callback) {\n    useIsomorphicLayoutEffect(() => {\n        if (isMotionValue(value)) {\n            callback(value.get());\n            return value.on(\"change\", callback);\n        }\n    }, [value, callback]);\n}\nfunction useMultiOnChange(values, handler, cleanup) {\n    useIsomorphicLayoutEffect(() => {\n        const subscriptions = values.map((value) => value.on(\"change\", handler));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            cleanup();\n        };\n    });\n}\n\nexport { useMultiOnChange, useOnChange };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAClCH,yBAAyB,CAAC,MAAM;IAC5B,IAAID,aAAa,CAACG,KAAK,CAAC,EAAE;MACtBC,QAAQ,CAACD,KAAK,CAACE,GAAG,CAAC,CAAC,CAAC;MACrB,OAAOF,KAAK,CAACG,EAAE,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IACvC;EACJ,CAAC,EAAE,CAACD,KAAK,EAAEC,QAAQ,CAAC,CAAC;AACzB;AACA,SAASG,gBAAgBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAChDT,yBAAyB,CAAC,MAAM;IAC5B,MAAMU,aAAa,GAAGH,MAAM,CAACI,GAAG,CAAET,KAAK,IAAKA,KAAK,CAACG,EAAE,CAAC,QAAQ,EAAEG,OAAO,CAAC,CAAC;IACxE,OAAO,MAAM;MACTE,aAAa,CAACE,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC;MACrDJ,OAAO,CAAC,CAAC;IACb,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAASH,gBAAgB,EAAEL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}