{"ast": null, "code": "'use strict';\n\nvar cssVar = require('./utils/css-var.cjs.js');\nvar getStyleName = require('./utils/get-style-name.cjs.js');\nvar transforms = require('./utils/transforms.cjs.js');\nconst style = {\n  get: (element, name) => {\n    name = getStyleName.getStyleName(name);\n    let value = cssVar.isCssVar(name) ? element.style.getPropertyValue(name) : getComputedStyle(element)[name];\n    // TODO Decide if value can be 0\n    if (!value && value !== 0) {\n      const definition = transforms.transformDefinitions.get(name);\n      if (definition) value = definition.initialValue;\n    }\n    return value;\n  },\n  set: (element, name, value) => {\n    name = getStyleName.getStyleName(name);\n    if (cssVar.isCssVar(name)) {\n      element.style.setProperty(name, value);\n    } else {\n      element.style[name] = value;\n    }\n  }\n};\nexports.style = style;", "map": {"version": 3, "names": ["cssVar", "require", "getStyleName", "transforms", "style", "get", "element", "name", "value", "isCssVar", "getPropertyValue", "getComputedStyle", "definition", "transformDefinitions", "initialValue", "set", "setProperty", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/style.cjs.js"], "sourcesContent": ["'use strict';\n\nvar cssVar = require('./utils/css-var.cjs.js');\nvar getStyleName = require('./utils/get-style-name.cjs.js');\nvar transforms = require('./utils/transforms.cjs.js');\n\nconst style = {\n    get: (element, name) => {\n        name = getStyleName.getStyleName(name);\n        let value = cssVar.isCssVar(name)\n            ? element.style.getPropertyValue(name)\n            : getComputedStyle(element)[name];\n        // TODO Decide if value can be 0\n        if (!value && value !== 0) {\n            const definition = transforms.transformDefinitions.get(name);\n            if (definition)\n                value = definition.initialValue;\n        }\n        return value;\n    },\n    set: (element, name, value) => {\n        name = getStyleName.getStyleName(name);\n        if (cssVar.isCssVar(name)) {\n            element.style.setProperty(name, value);\n        }\n        else {\n            element.style[name] = value;\n        }\n    },\n};\n\nexports.style = style;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC9C,IAAIC,YAAY,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAC3D,IAAIE,UAAU,GAAGF,OAAO,CAAC,2BAA2B,CAAC;AAErD,MAAMG,KAAK,GAAG;EACVC,GAAG,EAAEA,CAACC,OAAO,EAAEC,IAAI,KAAK;IACpBA,IAAI,GAAGL,YAAY,CAACA,YAAY,CAACK,IAAI,CAAC;IACtC,IAAIC,KAAK,GAAGR,MAAM,CAACS,QAAQ,CAACF,IAAI,CAAC,GAC3BD,OAAO,CAACF,KAAK,CAACM,gBAAgB,CAACH,IAAI,CAAC,GACpCI,gBAAgB,CAACL,OAAO,CAAC,CAACC,IAAI,CAAC;IACrC;IACA,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;MACvB,MAAMI,UAAU,GAAGT,UAAU,CAACU,oBAAoB,CAACR,GAAG,CAACE,IAAI,CAAC;MAC5D,IAAIK,UAAU,EACVJ,KAAK,GAAGI,UAAU,CAACE,YAAY;IACvC;IACA,OAAON,KAAK;EAChB,CAAC;EACDO,GAAG,EAAEA,CAACT,OAAO,EAAEC,IAAI,EAAEC,KAAK,KAAK;IAC3BD,IAAI,GAAGL,YAAY,CAACA,YAAY,CAACK,IAAI,CAAC;IACtC,IAAIP,MAAM,CAACS,QAAQ,CAACF,IAAI,CAAC,EAAE;MACvBD,OAAO,CAACF,KAAK,CAACY,WAAW,CAACT,IAAI,EAAEC,KAAK,CAAC;IAC1C,CAAC,MACI;MACDF,OAAO,CAACF,KAAK,CAACG,IAAI,CAAC,GAAGC,KAAK;IAC/B;EACJ;AACJ,CAAC;AAEDS,OAAO,CAACb,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}