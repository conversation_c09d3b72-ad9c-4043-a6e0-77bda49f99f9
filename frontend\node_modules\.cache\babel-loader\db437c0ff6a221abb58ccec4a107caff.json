{"ast": null, "code": "'use strict';\n\nfunction compareByTime(a, b) {\n  if (a.at === b.at) {\n    return a.value === null ? 1 : -1;\n  } else {\n    return a.at - b.at;\n  }\n}\nexports.compareByTime = compareByTime;", "map": {"version": 3, "names": ["compareByTime", "a", "b", "at", "value", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/timeline/utils/sort.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction compareByTime(a, b) {\n    if (a.at === b.at) {\n        return a.value === null ? 1 : -1;\n    }\n    else {\n        return a.at - b.at;\n    }\n}\n\nexports.compareByTime = compareByTime;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAID,CAAC,CAACE,EAAE,KAAKD,CAAC,CAACC,EAAE,EAAE;IACf,OAAOF,CAAC,CAACG,KAAK,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACpC,CAAC,MACI;IACD,OAAOH,CAAC,CAACE,EAAE,GAAGD,CAAC,CAACC,EAAE;EACtB;AACJ;AAEAE,OAAO,CAACL,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}