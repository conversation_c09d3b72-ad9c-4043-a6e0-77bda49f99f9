{"ast": null, "code": "import { useFocusGesture } from '../../gestures/use-focus-gesture.mjs';\nimport { useHoverGesture } from '../../gestures/use-hover-gesture.mjs';\nimport { useTapGesture } from '../../gestures/use-tap-gesture.mjs';\nimport { useViewport } from './viewport/use-viewport.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\nconst gestureAnimations = {\n  inView: makeRenderlessComponent(useViewport),\n  tap: makeRenderlessComponent(useTapGesture),\n  focus: makeRenderlessComponent(useFocusGesture),\n  hover: makeRenderlessComponent(useHoverGesture)\n};\nexport { gestureAnimations };", "map": {"version": 3, "names": ["useFocusGesture", "useHoverGesture", "useTapGesture", "useViewport", "makeRenderlessComponent", "gestureAnimations", "inView", "tap", "focus", "hover"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/gestures.mjs"], "sourcesContent": ["import { useFocusGesture } from '../../gestures/use-focus-gesture.mjs';\nimport { useHoverGesture } from '../../gestures/use-hover-gesture.mjs';\nimport { useTapGesture } from '../../gestures/use-tap-gesture.mjs';\nimport { useViewport } from './viewport/use-viewport.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\n\nconst gestureAnimations = {\n    inView: makeRenderlessComponent(useViewport),\n    tap: makeRenderlessComponent(useTapGesture),\n    focus: makeRenderlessComponent(useFocusGesture),\n    hover: makeRenderlessComponent(useHoverGesture),\n};\n\nexport { gestureAnimations };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sCAAsC;AACtE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,MAAMC,iBAAiB,GAAG;EACtBC,MAAM,EAAEF,uBAAuB,CAACD,WAAW,CAAC;EAC5CI,GAAG,EAAEH,uBAAuB,CAACF,aAAa,CAAC;EAC3CM,KAAK,EAAEJ,uBAAuB,CAACJ,eAAe,CAAC;EAC/CS,KAAK,EAAEL,uBAAuB,CAACH,eAAe;AAClD,CAAC;AAED,SAASI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}