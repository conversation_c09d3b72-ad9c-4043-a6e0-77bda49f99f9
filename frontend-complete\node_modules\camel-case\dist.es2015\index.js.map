{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,UAAU,EACV,mBAAmB,EACnB,wBAAwB,GAEzB,MAAM,aAAa,CAAC;AAIrB,MAAM,UAAU,kBAAkB,CAAC,KAAa,EAAE,KAAa;IAC7D,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;IAC5C,OAAO,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,KAAa,EAAE,KAAa;IAClE,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;IAC5C,OAAO,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC5D,OAAO,UAAU,CAAC,KAAK,aACrB,SAAS,EAAE,kBAAkB,IAC1B,OAAO,EACV,CAAC;AACL,CAAC", "sourcesContent": ["import {\n  pascalCase,\n  pascalCaseTransform,\n  pascalCaseTransformMerge,\n  Options,\n} from \"pascal-case\";\n\nexport { Options };\n\nexport function camelCaseTransform(input: string, index: number) {\n  if (index === 0) return input.toLowerCase();\n  return pascalCaseTransform(input, index);\n}\n\nexport function camelCaseTransformMerge(input: string, index: number) {\n  if (index === 0) return input.toLowerCase();\n  return pascalCaseTransformMerge(input);\n}\n\nexport function camelCase(input: string, options: Options = {}) {\n  return pascalCase(input, {\n    transform: camelCaseTransform,\n    ...options,\n  });\n}\n"]}