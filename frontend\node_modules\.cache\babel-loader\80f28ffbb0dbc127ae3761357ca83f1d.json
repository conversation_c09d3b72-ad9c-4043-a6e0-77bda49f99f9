{"ast": null, "code": "import { getAnimatableNone } from '../../render/dom/value-types/animatable-none.mjs';\n\n/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({\n  when,\n  delay: _delay,\n  delayChildren,\n  staggerChildren,\n  staggerDirection,\n  repeat,\n  repeatType,\n  repeatDelay,\n  from,\n  ...transition\n}) {\n  return !!Object.keys(transition).length;\n}\nfunction isZero(value) {\n  return value === 0 || typeof value === \"string\" && parseFloat(value) === 0 && value.indexOf(\" \") === -1;\n}\nfunction getZeroUnit(potentialUnitType) {\n  return typeof potentialUnitType === \"number\" ? 0 : getAnimatableNone(\"\", potentialUnitType);\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] || transition[\"default\"] || transition;\n}\nexport { getValueTransition, getZeroUnit, isTransitionDefined, isZero };", "map": {"version": 3, "names": ["getAnimatableNone", "isTransitionDefined", "when", "delay", "_delay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "repeat", "repeatType", "repeatDelay", "from", "transition", "Object", "keys", "length", "isZero", "value", "parseFloat", "indexOf", "getZeroUnit", "potentialUnitType", "getValueTransition", "key"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/utils/transitions.mjs"], "sourcesContent": ["import { getAnimatableNone } from '../../render/dom/value-types/animatable-none.mjs';\n\n/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, ...transition }) {\n    return !!Object.keys(transition).length;\n}\nfunction isZero(value) {\n    return (value === 0 ||\n        (typeof value === \"string\" &&\n            parseFloat(value) === 0 &&\n            value.indexOf(\" \") === -1));\n}\nfunction getZeroUnit(potentialUnitType) {\n    return typeof potentialUnitType === \"number\"\n        ? 0\n        : getAnimatableNone(\"\", potentialUnitType);\n}\nfunction getValueTransition(transition, key) {\n    return transition[key] || transition[\"default\"] || transition;\n}\n\nexport { getValueTransition, getZeroUnit, isTransitionDefined, isZero };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kDAAkD;;AAEpF;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAAC;EAAEC,IAAI;EAAEC,KAAK,EAAEC,MAAM;EAAEC,aAAa;EAAEC,eAAe;EAAEC,gBAAgB;EAAEC,MAAM;EAAEC,UAAU;EAAEC,WAAW;EAAEC,IAAI;EAAE,GAAGC;AAAW,CAAC,EAAE;EAC1J,OAAO,CAAC,CAACC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,MAAM;AAC3C;AACA,SAASC,MAAMA,CAACC,KAAK,EAAE;EACnB,OAAQA,KAAK,KAAK,CAAC,IACd,OAAOA,KAAK,KAAK,QAAQ,IACtBC,UAAU,CAACD,KAAK,CAAC,KAAK,CAAC,IACvBA,KAAK,CAACE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAE;AACtC;AACA,SAASC,WAAWA,CAACC,iBAAiB,EAAE;EACpC,OAAO,OAAOA,iBAAiB,KAAK,QAAQ,GACtC,CAAC,GACDrB,iBAAiB,CAAC,EAAE,EAAEqB,iBAAiB,CAAC;AAClD;AACA,SAASC,kBAAkBA,CAACV,UAAU,EAAEW,GAAG,EAAE;EACzC,OAAOX,UAAU,CAACW,GAAG,CAAC,IAAIX,UAAU,CAAC,SAAS,CAAC,IAAIA,UAAU;AACjE;AAEA,SAASU,kBAAkB,EAAEF,WAAW,EAAEnB,mBAAmB,EAAEe,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}