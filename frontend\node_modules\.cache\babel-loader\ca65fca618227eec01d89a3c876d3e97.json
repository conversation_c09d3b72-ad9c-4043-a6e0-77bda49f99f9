{"ast": null, "code": "'use strict';\n\nvar events = require('../utils/events.cjs.js');\nconst mouseEvent = (element, name, action) => event => {\n  if (event.pointerType && event.pointerType !== \"mouse\") return;\n  action();\n  events.dispatchPointerEvent(element, name, event);\n};\nconst hover = {\n  isActive: options => Bo<PERSON>an(options.hover),\n  subscribe: (element, {\n    enable,\n    disable\n  }) => {\n    const onEnter = mouseEvent(element, \"hoverstart\", enable);\n    const onLeave = mouseEvent(element, \"hoverend\", disable);\n    element.addEventListener(\"pointerenter\", onEnter);\n    element.addEventListener(\"pointerleave\", onLeave);\n    return () => {\n      element.removeEventListener(\"pointerenter\", onEnter);\n      element.removeEventListener(\"pointerleave\", onLeave);\n    };\n  }\n};\nexports.hover = hover;", "map": {"version": 3, "names": ["events", "require", "mouseEvent", "element", "name", "action", "event", "pointerType", "dispatchPointerEvent", "hover", "isActive", "options", "Boolean", "subscribe", "enable", "disable", "onEnter", "onLeave", "addEventListener", "removeEventListener", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/gestures/hover.cjs.js"], "sourcesContent": ["'use strict';\n\nvar events = require('../utils/events.cjs.js');\n\nconst mouseEvent = (element, name, action) => (event) => {\n    if (event.pointerType && event.pointerType !== \"mouse\")\n        return;\n    action();\n    events.dispatchPointerEvent(element, name, event);\n};\nconst hover = {\n    isActive: (options) => Boolean(options.hover),\n    subscribe: (element, { enable, disable }) => {\n        const onEnter = mouseEvent(element, \"hoverstart\", enable);\n        const onLeave = mouseEvent(element, \"hoverend\", disable);\n        element.addEventListener(\"pointerenter\", onEnter);\n        element.addEventListener(\"pointerleave\", onLeave);\n        return () => {\n            element.removeEventListener(\"pointerenter\", onEnter);\n            element.removeEventListener(\"pointerleave\", onLeave);\n        };\n    },\n};\n\nexports.hover = hover;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9C,MAAMC,UAAU,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEC,MAAM,KAAMC,KAAK,IAAK;EACrD,IAAIA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACC,WAAW,KAAK,OAAO,EAClD;EACJF,MAAM,CAAC,CAAC;EACRL,MAAM,CAACQ,oBAAoB,CAACL,OAAO,EAAEC,IAAI,EAAEE,KAAK,CAAC;AACrD,CAAC;AACD,MAAMG,KAAK,GAAG;EACVC,QAAQ,EAAGC,OAAO,IAAKC,OAAO,CAACD,OAAO,CAACF,KAAK,CAAC;EAC7CI,SAAS,EAAEA,CAACV,OAAO,EAAE;IAAEW,MAAM;IAAEC;EAAQ,CAAC,KAAK;IACzC,MAAMC,OAAO,GAAGd,UAAU,CAACC,OAAO,EAAE,YAAY,EAAEW,MAAM,CAAC;IACzD,MAAMG,OAAO,GAAGf,UAAU,CAACC,OAAO,EAAE,UAAU,EAAEY,OAAO,CAAC;IACxDZ,OAAO,CAACe,gBAAgB,CAAC,cAAc,EAAEF,OAAO,CAAC;IACjDb,OAAO,CAACe,gBAAgB,CAAC,cAAc,EAAED,OAAO,CAAC;IACjD,OAAO,MAAM;MACTd,OAAO,CAACgB,mBAAmB,CAAC,cAAc,EAAEH,OAAO,CAAC;MACpDb,OAAO,CAACgB,mBAAmB,CAAC,cAAc,EAAEF,OAAO,CAAC;IACxD,CAAC;EACL;AACJ,CAAC;AAEDG,OAAO,CAACX,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}