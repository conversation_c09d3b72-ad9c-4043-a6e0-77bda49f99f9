{"ast": null, "code": "export { MotionValue } from './MotionValue.es.js';", "map": {"version": 3, "names": ["MotionValue"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/types/dist/index.es.js"], "sourcesContent": ["export { MotionValue } from './MotionValue.es.js';\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}