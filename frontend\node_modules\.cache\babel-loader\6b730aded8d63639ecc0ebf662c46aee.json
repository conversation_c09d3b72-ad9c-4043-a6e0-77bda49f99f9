{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar featureDetection = require('./feature-detection.cjs.js');\n\n// Create a linear easing point for every x second\nconst resolution = 0.015;\nconst generateLinearEasingPoints = (easing, duration) => {\n  let points = \"\";\n  const numPoints = Math.round(duration / resolution);\n  for (let i = 0; i < numPoints; i++) {\n    points += easing(utils.progress(0, numPoints - 1, i)) + \", \";\n  }\n  return points.substring(0, points.length - 2);\n};\nconst convertEasing = (easing, duration) => {\n  if (utils.isFunction(easing)) {\n    return featureDetection.supports.linearEasing() ? `linear(${generateLinearEasingPoints(easing, duration)})` : utils.defaults.easing;\n  } else {\n    return utils.isCubicBezier(easing) ? cubicBezierAsString(easing) : easing;\n  }\n};\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nexports.convertEasing = convertEasing;\nexports.cubicBezierAsString = cubicBezierAsString;\nexports.generateLinearEasingPoints = generateLinearEasingPoints;", "map": {"version": 3, "names": ["utils", "require", "featureDetection", "resolution", "generateLinearEasingPoints", "easing", "duration", "points", "numPoints", "Math", "round", "i", "progress", "substring", "length", "convertEasing", "isFunction", "supports", "linearEasing", "defaults", "isCubicBezier", "cubicBezierAsString", "a", "b", "c", "d", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/easing.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar featureDetection = require('./feature-detection.cjs.js');\n\n// Create a linear easing point for every x second\nconst resolution = 0.015;\nconst generateLinearEasingPoints = (easing, duration) => {\n    let points = \"\";\n    const numPoints = Math.round(duration / resolution);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing(utils.progress(0, numPoints - 1, i)) + \", \";\n    }\n    return points.substring(0, points.length - 2);\n};\nconst convertEasing = (easing, duration) => {\n    if (utils.isFunction(easing)) {\n        return featureDetection.supports.linearEasing()\n            ? `linear(${generateLinearEasingPoints(easing, duration)})`\n            : utils.defaults.easing;\n    }\n    else {\n        return utils.isCubicBezier(easing) ? cubicBezierAsString(easing) : easing;\n    }\n};\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexports.convertEasing = convertEasing;\nexports.cubicBezierAsString = cubicBezierAsString;\nexports.generateLinearEasingPoints = generateLinearEasingPoints;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,4BAA4B,CAAC;;AAE5D;AACA,MAAME,UAAU,GAAG,KAAK;AACxB,MAAMC,0BAA0B,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;EACrD,IAAIC,MAAM,GAAG,EAAE;EACf,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,GAAGH,UAAU,CAAC;EACnD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAEG,CAAC,EAAE,EAAE;IAChCJ,MAAM,IAAIF,MAAM,CAACL,KAAK,CAACY,QAAQ,CAAC,CAAC,EAAEJ,SAAS,GAAG,CAAC,EAAEG,CAAC,CAAC,CAAC,GAAG,IAAI;EAChE;EACA,OAAOJ,MAAM,CAACM,SAAS,CAAC,CAAC,EAAEN,MAAM,CAACO,MAAM,GAAG,CAAC,CAAC;AACjD,CAAC;AACD,MAAMC,aAAa,GAAGA,CAACV,MAAM,EAAEC,QAAQ,KAAK;EACxC,IAAIN,KAAK,CAACgB,UAAU,CAACX,MAAM,CAAC,EAAE;IAC1B,OAAOH,gBAAgB,CAACe,QAAQ,CAACC,YAAY,CAAC,CAAC,GACzC,UAAUd,0BAA0B,CAACC,MAAM,EAAEC,QAAQ,CAAC,GAAG,GACzDN,KAAK,CAACmB,QAAQ,CAACd,MAAM;EAC/B,CAAC,MACI;IACD,OAAOL,KAAK,CAACoB,aAAa,CAACf,MAAM,CAAC,GAAGgB,mBAAmB,CAAChB,MAAM,CAAC,GAAGA,MAAM;EAC7E;AACJ,CAAC;AACD,MAAMgB,mBAAmB,GAAGA,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,KAAK,gBAAgBH,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAAG;AAEpFC,OAAO,CAACX,aAAa,GAAGA,aAAa;AACrCW,OAAO,CAACL,mBAAmB,GAAGA,mBAAmB;AACjDK,OAAO,CAACtB,0BAA0B,GAAGA,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}