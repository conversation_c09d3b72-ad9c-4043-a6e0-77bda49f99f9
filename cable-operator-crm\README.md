# Cable Operator Management System (Production-Ready)

## 🚀 Overview
A comprehensive, production-ready Cable Operator CRM System built with modern technologies for managing customers, plans, payments, and business operations.

## 🔧 Tech Stack
- **Frontend**: React.js 18 + TypeScript + TailwindCSS + Framer Motion
- **Backend**: FastAPI (Python) + SQLAlchemy + Pydantic
- **Database**: PostgreSQL (Production) / SQLite (Development)
- **Authentication**: JWT with role-based access control
- **File Storage**: Local + AWS S3 support
- **Email**: SMTP integration for invoices
- **Deployment**: Docker + Docker Compose
- **API Documentation**: Auto-generated with FastAPI

## ✨ Features

### Core Features
- ✅ **Customer Management**: Complete CRUD with search, filter, and status management
- ✅ **Plan Management**: Internet/Cable plans with pricing and descriptions
- ✅ **Payment Management**: Automated billing, payment tracking, and history
- ✅ **Dashboard Analytics**: Revenue, customer stats, and visual charts

### Advanced Features
- 🔑 **Owner Registration**: First-time setup wizard for business configuration
- 👤 **Multi-User System**: Role-based access (Owner, Admin, Staff)
- 🧾 **Invoice Generation**: PDF invoices with email delivery
- 📅 **Automated Billing**: Monthly recurring bills and payment alerts
- 🔍 **Advanced Search**: Smart filtering across all modules
- 💡 **Dark Mode**: Complete theme switching
- 📦 **Data Export**: Excel/CSV export and database backup
- 📱 **Responsive Design**: Mobile-first, desktop-optimized

### Security & Performance
- 🔐 JWT Authentication with refresh tokens
- 🛡️ Role-based permissions
- 📊 API rate limiting
- 🔄 Database migrations
- 📝 Comprehensive logging
- ⚡ Optimized queries and caching

## 🏗️ Project Structure
```
cable-operator-crm/
├── backend/                 # FastAPI Backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Configuration & security
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
│   ├── alembic/            # Database migrations
│   ├── tests/              # Test suite
│   └── requirements.txt
├── frontend/               # React Frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   ├── store/          # State management
│   │   └── utils/          # Utilities
│   ├── public/
│   └── package.json
├── docker/                 # Docker configurations
├── docs/                   # Documentation
└── scripts/               # Deployment scripts
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.9+
- PostgreSQL 13+ (or Docker)
- Git

### Development Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cable-operator-crm
   ```

2. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb cable_operator_db
   
   # Run migrations
   alembic upgrade head
   ```

4. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

5. **Environment Configuration**
   ```bash
   # Backend (.env)
   cp backend/.env.example backend/.env
   
   # Frontend (.env)
   cp frontend/.env.example frontend/.env
   ```

6. **Start Development Servers**
   ```bash
   # Terminal 1: Backend
   cd backend && uvicorn app.main:app --reload
   
   # Terminal 2: Frontend
   cd frontend && npm start
   ```

### Production Deployment (Docker)
```bash
# Build and start all services
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

## 🔑 Default Credentials
- **Super Admin**: <EMAIL> / admin123
- **Demo Owner**: <EMAIL> / demo123

## 📚 API Documentation
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🧪 Testing
```bash
# Backend tests
cd backend && pytest

# Frontend tests
cd frontend && npm test
```

## 📦 Production Features
- **SSL/TLS**: HTTPS configuration
- **Load Balancing**: Nginx reverse proxy
- **Monitoring**: Health checks and metrics
- **Backup**: Automated database backups
- **Logging**: Structured logging with rotation
- **Security**: CORS, rate limiting, input validation

## 🤝 Contributing
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License
This project is licensed under the MIT License.

## 🆘 Support
For support and questions, please contact: <EMAIL>
