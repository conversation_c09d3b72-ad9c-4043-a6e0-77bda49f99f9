"""
Audit log model for tracking system activities
"""

import enum
from datetime import datetime
from sqlalchemy import Column, String, ForeignKey, Integer, DateTime, Text, Enum, JSON
from sqlalchemy.orm import relationship

from .base import BaseModel


class AuditLog(BaseModel):
    """Audit log model for compliance and security tracking"""
    
    __tablename__ = "audit_logs"
    
    # Basic Information
    action = Column(String(100), nullable=False, index=True)
    entity_type = Column(String(50), nullable=False, index=True)
    entity_id = Column(Integer, nullable=True, index=True)
    
    # User Information
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    user_email = Column(String(255), nullable=True)
    user_role = Column(String(50), nullable=True)
    
    # Request Information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    request_method = Column(String(10), nullable=True)
    request_url = Column(String(500), nullable=True)
    
    # Changes
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    changed_fields = Column(JSON, nullable=True)
    
    # Additional Information
    description = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # Company Association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=True, index=True)
    
    # Status
    success = Column(String(10), nullable=False, default="true")  # true, false, partial
    error_message = Column(Text, nullable=True)
    
    # Timestamp (using created_at from BaseModel)
    
    # Relationships
    user = relationship("User")
    company = relationship("Company")
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return ['action', 'entity_type', 'user_email', 'description', 'ip_address']
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return [
            'action', 'entity_type', 'user_id', 'company_id', 
            'success', 'created_at', 'user_role'
        ]
    
    @classmethod
    def log_create(
        cls,
        entity_type: str,
        entity_id: int,
        new_values: dict,
        user_id: int = None,
        user_email: str = None,
        user_role: str = None,
        company_id: int = None,
        ip_address: str = None,
        description: str = None
    ):
        """Log a create action"""
        return cls(
            action="CREATE",
            entity_type=entity_type,
            entity_id=entity_id,
            new_values=new_values,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            description=description or f"Created {entity_type} with ID {entity_id}"
        )
    
    @classmethod
    def log_update(
        cls,
        entity_type: str,
        entity_id: int,
        old_values: dict,
        new_values: dict,
        user_id: int = None,
        user_email: str = None,
        user_role: str = None,
        company_id: int = None,
        ip_address: str = None,
        description: str = None
    ):
        """Log an update action"""
        # Calculate changed fields
        changed_fields = []
        for key in new_values:
            if key in old_values and old_values[key] != new_values[key]:
                changed_fields.append(key)
        
        return cls(
            action="UPDATE",
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=old_values,
            new_values=new_values,
            changed_fields=changed_fields,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            description=description or f"Updated {entity_type} with ID {entity_id}"
        )
    
    @classmethod
    def log_delete(
        cls,
        entity_type: str,
        entity_id: int,
        old_values: dict,
        user_id: int = None,
        user_email: str = None,
        user_role: str = None,
        company_id: int = None,
        ip_address: str = None,
        description: str = None
    ):
        """Log a delete action"""
        return cls(
            action="DELETE",
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=old_values,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            description=description or f"Deleted {entity_type} with ID {entity_id}"
        )
    
    @classmethod
    def log_login(
        cls,
        user_id: int,
        user_email: str,
        user_role: str,
        company_id: int = None,
        ip_address: str = None,
        user_agent: str = None,
        success: bool = True,
        error_message: str = None
    ):
        """Log a login attempt"""
        return cls(
            action="LOGIN",
            entity_type="user",
            entity_id=user_id,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success="true" if success else "false",
            error_message=error_message,
            description=f"User login {'successful' if success else 'failed'}"
        )
    
    @classmethod
    def log_logout(
        cls,
        user_id: int,
        user_email: str,
        user_role: str,
        company_id: int = None,
        ip_address: str = None
    ):
        """Log a logout action"""
        return cls(
            action="LOGOUT",
            entity_type="user",
            entity_id=user_id,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            description="User logout"
        )
    
    @classmethod
    def log_payment(
        cls,
        payment_id: int,
        action: str,  # PAYMENT_CREATED, PAYMENT_UPDATED, PAYMENT_PAID, etc.
        amount: float,
        customer_id: int,
        user_id: int = None,
        user_email: str = None,
        user_role: str = None,
        company_id: int = None,
        ip_address: str = None,
        metadata: dict = None
    ):
        """Log a payment-related action"""
        return cls(
            action=action,
            entity_type="payment",
            entity_id=payment_id,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            metadata={
                "amount": amount,
                "customer_id": customer_id,
                **(metadata or {})
            },
            description=f"Payment action: {action} for amount ${amount:.2f}"
        )
    
    @classmethod
    def log_invoice(
        cls,
        invoice_id: int,
        action: str,  # INVOICE_CREATED, INVOICE_SENT, INVOICE_PAID, etc.
        invoice_number: str,
        amount: float,
        customer_id: int,
        user_id: int = None,
        user_email: str = None,
        user_role: str = None,
        company_id: int = None,
        ip_address: str = None,
        metadata: dict = None
    ):
        """Log an invoice-related action"""
        return cls(
            action=action,
            entity_type="invoice",
            entity_id=invoice_id,
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            metadata={
                "invoice_number": invoice_number,
                "amount": amount,
                "customer_id": customer_id,
                **(metadata or {})
            },
            description=f"Invoice action: {action} for invoice {invoice_number}"
        )
    
    @classmethod
    def log_system_action(
        cls,
        action: str,
        description: str,
        user_id: int = None,
        user_email: str = None,
        user_role: str = None,
        company_id: int = None,
        ip_address: str = None,
        metadata: dict = None,
        success: bool = True,
        error_message: str = None
    ):
        """Log a system-level action"""
        return cls(
            action=action,
            entity_type="system",
            user_id=user_id,
            user_email=user_email,
            user_role=user_role,
            company_id=company_id,
            ip_address=ip_address,
            metadata=metadata,
            success="true" if success else "false",
            error_message=error_message,
            description=description
        )
    
    def set_metadata(self, key: str, value):
        """Set metadata value"""
        if not self.metadata:
            self.metadata = {}
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default=None):
        """Get metadata value"""
        return self.metadata.get(key, default) if self.metadata else default
    
    def is_successful(self) -> bool:
        """Check if the action was successful"""
        return self.success == "true"
    
    def get_user_display(self) -> str:
        """Get display name for the user"""
        if self.user:
            return self.user.full_name
        elif self.user_email:
            return self.user_email
        else:
            return "System"
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, action='{self.action}', entity='{self.entity_type}')>"
