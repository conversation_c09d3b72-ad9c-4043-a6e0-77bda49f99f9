{"ast": null, "code": "'use strict';\n\nconst motionEvent = (name, target) => new CustomEvent(name, {\n  detail: {\n    target\n  }\n});\nfunction dispatchPointerEvent(element, name, event) {\n  element.dispatchEvent(new CustomEvent(name, {\n    detail: {\n      originalEvent: event\n    }\n  }));\n}\nfunction dispatchViewEvent(element, name, entry) {\n  element.dispatchEvent(new CustomEvent(name, {\n    detail: {\n      originalEntry: entry\n    }\n  }));\n}\nexports.dispatchPointerEvent = dispatchPointerEvent;\nexports.dispatchViewEvent = dispatchViewEvent;\nexports.motionEvent = motionEvent;", "map": {"version": 3, "names": ["motionEvent", "name", "target", "CustomEvent", "detail", "dispatchPointerEvent", "element", "event", "dispatchEvent", "originalEvent", "dispatchViewEvent", "entry", "originalEntry", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/utils/events.cjs.js"], "sourcesContent": ["'use strict';\n\nconst motionEvent = (name, target) => new CustomEvent(name, { detail: { target } });\nfunction dispatchPointerEvent(element, name, event) {\n    element.dispatchEvent(new CustomEvent(name, { detail: { originalEvent: event } }));\n}\nfunction dispatchViewEvent(element, name, entry) {\n    element.dispatchEvent(new CustomEvent(name, { detail: { originalEntry: entry } }));\n}\n\nexports.dispatchPointerEvent = dispatchPointerEvent;\nexports.dispatchViewEvent = dispatchViewEvent;\nexports.motionEvent = motionEvent;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,WAAW,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK,IAAIC,WAAW,CAACF,IAAI,EAAE;EAAEG,MAAM,EAAE;IAAEF;EAAO;AAAE,CAAC,CAAC;AACnF,SAASG,oBAAoBA,CAACC,OAAO,EAAEL,IAAI,EAAEM,KAAK,EAAE;EAChDD,OAAO,CAACE,aAAa,CAAC,IAAIL,WAAW,CAACF,IAAI,EAAE;IAAEG,MAAM,EAAE;MAAEK,aAAa,EAAEF;IAAM;EAAE,CAAC,CAAC,CAAC;AACtF;AACA,SAASG,iBAAiBA,CAACJ,OAAO,EAAEL,IAAI,EAAEU,KAAK,EAAE;EAC7CL,OAAO,CAACE,aAAa,CAAC,IAAIL,WAAW,CAACF,IAAI,EAAE;IAAEG,MAAM,EAAE;MAAEQ,aAAa,EAAED;IAAM;EAAE,CAAC,CAAC,CAAC;AACtF;AAEAE,OAAO,CAACR,oBAAoB,GAAGA,oBAAoB;AACnDQ,OAAO,CAACH,iBAAiB,GAAGA,iBAAiB;AAC7CG,OAAO,CAACb,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}