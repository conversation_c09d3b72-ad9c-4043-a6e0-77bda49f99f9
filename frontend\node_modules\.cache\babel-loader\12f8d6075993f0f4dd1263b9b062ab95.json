{"ast": null, "code": "import { isMouseEvent } from './utils/event-type.mjs';\nimport { AnimationType } from '../render/utils/types.mjs';\nimport { usePointerEvent } from '../events/use-pointer-event.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nfunction createHoverEvent(visualElement, isActive, callback) {\n  return (event, info) => {\n    if (!isMouseEvent(event) || isDragActive()) return;\n    /**\n     * Ensure we trigger animations before firing event callback\n     */\n    if (visualElement.animationState) {\n      visualElement.animationState.setActive(AnimationType.Hover, isActive);\n    }\n    callback && callback(event, info);\n  };\n}\nfunction useHoverGesture({\n  onHoverStart,\n  onHoverEnd,\n  whileHover,\n  visualElement\n}) {\n  usePointerEvent(visualElement, \"pointerenter\", onHoverStart || whileHover ? createHoverEvent(visualElement, true, onHoverStart) : undefined, {\n    passive: !onHoverStart\n  });\n  usePointerEvent(visualElement, \"pointerleave\", onHoverEnd || whileHover ? createHoverEvent(visualElement, false, onHoverEnd) : undefined, {\n    passive: !onHoverEnd\n  });\n}\nexport { useHoverGesture };", "map": {"version": 3, "names": ["isMouseEvent", "AnimationType", "usePointerEvent", "isDragActive", "createHoverEvent", "visualElement", "isActive", "callback", "event", "info", "animationState", "setActive", "Hover", "useHoverGesture", "onHoverStart", "onHoverEnd", "whileHover", "undefined", "passive"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/use-hover-gesture.mjs"], "sourcesContent": ["import { isMouseEvent } from './utils/event-type.mjs';\nimport { AnimationType } from '../render/utils/types.mjs';\nimport { usePointerEvent } from '../events/use-pointer-event.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\n\nfunction createHoverEvent(visualElement, isActive, callback) {\n    return (event, info) => {\n        if (!isMouseEvent(event) || isDragActive())\n            return;\n        /**\n         * Ensure we trigger animations before firing event callback\n         */\n        if (visualElement.animationState) {\n            visualElement.animationState.setActive(AnimationType.Hover, isActive);\n        }\n        callback && callback(event, info);\n    };\n}\nfunction useHoverGesture({ onHoverStart, onHoverEnd, whileHover, visualElement, }) {\n    usePointerEvent(visualElement, \"pointerenter\", onHoverStart || whileHover\n        ? createHoverEvent(visualElement, true, onHoverStart)\n        : undefined, { passive: !onHoverStart });\n    usePointerEvent(visualElement, \"pointerleave\", onHoverEnd || whileHover\n        ? createHoverEvent(visualElement, false, onHoverEnd)\n        : undefined, { passive: !onHoverEnd });\n}\n\nexport { useHoverGesture };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,YAAY,QAAQ,uBAAuB;AAEpD,SAASC,gBAAgBA,CAACC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACzD,OAAO,CAACC,KAAK,EAAEC,IAAI,KAAK;IACpB,IAAI,CAACT,YAAY,CAACQ,KAAK,CAAC,IAAIL,YAAY,CAAC,CAAC,EACtC;IACJ;AACR;AACA;IACQ,IAAIE,aAAa,CAACK,cAAc,EAAE;MAC9BL,aAAa,CAACK,cAAc,CAACC,SAAS,CAACV,aAAa,CAACW,KAAK,EAAEN,QAAQ,CAAC;IACzE;IACAC,QAAQ,IAAIA,QAAQ,CAACC,KAAK,EAAEC,IAAI,CAAC;EACrC,CAAC;AACL;AACA,SAASI,eAAeA,CAAC;EAAEC,YAAY;EAAEC,UAAU;EAAEC,UAAU;EAAEX;AAAe,CAAC,EAAE;EAC/EH,eAAe,CAACG,aAAa,EAAE,cAAc,EAAES,YAAY,IAAIE,UAAU,GACnEZ,gBAAgB,CAACC,aAAa,EAAE,IAAI,EAAES,YAAY,CAAC,GACnDG,SAAS,EAAE;IAAEC,OAAO,EAAE,CAACJ;EAAa,CAAC,CAAC;EAC5CZ,eAAe,CAACG,aAAa,EAAE,cAAc,EAAEU,UAAU,IAAIC,UAAU,GACjEZ,gBAAgB,CAACC,aAAa,EAAE,KAAK,EAAEU,UAAU,CAAC,GAClDE,SAAS,EAAE;IAAEC,OAAO,EAAE,CAACH;EAAW,CAAC,CAAC;AAC9C;AAEA,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}