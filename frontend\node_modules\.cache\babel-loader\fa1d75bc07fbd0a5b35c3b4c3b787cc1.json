{"ast": null, "code": "'use strict';\n\nconst getOptions = (options, key) =>\n/**\n * TODO: Make test for this\n * Always return a new object otherwise delay is overwritten by results of stagger\n * and this results in no stagger\n */\noptions[key] ? Object.assign(Object.assign({}, options), options[key]) : Object.assign({}, options);\nexports.getOptions = getOptions;", "map": {"version": 3, "names": ["getOptions", "options", "key", "Object", "assign", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/options.cjs.js"], "sourcesContent": ["'use strict';\n\nconst getOptions = (options, key) => \n/**\n * TODO: Make test for this\n * Always return a new object otherwise delay is overwritten by results of stagger\n * and this results in no stagger\n */\noptions[key] ? Object.assign(Object.assign({}, options), options[key]) : Object.assign({}, options);\n\nexports.getOptions = getOptions;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,UAAU,GAAGA,CAACC,OAAO,EAAEC,GAAG;AAChC;AACA;AACA;AACA;AACA;AACAD,OAAO,CAACC,GAAG,CAAC,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAAC,EAAEA,OAAO,CAACC,GAAG,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAAC;AAEnGI,OAAO,CAACL,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script"}