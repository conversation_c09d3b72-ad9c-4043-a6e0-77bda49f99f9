# 🚀 Local Development Setup Guide

## Prerequisites

- **Docker** and **Docker Compose** installed
- **Node.js 18+** (for frontend development)
- **Python 3.9+** (for backend development)
- **Git** for cloning the repository

## Option 1: Quick Start with Docker (Recommended)

### 1. <PERSON><PERSON> and Navigate
```bash
git clone <repository-url>
cd cable-operator-crm
```

### 2. Start All Services
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run the deployment script
./scripts/deploy.sh
```

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Admin Login**: <EMAIL> / admin123

### 4. Check Service Status
```bash
# View running containers
docker-compose ps

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

## Option 2: Development Mode (Frontend + Backend Separately)

### Backend Setup

#### 1. Navigate to Backend Directory
```bash
cd cable-operator-crm/backend
```

#### 2. Create Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

#### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

#### 4. Setup Environment
```bash
# Copy environment file
cp .env.example .env

# Edit .env file with your settings
# For local development, you can use SQLite:
# DATABASE_URL=sqlite:///./cable_operator.db
```

#### 5. Initialize Database
```bash
# Run database migrations
alembic upgrade head

# Create sample data (optional)
python scripts/create_sample_data.py
```

#### 6. Start Backend Server
```bash
# Development server with auto-reload
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Or using the run script
python run.py
```

#### 7. Verify Backend
- **API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health
- **API Docs**: http://localhost:8000/docs
- **Interactive API**: http://localhost:8000/redoc

### Frontend Setup

#### 1. Navigate to Frontend Directory
```bash
cd cable-operator-crm/frontend
```

#### 2. Install Dependencies
```bash
npm install
```

#### 3. Setup Environment
```bash
# Copy environment file
cp .env.example .env

# Edit .env file
# REACT_APP_API_URL=http://localhost:8000/api/v1
```

#### 4. Start Frontend Server
```bash
# Development server with hot reload
npm start
```

#### 5. Verify Frontend
- **Application**: http://localhost:3000
- **Login**: <EMAIL> / admin123

## Option 3: Hybrid Setup (Docker Backend + Local Frontend)

This is useful when you want to develop the frontend but use Docker for backend services:

### 1. Start Backend Services Only
```bash
# Start only backend, database, and redis
docker-compose up -d postgres redis backend
```

### 2. Setup and Start Frontend Locally
```bash
cd frontend
npm install
cp .env.example .env
# Edit .env to point to: REACT_APP_API_URL=http://localhost:8000/api/v1
npm start
```

## Testing the System

### 1. Backend API Testing

#### Using curl:
```bash
# Health check
curl http://localhost:8000/health

# Login to get token
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Use the token for authenticated requests
curl -X GET http://localhost:8000/api/v1/customers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Using the Interactive API Docs:
1. Go to http://localhost:8000/docs
2. Click "Authorize" button
3. <NAME_EMAIL> / admin123
4. Test any endpoint directly from the browser

### 2. Frontend Testing

#### Login Flow:
1. Go to http://localhost:3000
2. Login with: <EMAIL> / admin123
3. Navigate through different sections:
   - Dashboard
   - Customers
   - Plans
   - Payments
   - Invoices
   - Reports
   - Settings

#### Test Features:
- Create a new customer
- Add a new plan
- Record a payment
- Generate an invoice
- Export data
- Toggle dark mode

## Troubleshooting

### Common Issues

#### Backend Issues:

**Port Already in Use:**
```bash
# Find process using port 8000
lsof -i :8000
# Kill the process
kill -9 <PID>
```

**Database Connection Error:**
```bash
# If using Docker, ensure PostgreSQL is running
docker-compose up -d postgres

# Check database connection
docker-compose exec postgres pg_isready -U cable_user
```

**Module Import Errors:**
```bash
# Ensure virtual environment is activated
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Reinstall dependencies
pip install -r requirements.txt
```

#### Frontend Issues:

**Node Modules Issues:**
```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

**Port 3000 in Use:**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or start on different port
PORT=3001 npm start
```

**API Connection Issues:**
- Check if backend is running on http://localhost:8000
- Verify REACT_APP_API_URL in .env file
- Check browser console for CORS errors

#### Docker Issues:

**Permission Denied:**
```bash
# Make script executable
chmod +x scripts/deploy.sh

# Fix file permissions
sudo chown -R $USER:$USER .
```

**Container Won't Start:**
```bash
# Check logs
docker-compose logs <service-name>

# Rebuild containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Database Volume Issues:**
```bash
# Reset database volume
docker-compose down -v
docker-compose up -d
```

## Development Workflow

### 1. Making Changes

#### Backend Changes:
- Edit files in `backend/app/`
- Server auto-reloads with `--reload` flag
- Check logs in terminal
- Test API endpoints at http://localhost:8000/docs

#### Frontend Changes:
- Edit files in `frontend/src/`
- Browser auto-reloads with hot reload
- Check browser console for errors
- Test UI at http://localhost:3000

### 2. Database Changes

#### Create Migration:
```bash
cd backend
alembic revision --autogenerate -m "Description of changes"
alembic upgrade head
```

#### Reset Database:
```bash
# Drop and recreate
alembic downgrade base
alembic upgrade head
```

### 3. Adding New Features

#### Backend:
1. Add model in `app/models/`
2. Create schema in `app/schemas/`
3. Add routes in `app/api/v1/`
4. Create migration with alembic
5. Test with API docs

#### Frontend:
1. Create component in `src/components/`
2. Add page in `src/pages/`
3. Update routing in `App.tsx`
4. Add API calls in `src/services/`
5. Test in browser

## Performance Monitoring

### Backend Monitoring:
```bash
# View API logs
docker-compose logs -f backend

# Monitor database
docker-compose exec postgres psql -U cable_user -d cable_operator_db
```

### Frontend Monitoring:
- Open browser DevTools (F12)
- Check Network tab for API calls
- Monitor Console for errors
- Use React DevTools extension

## Quick Commands Reference

```bash
# Start everything
./scripts/deploy.sh

# View status
docker-compose ps

# View logs
docker-compose logs -f

# Stop everything
docker-compose down

# Restart services
docker-compose restart

# Backend only
cd backend && uvicorn app.main:app --reload

# Frontend only
cd frontend && npm start

# Database shell
docker-compose exec postgres psql -U cable_user cable_operator_db

# Backend shell
docker-compose exec backend bash

# Run tests
cd backend && pytest
cd frontend && npm test
```

This setup gives you a fully functional local development environment where you can test all features of the Cable Operator CRM system!
