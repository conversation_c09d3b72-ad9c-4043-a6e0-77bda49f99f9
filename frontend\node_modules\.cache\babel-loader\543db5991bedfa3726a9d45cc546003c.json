{"ast": null, "code": "import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\nimport { frameData } from './data.mjs';\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst stepsOrder = [\"read\", \"update\", \"preRender\", \"render\", \"postRender\"];\nconst steps = stepsOrder.reduce((acc, key) => {\n  acc[key] = createRenderStep(() => runNextFrame = true);\n  return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n  const step = steps[key];\n  acc[key] = (process, keepAlive = false, immediate = false) => {\n    if (!runNextFrame) startLoop();\n    return step.schedule(process, keepAlive, immediate);\n  };\n  return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = steps[key].cancel;\n  return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = () => steps[key].process(frameData);\n  return acc;\n}, {});\nconst processStep = stepId => steps[stepId].process(frameData);\nconst processFrame = timestamp => {\n  runNextFrame = false;\n  frameData.delta = useDefaultElapsed ? defaultTimestep : Math.max(Math.min(timestamp - frameData.timestamp, maxElapsed), 1);\n  frameData.timestamp = timestamp;\n  isProcessing = true;\n  stepsOrder.forEach(processStep);\n  isProcessing = false;\n  if (runNextFrame) {\n    useDefaultElapsed = false;\n    onNextFrame(processFrame);\n  }\n};\nconst startLoop = () => {\n  runNextFrame = true;\n  useDefaultElapsed = true;\n  if (!isProcessing) onNextFrame(processFrame);\n};\nexport { cancelSync, flushSync, sync };", "map": {"version": 3, "names": ["onNextFrame", "defaultTimestep", "createRenderStep", "frameData", "maxElapsed", "useDefaultElapsed", "runNextFrame", "isProcessing", "stepsOrder", "steps", "reduce", "acc", "key", "sync", "step", "process", "keepAlive", "immediate", "startLoop", "schedule", "cancelSync", "cancel", "flushSync", "processStep", "stepId", "processFrame", "timestamp", "delta", "Math", "max", "min", "for<PERSON>ach"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/frameloop/index.mjs"], "sourcesContent": ["import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\nimport { frameData } from './data.mjs';\n\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst stepsOrder = [\n    \"read\",\n    \"update\",\n    \"preRender\",\n    \"render\",\n    \"postRender\",\n];\nconst steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(() => (runNextFrame = true));\n    return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = (process, keepAlive = false, immediate = false) => {\n        if (!runNextFrame)\n            startLoop();\n        return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = steps[key].cancel;\n    return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = () => steps[key].process(frameData);\n    return acc;\n}, {});\nconst processStep = (stepId) => steps[stepId].process(frameData);\nconst processFrame = (timestamp) => {\n    runNextFrame = false;\n    frameData.delta = useDefaultElapsed\n        ? defaultTimestep\n        : Math.max(Math.min(timestamp - frameData.timestamp, maxElapsed), 1);\n    frameData.timestamp = timestamp;\n    isProcessing = true;\n    stepsOrder.forEach(processStep);\n    isProcessing = false;\n    if (runNextFrame) {\n        useDefaultElapsed = false;\n        onNextFrame(processFrame);\n    }\n};\nconst startLoop = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!isProcessing)\n        onNextFrame(processFrame);\n};\n\nexport { cancelSync, flushSync, sync };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,QAAQ,qBAAqB;AAClE,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,SAAS,QAAQ,YAAY;AAEtC,MAAMC,UAAU,GAAG,EAAE;AACrB,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,YAAY,GAAG,KAAK;AACxB,IAAIC,YAAY,GAAG,KAAK;AACxB,MAAMC,UAAU,GAAG,CACf,MAAM,EACN,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,YAAY,CACf;AACD,MAAMC,KAAK,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC1CD,GAAG,CAACC,GAAG,CAAC,GAAGV,gBAAgB,CAAC,MAAOI,YAAY,GAAG,IAAK,CAAC;EACxD,OAAOK,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAME,IAAI,GAAGL,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EACzC,MAAME,IAAI,GAAGL,KAAK,CAACG,GAAG,CAAC;EACvBD,GAAG,CAACC,GAAG,CAAC,GAAG,CAACG,OAAO,EAAEC,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;IAC1D,IAAI,CAACX,YAAY,EACbY,SAAS,CAAC,CAAC;IACf,OAAOJ,IAAI,CAACK,QAAQ,CAACJ,OAAO,EAAEC,SAAS,EAAEC,SAAS,CAAC;EACvD,CAAC;EACD,OAAON,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMS,UAAU,GAAGZ,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC/CD,GAAG,CAACC,GAAG,CAAC,GAAGH,KAAK,CAACG,GAAG,CAAC,CAACS,MAAM;EAC5B,OAAOV,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMW,SAAS,GAAGd,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC9CD,GAAG,CAACC,GAAG,CAAC,GAAG,MAAMH,KAAK,CAACG,GAAG,CAAC,CAACG,OAAO,CAACZ,SAAS,CAAC;EAC9C,OAAOQ,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMY,WAAW,GAAIC,MAAM,IAAKf,KAAK,CAACe,MAAM,CAAC,CAACT,OAAO,CAACZ,SAAS,CAAC;AAChE,MAAMsB,YAAY,GAAIC,SAAS,IAAK;EAChCpB,YAAY,GAAG,KAAK;EACpBH,SAAS,CAACwB,KAAK,GAAGtB,iBAAiB,GAC7BJ,eAAe,GACf2B,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,SAAS,GAAGvB,SAAS,CAACuB,SAAS,EAAEtB,UAAU,CAAC,EAAE,CAAC,CAAC;EACxED,SAAS,CAACuB,SAAS,GAAGA,SAAS;EAC/BnB,YAAY,GAAG,IAAI;EACnBC,UAAU,CAACuB,OAAO,CAACR,WAAW,CAAC;EAC/BhB,YAAY,GAAG,KAAK;EACpB,IAAID,YAAY,EAAE;IACdD,iBAAiB,GAAG,KAAK;IACzBL,WAAW,CAACyB,YAAY,CAAC;EAC7B;AACJ,CAAC;AACD,MAAMP,SAAS,GAAGA,CAAA,KAAM;EACpBZ,YAAY,GAAG,IAAI;EACnBD,iBAAiB,GAAG,IAAI;EACxB,IAAI,CAACE,YAAY,EACbP,WAAW,CAACyB,YAAY,CAAC;AACjC,CAAC;AAED,SAASL,UAAU,EAAEE,SAAS,EAAET,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}