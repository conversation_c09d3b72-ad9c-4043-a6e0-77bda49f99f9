{"ast": null, "code": "'use strict';\n\n/** @type {import('./isNaN')} */\nmodule.exports = Number.isNaN || function isNaN(a) {\n  return a !== a;\n};", "map": {"version": 3, "names": ["module", "exports", "Number", "isNaN", "a"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/math-intrinsics/isNaN.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./isNaN')} */\nmodule.exports = Number.isNaN || function isNaN(a) {\n\treturn a !== a;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,KAAK,IAAI,SAASA,KAAKA,CAACC,CAAC,EAAE;EAClD,OAAOA,CAAC,KAAKA,CAAC;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}