{"ast": null, "code": "'use strict';\n\nfunction hydrateKeyframes(keyframes, readInitialValue) {\n  for (let i = 0; i < keyframes.length; i++) {\n    if (keyframes[i] === null) {\n      keyframes[i] = i ? keyframes[i - 1] : readInitialValue();\n    }\n  }\n  return keyframes;\n}\nconst keyframesList = keyframes => Array.isArray(keyframes) ? keyframes : [keyframes];\nexports.hydrateKeyframes = hydrateKeyframes;\nexports.keyframesList = keyframesList;", "map": {"version": 3, "names": ["hydrateKeyframes", "keyframes", "readInitialValue", "i", "length", "keyframesList", "Array", "isArray", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/keyframes.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction hydrateKeyframes(keyframes, readInitialValue) {\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] === null) {\n            keyframes[i] = i ? keyframes[i - 1] : readInitialValue();\n        }\n    }\n    return keyframes;\n}\nconst keyframesList = (keyframes) => Array.isArray(keyframes) ? keyframes : [keyframes];\n\nexports.hydrateKeyframes = hydrateKeyframes;\nexports.keyframesList = keyframesList;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,gBAAgB,EAAE;EACnD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIF,SAAS,CAACE,CAAC,CAAC,KAAK,IAAI,EAAE;MACvBF,SAAS,CAACE,CAAC,CAAC,GAAGA,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGD,gBAAgB,CAAC,CAAC;IAC5D;EACJ;EACA,OAAOD,SAAS;AACpB;AACA,MAAMI,aAAa,GAAIJ,SAAS,IAAKK,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAEvFO,OAAO,CAACR,gBAAgB,GAAGA,gBAAgB;AAC3CQ,OAAO,CAACH,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}