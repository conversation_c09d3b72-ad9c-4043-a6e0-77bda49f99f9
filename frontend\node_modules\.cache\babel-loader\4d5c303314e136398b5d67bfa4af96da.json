{"ast": null, "code": "import { transformPropOrder } from './transform.mjs';\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\",\n  transformPerspective: \"perspective\"\n};\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst sortTransformProps = (a, b) => transformPropOrder.indexOf(a) - transformPropOrder.indexOf(b);\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform({\n  transform,\n  transformKeys\n}, {\n  enableHardwareAcceleration = true,\n  allowTransformNone = true\n}, transformIsDefault, transformTemplate) {\n  // The transform string we're going to build into.\n  let transformString = \"\";\n  // Transform keys into their default order - this will determine the output order.\n  transformKeys.sort(sortTransformProps);\n  // Loop over each transform and build them into transformString\n  for (const key of transformKeys) {\n    transformString += `${translateAlias[key] || key}(${transform[key]}) `;\n  }\n  if (enableHardwareAcceleration && !transform.z) {\n    transformString += \"translateZ(0)\";\n  }\n  transformString = transformString.trim();\n  // If we have a custom `transform` template, pass our transform values and\n  // generated transformString to that before returning\n  if (transformTemplate) {\n    transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n  } else if (allowTransformNone && transformIsDefault) {\n    transformString = \"none\";\n  }\n  return transformString;\n}\nexport { buildTransform };", "map": {"version": 3, "names": ["transformPropOrder", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "z", "transformPerspective", "sortTransformProps", "a", "b", "indexOf", "buildTransform", "transform", "transformKeys", "enableHardwareAcceleration", "allowTransformNone", "transformIsDefault", "transformTemplate", "transformString", "sort", "key", "trim"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs"], "sourcesContent": ["import { transformPropOrder } from './transform.mjs';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst sortTransformProps = (a, b) => transformPropOrder.indexOf(a) - transformPropOrder.indexOf(b);\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform({ transform, transformKeys, }, { enableHardwareAcceleration = true, allowTransformNone = true, }, transformIsDefault, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    // Transform keys into their default order - this will determine the output order.\n    transformKeys.sort(sortTransformProps);\n    // Loop over each transform and build them into transformString\n    for (const key of transformKeys) {\n        transformString += `${translateAlias[key] || key}(${transform[key]}) `;\n    }\n    if (enableHardwareAcceleration && !transform.z) {\n        transformString += \"translateZ(0)\";\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (allowTransformNone && transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,iBAAiB;AAEpD,MAAMC,cAAc,GAAG;EACnBC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,oBAAoB,EAAE;AAC1B,CAAC;AACD;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKR,kBAAkB,CAACS,OAAO,CAACF,CAAC,CAAC,GAAGP,kBAAkB,CAACS,OAAO,CAACD,CAAC,CAAC;AAClG;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAAC;EAAEC,SAAS;EAAEC;AAAe,CAAC,EAAE;EAAEC,0BAA0B,GAAG,IAAI;EAAEC,kBAAkB,GAAG;AAAM,CAAC,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAE;EAC7J;EACA,IAAIC,eAAe,GAAG,EAAE;EACxB;EACAL,aAAa,CAACM,IAAI,CAACZ,kBAAkB,CAAC;EACtC;EACA,KAAK,MAAMa,GAAG,IAAIP,aAAa,EAAE;IAC7BK,eAAe,IAAI,GAAGhB,cAAc,CAACkB,GAAG,CAAC,IAAIA,GAAG,IAAIR,SAAS,CAACQ,GAAG,CAAC,IAAI;EAC1E;EACA,IAAIN,0BAA0B,IAAI,CAACF,SAAS,CAACP,CAAC,EAAE;IAC5Ca,eAAe,IAAI,eAAe;EACtC;EACAA,eAAe,GAAGA,eAAe,CAACG,IAAI,CAAC,CAAC;EACxC;EACA;EACA,IAAIJ,iBAAiB,EAAE;IACnBC,eAAe,GAAGD,iBAAiB,CAACL,SAAS,EAAEI,kBAAkB,GAAG,EAAE,GAAGE,eAAe,CAAC;EAC7F,CAAC,MACI,IAAIH,kBAAkB,IAAIC,kBAAkB,EAAE;IAC/CE,eAAe,GAAG,MAAM;EAC5B;EACA,OAAOA,eAAe;AAC1B;AAEA,SAASP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}