{"ast": null, "code": "import { MeasureLayout } from './MeasureLayout.mjs';\nconst layoutFeatures = {\n  measureLayout: MeasureLayout\n};\nexport { layoutFeatures };", "map": {"version": 3, "names": ["MeasureLayout", "layoutFeatures", "measureLayout"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/layout/index.mjs"], "sourcesContent": ["import { MeasureLayout } from './MeasureLayout.mjs';\n\nconst layoutFeatures = {\n    measureLayout: MeasureLayout,\n};\n\nexport { layoutFeatures };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AAEnD,MAAMC,cAAc,GAAG;EACnBC,aAAa,EAAEF;AACnB,CAAC;AAED,SAASC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}