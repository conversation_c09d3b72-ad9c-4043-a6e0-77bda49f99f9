"""
Custom exceptions for the Cable Operator CRM application
"""

from typing import Any, Dict, Optional


class CableOperatorCRMException(Exception):
    """Base exception for Cable Operator CRM"""
    
    def __init__(self, detail: str, extra_data: Optional[Dict[str, Any]] = None):
        self.detail = detail
        self.extra_data = extra_data or {}
        super().__init__(detail)


class ValidationException(CableOperatorCRMException):
    """Raised when validation fails"""
    pass


class AuthenticationException(CableOperatorCRMException):
    """Raised when authentication fails"""
    pass


class AuthorizationException(CableOperatorCRMException):
    """Raised when authorization fails"""
    pass


class NotFoundException(CableOperatorCRMException):
    """Raised when a resource is not found"""
    pass


class ConflictException(CableOperatorCRMException):
    """Raised when there's a conflict (e.g., duplicate data)"""
    pass


class BusinessLogicException(CableOperatorCRMException):
    """Raised when business logic validation fails"""
    pass


class ExternalServiceException(CableOperatorCRMException):
    """Raised when external service calls fail"""
    pass


class FileProcessingException(CableOperatorCRMException):
    """Raised when file processing fails"""
    pass


class EmailException(CableOperatorCRMException):
    """Raised when email operations fail"""
    pass


class PaymentException(CableOperatorCRMException):
    """Raised when payment operations fail"""
    pass


class InvoiceException(CableOperatorCRMException):
    """Raised when invoice operations fail"""
    pass


class DatabaseException(CableOperatorCRMException):
    """Raised when database operations fail"""
    pass


class ConfigurationException(CableOperatorCRMException):
    """Raised when configuration is invalid"""
    pass


# Specific business exceptions
class CustomerNotFoundException(NotFoundException):
    """Customer not found"""
    
    def __init__(self, customer_id: int):
        super().__init__(f"Customer with ID {customer_id} not found")


class PlanNotFoundException(NotFoundException):
    """Plan not found"""
    
    def __init__(self, plan_id: int):
        super().__init__(f"Plan with ID {plan_id} not found")


class PaymentNotFoundException(NotFoundException):
    """Payment not found"""
    
    def __init__(self, payment_id: int):
        super().__init__(f"Payment with ID {payment_id} not found")


class UserNotFoundException(NotFoundException):
    """User not found"""
    
    def __init__(self, user_id: int):
        super().__init__(f"User with ID {user_id} not found")


class CompanyNotFoundException(NotFoundException):
    """Company not found"""
    
    def __init__(self, company_id: int):
        super().__init__(f"Company with ID {company_id} not found")


class DuplicateEmailException(ConflictException):
    """Email already exists"""
    
    def __init__(self, email: str):
        super().__init__(f"Email {email} already exists")


class DuplicatePhoneException(ConflictException):
    """Phone number already exists"""
    
    def __init__(self, phone: str):
        super().__init__(f"Phone number {phone} already exists")


class InsufficientPermissionsException(AuthorizationException):
    """User doesn't have required permissions"""
    
    def __init__(self, required_permission: str):
        super().__init__(f"Permission '{required_permission}' required")


class InvalidCredentialsException(AuthenticationException):
    """Invalid login credentials"""
    
    def __init__(self):
        super().__init__("Invalid email or password")


class TokenExpiredException(AuthenticationException):
    """JWT token has expired"""
    
    def __init__(self):
        super().__init__("Token has expired")


class InvalidTokenException(AuthenticationException):
    """Invalid JWT token"""
    
    def __init__(self):
        super().__init__("Invalid token")


class AccountInactiveException(AuthenticationException):
    """User account is inactive"""
    
    def __init__(self):
        super().__init__("Account is inactive")


class PaymentOverdueException(PaymentException):
    """Payment is overdue"""
    
    def __init__(self, days_overdue: int):
        super().__init__(f"Payment is {days_overdue} days overdue")


class InvoiceAlreadyPaidException(InvoiceException):
    """Invoice is already paid"""
    
    def __init__(self, invoice_id: str):
        super().__init__(f"Invoice {invoice_id} is already paid")


class PlanInUseException(BusinessLogicException):
    """Plan cannot be deleted because it's in use"""
    
    def __init__(self, plan_id: int, customer_count: int):
        super().__init__(
            f"Plan {plan_id} cannot be deleted. It's assigned to {customer_count} customers"
        )


class FileUploadException(FileProcessingException):
    """File upload failed"""
    
    def __init__(self, reason: str):
        super().__init__(f"File upload failed: {reason}")


class EmailDeliveryException(EmailException):
    """Email delivery failed"""
    
    def __init__(self, recipient: str, reason: str):
        super().__init__(f"Failed to send email to {recipient}: {reason}")


# Export all exceptions
__all__ = [
    "CableOperatorCRMException",
    "ValidationException",
    "AuthenticationException",
    "AuthorizationException",
    "NotFoundException",
    "ConflictException",
    "BusinessLogicException",
    "ExternalServiceException",
    "FileProcessingException",
    "EmailException",
    "PaymentException",
    "InvoiceException",
    "DatabaseException",
    "ConfigurationException",
    "CustomerNotFoundException",
    "PlanNotFoundException",
    "PaymentNotFoundException",
    "UserNotFoundException",
    "CompanyNotFoundException",
    "DuplicateEmailException",
    "DuplicatePhoneException",
    "InsufficientPermissionsException",
    "InvalidCredentialsException",
    "TokenExpiredException",
    "InvalidTokenException",
    "AccountInactiveException",
    "PaymentOverdueException",
    "InvoiceAlreadyPaidException",
    "PlanInUseException",
    "FileUploadException",
    "EmailDeliveryException",
]
