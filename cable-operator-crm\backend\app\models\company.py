"""
Company model for multi-tenant support
"""

from sqlalchemy import Column, String, Text, Boolean, Float, JSON
from sqlalchemy.orm import relationship

from .base import BaseModel


class Company(BaseModel):
    """Company/Business model"""
    
    __tablename__ = "companies"
    
    # Basic Information
    name = Column(String(255), nullable=False, index=True)
    business_name = Column(String(255), nullable=True)
    registration_number = Column(String(100), nullable=True, unique=True)
    
    # Contact Information
    email = Column(String(255), nullable=False, unique=True, index=True)
    phone = Column(String(20), nullable=False)
    website = Column(String(255), nullable=True)
    
    # Address
    address_line1 = Column(String(255), nullable=False)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=False)
    state = Column(String(100), nullable=False)
    postal_code = Column(String(20), nullable=False)
    country = Column(String(100), nullable=False, default="United States")
    
    # Business Settings
    currency = Column(String(3), nullable=False, default="USD")
    tax_rate = Column(Float, nullable=False, default=0.0)
    timezone = Column(String(50), nullable=False, default="UTC")
    
    # Billing Settings
    invoice_prefix = Column(String(10), nullable=False, default="INV")
    next_invoice_number = Column(String(20), nullable=False, default="000001")
    payment_terms_days = Column(String(10), nullable=False, default="30")
    
    # Branding
    logo_url = Column(String(500), nullable=True)
    primary_color = Column(String(7), nullable=False, default="#3B82F6")
    secondary_color = Column(String(7), nullable=False, default="#1F2937")
    
    # Features & Settings
    features_enabled = Column(JSON, nullable=False, default=dict)
    email_settings = Column(JSON, nullable=False, default=dict)
    sms_settings = Column(JSON, nullable=False, default=dict)
    
    # Status
    is_active = Column(Boolean, nullable=False, default=True)
    is_setup_complete = Column(Boolean, nullable=False, default=False)
    
    # Additional Information
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Relationships
    users = relationship("User", back_populates="company", cascade="all, delete-orphan")
    customers = relationship("Customer", back_populates="company", cascade="all, delete-orphan")
    plans = relationship("Plan", back_populates="company", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="company", cascade="all, delete-orphan")
    invoices = relationship("Invoice", back_populates="company", cascade="all, delete-orphan")
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return ['name', 'business_name', 'email', 'phone', 'city', 'state']
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return ['is_active', 'is_setup_complete', 'country', 'state', 'currency']
    
    def get_full_address(self):
        """Get formatted full address"""
        address_parts = [self.address_line1]
        
        if self.address_line2:
            address_parts.append(self.address_line2)
        
        address_parts.append(f"{self.city}, {self.state} {self.postal_code}")
        address_parts.append(self.country)
        
        return "\n".join(address_parts)
    
    def get_next_invoice_number(self):
        """Get and increment next invoice number"""
        current_number = self.next_invoice_number
        
        # Increment for next time
        try:
            next_num = int(current_number) + 1
            self.next_invoice_number = str(next_num).zfill(len(current_number))
        except ValueError:
            # If not numeric, just increment a counter
            self.next_invoice_number = "000001"
        
        return f"{self.invoice_prefix}-{current_number}"
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """Check if a feature is enabled"""
        return self.features_enabled.get(feature_name, False)
    
    def enable_feature(self, feature_name: str):
        """Enable a feature"""
        if not self.features_enabled:
            self.features_enabled = {}
        self.features_enabled[feature_name] = True
    
    def disable_feature(self, feature_name: str):
        """Disable a feature"""
        if not self.features_enabled:
            self.features_enabled = {}
        self.features_enabled[feature_name] = False
    
    def get_email_setting(self, setting_name: str, default=None):
        """Get email setting"""
        return self.email_settings.get(setting_name, default)
    
    def set_email_setting(self, setting_name: str, value):
        """Set email setting"""
        if not self.email_settings:
            self.email_settings = {}
        self.email_settings[setting_name] = value
    
    def get_sms_setting(self, setting_name: str, default=None):
        """Get SMS setting"""
        return self.sms_settings.get(setting_name, default)
    
    def set_sms_setting(self, setting_name: str, value):
        """Set SMS setting"""
        if not self.sms_settings:
            self.sms_settings = {}
        self.sms_settings[setting_name] = value
    
    def complete_setup(self):
        """Mark company setup as complete"""
        self.is_setup_complete = True
    
    def __repr__(self):
        return f"<Company(id={self.id}, name='{self.name}')>"
