{"ast": null, "code": "import { featureDefinitions } from './definitions.mjs';\nfunction loadFeatures(features) {\n  for (const key in features) {\n    if (key === \"projectionNodeConstructor\") {\n      featureDefinitions.projectionNodeConstructor = features[key];\n    } else {\n      featureDefinitions[key].Component = features[key];\n    }\n  }\n}\nexport { loadFeatures };", "map": {"version": 3, "names": ["featureDefinitions", "loadFeatures", "features", "key", "projectionNodeConstructor", "Component"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/load-features.mjs"], "sourcesContent": ["import { featureDefinitions } from './definitions.mjs';\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        if (key === \"projectionNodeConstructor\") {\n            featureDefinitions.projectionNodeConstructor = features[key];\n        }\n        else {\n            featureDefinitions[key].Component = features[key];\n        }\n    }\n}\n\nexport { loadFeatures };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,mBAAmB;AAEtD,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,KAAK,MAAMC,GAAG,IAAID,QAAQ,EAAE;IACxB,IAAIC,GAAG,KAAK,2BAA2B,EAAE;MACrCH,kBAAkB,CAACI,yBAAyB,GAAGF,QAAQ,CAACC,GAAG,CAAC;IAChE,CAAC,MACI;MACDH,kBAAkB,CAACG,GAAG,CAAC,CAACE,SAAS,GAAGH,QAAQ,CAACC,GAAG,CAAC;IACrD;EACJ;AACJ;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}