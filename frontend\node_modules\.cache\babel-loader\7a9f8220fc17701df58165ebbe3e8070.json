{"ast": null, "code": "var warning = function () {};\nvar invariant = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  warning = function (check, message) {\n    if (!check && typeof console !== 'undefined') {\n      console.warn(message);\n    }\n  };\n  invariant = function (check, message) {\n    if (!check) {\n      throw new Error(message);\n    }\n  };\n}\nexport { invariant, warning };", "map": {"version": 3, "names": ["warning", "invariant", "process", "env", "NODE_ENV", "check", "message", "console", "warn", "Error"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/hey-listen/dist/hey-listen.es.js"], "sourcesContent": ["var warning = function () { };\r\nvar invariant = function () { };\r\nif (process.env.NODE_ENV !== 'production') {\r\n    warning = function (check, message) {\r\n        if (!check && typeof console !== 'undefined') {\r\n            console.warn(message);\r\n        }\r\n    };\r\n    invariant = function (check, message) {\r\n        if (!check) {\r\n            throw new Error(message);\r\n        }\r\n    };\r\n}\n\nexport { invariant, warning };\n"], "mappings": "AAAA,IAAIA,OAAO,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AAC7B,IAAIC,SAAS,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AAC/B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACvCJ,OAAO,GAAG,SAAAA,CAAUK,KAAK,EAAEC,OAAO,EAAE;IAChC,IAAI,CAACD,KAAK,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAC1CA,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;IACzB;EACJ,CAAC;EACDL,SAAS,GAAG,SAAAA,CAAUI,KAAK,EAAEC,OAAO,EAAE;IAClC,IAAI,CAACD,KAAK,EAAE;MACR,MAAM,IAAII,KAAK,CAACH,OAAO,CAAC;IAC5B;EACJ,CAAC;AACL;AAEA,SAASL,SAAS,EAAED,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}