{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar transforms = require('./transforms.cjs.js');\nfunction createStyles(keyframes) {\n  const initialKeyframes = {};\n  const transformKeys = [];\n  for (let key in keyframes) {\n    const value = keyframes[key];\n    if (transforms.isTransform(key)) {\n      if (transforms.transformAlias[key]) key = transforms.transformAlias[key];\n      transformKeys.push(key);\n      key = transforms.asTransformCssVar(key);\n    }\n    let initialKeyframe = Array.isArray(value) ? value[0] : value;\n    /**\n     * If this is a number and we have a default value type, convert the number\n     * to this type.\n     */\n    const definition = transforms.transformDefinitions.get(key);\n    if (definition) {\n      initialKeyframe = utils.isNumber(value) ? definition.toDefaultUnit(value) : value;\n    }\n    initialKeyframes[key] = initialKeyframe;\n  }\n  if (transformKeys.length) {\n    initialKeyframes.transform = transforms.buildTransformTemplate(transformKeys);\n  }\n  return initialKeyframes;\n}\nexports.createStyles = createStyles;", "map": {"version": 3, "names": ["utils", "require", "transforms", "createStyles", "keyframes", "initialKeyframes", "transformKeys", "key", "value", "isTransform", "transformAlias", "push", "asTransformCssVar", "initialKeyframe", "Array", "isArray", "definition", "transformDefinitions", "get", "isNumber", "toDefaultUnit", "length", "transform", "buildTransformTemplate", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/style-object.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar transforms = require('./transforms.cjs.js');\n\nfunction createStyles(keyframes) {\n    const initialKeyframes = {};\n    const transformKeys = [];\n    for (let key in keyframes) {\n        const value = keyframes[key];\n        if (transforms.isTransform(key)) {\n            if (transforms.transformAlias[key])\n                key = transforms.transformAlias[key];\n            transformKeys.push(key);\n            key = transforms.asTransformCssVar(key);\n        }\n        let initialKeyframe = Array.isArray(value) ? value[0] : value;\n        /**\n         * If this is a number and we have a default value type, convert the number\n         * to this type.\n         */\n        const definition = transforms.transformDefinitions.get(key);\n        if (definition) {\n            initialKeyframe = utils.isNumber(value)\n                ? definition.toDefaultUnit(value)\n                : value;\n        }\n        initialKeyframes[key] = initialKeyframe;\n    }\n    if (transformKeys.length) {\n        initialKeyframes.transform = transforms.buildTransformTemplate(transformKeys);\n    }\n    return initialKeyframes;\n}\n\nexports.createStyles = createStyles;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,UAAU,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASE,YAAYA,CAACC,SAAS,EAAE;EAC7B,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3B,MAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIC,GAAG,IAAIH,SAAS,EAAE;IACvB,MAAMI,KAAK,GAAGJ,SAAS,CAACG,GAAG,CAAC;IAC5B,IAAIL,UAAU,CAACO,WAAW,CAACF,GAAG,CAAC,EAAE;MAC7B,IAAIL,UAAU,CAACQ,cAAc,CAACH,GAAG,CAAC,EAC9BA,GAAG,GAAGL,UAAU,CAACQ,cAAc,CAACH,GAAG,CAAC;MACxCD,aAAa,CAACK,IAAI,CAACJ,GAAG,CAAC;MACvBA,GAAG,GAAGL,UAAU,CAACU,iBAAiB,CAACL,GAAG,CAAC;IAC3C;IACA,IAAIM,eAAe,GAAGC,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;IAC7D;AACR;AACA;AACA;IACQ,MAAMQ,UAAU,GAAGd,UAAU,CAACe,oBAAoB,CAACC,GAAG,CAACX,GAAG,CAAC;IAC3D,IAAIS,UAAU,EAAE;MACZH,eAAe,GAAGb,KAAK,CAACmB,QAAQ,CAACX,KAAK,CAAC,GACjCQ,UAAU,CAACI,aAAa,CAACZ,KAAK,CAAC,GAC/BA,KAAK;IACf;IACAH,gBAAgB,CAACE,GAAG,CAAC,GAAGM,eAAe;EAC3C;EACA,IAAIP,aAAa,CAACe,MAAM,EAAE;IACtBhB,gBAAgB,CAACiB,SAAS,GAAGpB,UAAU,CAACqB,sBAAsB,CAACjB,aAAa,CAAC;EACjF;EACA,OAAOD,gBAAgB;AAC3B;AAEAmB,OAAO,CAACrB,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}