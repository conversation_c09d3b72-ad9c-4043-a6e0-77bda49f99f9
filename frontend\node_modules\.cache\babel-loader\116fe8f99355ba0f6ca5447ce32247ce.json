{"ast": null, "code": "const featureTests = {\n  waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\")\n};\nconst results = {};\nconst supports = {};\n/**\n * Generate features tests that cache their results.\n */\nfor (const key in featureTests) {\n  supports[key] = () => {\n    if (results[key] === undefined) results[key] = featureTests[key]();\n    return results[key];\n  };\n}\nexport { supports };", "map": {"version": 3, "names": ["featureTests", "waapi", "Object", "hasOwnProperty", "call", "Element", "prototype", "results", "supports", "key", "undefined"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/waapi/supports.mjs"], "sourcesContent": ["const featureTests = {\n    waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\"),\n};\nconst results = {};\nconst supports = {};\n/**\n * Generate features tests that cache their results.\n */\nfor (const key in featureTests) {\n    supports[key] = () => {\n        if (results[key] === undefined)\n            results[key] = featureTests[key]();\n        return results[key];\n    };\n}\n\nexport { supports };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,KAAK,EAAEA,CAAA,KAAMC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE,SAAS;AACxE,CAAC;AACD,MAAMC,OAAO,GAAG,CAAC,CAAC;AAClB,MAAMC,QAAQ,GAAG,CAAC,CAAC;AACnB;AACA;AACA;AACA,KAAK,MAAMC,GAAG,IAAIT,YAAY,EAAE;EAC5BQ,QAAQ,CAACC,GAAG,CAAC,GAAG,MAAM;IAClB,IAAIF,OAAO,CAACE,GAAG,CAAC,KAAKC,SAAS,EAC1BH,OAAO,CAACE,GAAG,CAAC,GAAGT,YAAY,CAACS,GAAG,CAAC,CAAC,CAAC;IACtC,OAAOF,OAAO,CAACE,GAAG,CAAC;EACvB,CAAC;AACL;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}