{"ast": null, "code": "import { noopReturn, defaults, isEasingGenerator, isEasingList, interpolate } from '@motionone/utils';\nimport { getEasingFunction } from './utils/easing.es.js';\nclass Animation {\n  constructor(output, keyframes = [0, 1], {\n    easing,\n    duration: initialDuration = defaults.duration,\n    delay = defaults.delay,\n    endDelay = defaults.endDelay,\n    repeat = defaults.repeat,\n    offset,\n    direction = \"normal\",\n    autoplay = true\n  } = {}) {\n    this.startTime = null;\n    this.rate = 1;\n    this.t = 0;\n    this.cancelTimestamp = null;\n    this.easing = noopReturn;\n    this.duration = 0;\n    this.totalDuration = 0;\n    this.repeat = 0;\n    this.playState = \"idle\";\n    this.finished = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n    easing = easing || defaults.easing;\n    if (isEasingGenerator(easing)) {\n      const custom = easing.createAnimation(keyframes);\n      easing = custom.easing;\n      keyframes = custom.keyframes || keyframes;\n      initialDuration = custom.duration || initialDuration;\n    }\n    this.repeat = repeat;\n    this.easing = isEasingList(easing) ? noopReturn : getEasingFunction(easing);\n    this.updateDuration(initialDuration);\n    const interpolate$1 = interpolate(keyframes, offset, isEasingList(easing) ? easing.map(getEasingFunction) : noopReturn);\n    this.tick = timestamp => {\n      var _a;\n      // TODO: Temporary fix for OptionsResolver typing\n      delay = delay;\n      let t = 0;\n      if (this.pauseTime !== undefined) {\n        t = this.pauseTime;\n      } else {\n        t = (timestamp - this.startTime) * this.rate;\n      }\n      this.t = t;\n      // Convert to seconds\n      t /= 1000;\n      // Rebase on delay\n      t = Math.max(t - delay, 0);\n      /**\n       * If this animation has finished, set the current time\n       * to the total duration.\n       */\n      if (this.playState === \"finished\" && this.pauseTime === undefined) {\n        t = this.totalDuration;\n      }\n      /**\n       * Get the current progress (0-1) of the animation. If t is >\n       * than duration we'll get values like 2.5 (midway through the\n       * third iteration)\n       */\n      const progress = t / this.duration;\n      // TODO progress += iterationStart\n      /**\n       * Get the current iteration (0 indexed). For instance the floor of\n       * 2.5 is 2.\n       */\n      let currentIteration = Math.floor(progress);\n      /**\n       * Get the current progress of the iteration by taking the remainder\n       * so 2.5 is 0.5 through iteration 2\n       */\n      let iterationProgress = progress % 1.0;\n      if (!iterationProgress && progress >= 1) {\n        iterationProgress = 1;\n      }\n      /**\n       * If iteration progress is 1 we count that as the end\n       * of the previous iteration.\n       */\n      iterationProgress === 1 && currentIteration--;\n      /**\n       * Reverse progress if we're not running in \"normal\" direction\n       */\n      const iterationIsOdd = currentIteration % 2;\n      if (direction === \"reverse\" || direction === \"alternate\" && iterationIsOdd || direction === \"alternate-reverse\" && !iterationIsOdd) {\n        iterationProgress = 1 - iterationProgress;\n      }\n      const p = t >= this.totalDuration ? 1 : Math.min(iterationProgress, 1);\n      const latest = interpolate$1(this.easing(p));\n      output(latest);\n      const isAnimationFinished = this.pauseTime === undefined && (this.playState === \"finished\" || t >= this.totalDuration + endDelay);\n      if (isAnimationFinished) {\n        this.playState = \"finished\";\n        (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, latest);\n      } else if (this.playState !== \"idle\") {\n        this.frameRequestId = requestAnimationFrame(this.tick);\n      }\n    };\n    if (autoplay) this.play();\n  }\n  play() {\n    const now = performance.now();\n    this.playState = \"running\";\n    if (this.pauseTime !== undefined) {\n      this.startTime = now - this.pauseTime;\n    } else if (!this.startTime) {\n      this.startTime = now;\n    }\n    this.cancelTimestamp = this.startTime;\n    this.pauseTime = undefined;\n    this.frameRequestId = requestAnimationFrame(this.tick);\n  }\n  pause() {\n    this.playState = \"paused\";\n    this.pauseTime = this.t;\n  }\n  finish() {\n    this.playState = \"finished\";\n    this.tick(0);\n  }\n  stop() {\n    var _a;\n    this.playState = \"idle\";\n    if (this.frameRequestId !== undefined) {\n      cancelAnimationFrame(this.frameRequestId);\n    }\n    (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, false);\n  }\n  cancel() {\n    this.stop();\n    this.tick(this.cancelTimestamp);\n  }\n  reverse() {\n    this.rate *= -1;\n  }\n  commitStyles() {}\n  updateDuration(duration) {\n    this.duration = duration;\n    this.totalDuration = duration * (this.repeat + 1);\n  }\n  get currentTime() {\n    return this.t;\n  }\n  set currentTime(t) {\n    if (this.pauseTime !== undefined || this.rate === 0) {\n      this.pauseTime = t;\n    } else {\n      this.startTime = performance.now() - t / this.rate;\n    }\n  }\n  get playbackRate() {\n    return this.rate;\n  }\n  set playbackRate(rate) {\n    this.rate = rate;\n  }\n}\nexport { Animation };", "map": {"version": 3, "names": ["noopReturn", "defaults", "isEasingGenerator", "isEasingList", "interpolate", "getEasingFunction", "Animation", "constructor", "output", "keyframes", "easing", "duration", "initialDuration", "delay", "endDelay", "repeat", "offset", "direction", "autoplay", "startTime", "rate", "t", "cancelTimestamp", "totalDuration", "playState", "finished", "Promise", "resolve", "reject", "custom", "createAnimation", "updateDuration", "interpolate$1", "map", "tick", "timestamp", "_a", "pauseTime", "undefined", "Math", "max", "progress", "currentIteration", "floor", "iterationProgress", "iterationIsOdd", "p", "min", "latest", "isAnimationFinished", "call", "frameRequestId", "requestAnimationFrame", "play", "now", "performance", "pause", "finish", "stop", "cancelAnimationFrame", "cancel", "reverse", "commitStyles", "currentTime", "playbackRate"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/animation/dist/Animation.es.js"], "sourcesContent": ["import { noopReturn, defaults, isEasingGenerator, isEasingList, interpolate } from '@motionone/utils';\nimport { getEasingFunction } from './utils/easing.es.js';\n\nclass Animation {\n    constructor(output, keyframes = [0, 1], { easing, duration: initialDuration = defaults.duration, delay = defaults.delay, endDelay = defaults.endDelay, repeat = defaults.repeat, offset, direction = \"normal\", autoplay = true, } = {}) {\n        this.startTime = null;\n        this.rate = 1;\n        this.t = 0;\n        this.cancelTimestamp = null;\n        this.easing = noopReturn;\n        this.duration = 0;\n        this.totalDuration = 0;\n        this.repeat = 0;\n        this.playState = \"idle\";\n        this.finished = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n        easing = easing || defaults.easing;\n        if (isEasingGenerator(easing)) {\n            const custom = easing.createAnimation(keyframes);\n            easing = custom.easing;\n            keyframes = custom.keyframes || keyframes;\n            initialDuration = custom.duration || initialDuration;\n        }\n        this.repeat = repeat;\n        this.easing = isEasingList(easing) ? noopReturn : getEasingFunction(easing);\n        this.updateDuration(initialDuration);\n        const interpolate$1 = interpolate(keyframes, offset, isEasingList(easing) ? easing.map(getEasingFunction) : noopReturn);\n        this.tick = (timestamp) => {\n            var _a;\n            // TODO: Temporary fix for OptionsResolver typing\n            delay = delay;\n            let t = 0;\n            if (this.pauseTime !== undefined) {\n                t = this.pauseTime;\n            }\n            else {\n                t = (timestamp - this.startTime) * this.rate;\n            }\n            this.t = t;\n            // Convert to seconds\n            t /= 1000;\n            // Rebase on delay\n            t = Math.max(t - delay, 0);\n            /**\n             * If this animation has finished, set the current time\n             * to the total duration.\n             */\n            if (this.playState === \"finished\" && this.pauseTime === undefined) {\n                t = this.totalDuration;\n            }\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = t / this.duration;\n            // TODO progress += iterationStart\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            iterationProgress === 1 && currentIteration--;\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const iterationIsOdd = currentIteration % 2;\n            if (direction === \"reverse\" ||\n                (direction === \"alternate\" && iterationIsOdd) ||\n                (direction === \"alternate-reverse\" && !iterationIsOdd)) {\n                iterationProgress = 1 - iterationProgress;\n            }\n            const p = t >= this.totalDuration ? 1 : Math.min(iterationProgress, 1);\n            const latest = interpolate$1(this.easing(p));\n            output(latest);\n            const isAnimationFinished = this.pauseTime === undefined &&\n                (this.playState === \"finished\" || t >= this.totalDuration + endDelay);\n            if (isAnimationFinished) {\n                this.playState = \"finished\";\n                (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, latest);\n            }\n            else if (this.playState !== \"idle\") {\n                this.frameRequestId = requestAnimationFrame(this.tick);\n            }\n        };\n        if (autoplay)\n            this.play();\n    }\n    play() {\n        const now = performance.now();\n        this.playState = \"running\";\n        if (this.pauseTime !== undefined) {\n            this.startTime = now - this.pauseTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = now;\n        }\n        this.cancelTimestamp = this.startTime;\n        this.pauseTime = undefined;\n        this.frameRequestId = requestAnimationFrame(this.tick);\n    }\n    pause() {\n        this.playState = \"paused\";\n        this.pauseTime = this.t;\n    }\n    finish() {\n        this.playState = \"finished\";\n        this.tick(0);\n    }\n    stop() {\n        var _a;\n        this.playState = \"idle\";\n        if (this.frameRequestId !== undefined) {\n            cancelAnimationFrame(this.frameRequestId);\n        }\n        (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, false);\n    }\n    cancel() {\n        this.stop();\n        this.tick(this.cancelTimestamp);\n    }\n    reverse() {\n        this.rate *= -1;\n    }\n    commitStyles() { }\n    updateDuration(duration) {\n        this.duration = duration;\n        this.totalDuration = duration * (this.repeat + 1);\n    }\n    get currentTime() {\n        return this.t;\n    }\n    set currentTime(t) {\n        if (this.pauseTime !== undefined || this.rate === 0) {\n            this.pauseTime = t;\n        }\n        else {\n            this.startTime = performance.now() - t / this.rate;\n        }\n    }\n    get playbackRate() {\n        return this.rate;\n    }\n    set playbackRate(rate) {\n        this.rate = rate;\n    }\n}\n\nexport { Animation };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,WAAW,QAAQ,kBAAkB;AACrG,SAASC,iBAAiB,QAAQ,sBAAsB;AAExD,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAACC,MAAM,EAAEC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAAEC,MAAM;IAAEC,QAAQ,EAAEC,eAAe,GAAGX,QAAQ,CAACU,QAAQ;IAAEE,KAAK,GAAGZ,QAAQ,CAACY,KAAK;IAAEC,QAAQ,GAAGb,QAAQ,CAACa,QAAQ;IAAEC,MAAM,GAAGd,QAAQ,CAACc,MAAM;IAAEC,MAAM;IAAEC,SAAS,GAAG,QAAQ;IAAEC,QAAQ,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACpO,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACZ,MAAM,GAAGV,UAAU;IACxB,IAAI,CAACW,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACY,aAAa,GAAG,CAAC;IACtB,IAAI,CAACR,MAAM,GAAG,CAAC;IACf,IAAI,CAACS,SAAS,GAAG,MAAM;IACvB,IAAI,CAACC,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC7C,IAAI,CAACD,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACxB,CAAC,CAAC;IACFlB,MAAM,GAAGA,MAAM,IAAIT,QAAQ,CAACS,MAAM;IAClC,IAAIR,iBAAiB,CAACQ,MAAM,CAAC,EAAE;MAC3B,MAAMmB,MAAM,GAAGnB,MAAM,CAACoB,eAAe,CAACrB,SAAS,CAAC;MAChDC,MAAM,GAAGmB,MAAM,CAACnB,MAAM;MACtBD,SAAS,GAAGoB,MAAM,CAACpB,SAAS,IAAIA,SAAS;MACzCG,eAAe,GAAGiB,MAAM,CAAClB,QAAQ,IAAIC,eAAe;IACxD;IACA,IAAI,CAACG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACL,MAAM,GAAGP,YAAY,CAACO,MAAM,CAAC,GAAGV,UAAU,GAAGK,iBAAiB,CAACK,MAAM,CAAC;IAC3E,IAAI,CAACqB,cAAc,CAACnB,eAAe,CAAC;IACpC,MAAMoB,aAAa,GAAG5B,WAAW,CAACK,SAAS,EAAEO,MAAM,EAAEb,YAAY,CAACO,MAAM,CAAC,GAAGA,MAAM,CAACuB,GAAG,CAAC5B,iBAAiB,CAAC,GAAGL,UAAU,CAAC;IACvH,IAAI,CAACkC,IAAI,GAAIC,SAAS,IAAK;MACvB,IAAIC,EAAE;MACN;MACAvB,KAAK,GAAGA,KAAK;MACb,IAAIQ,CAAC,GAAG,CAAC;MACT,IAAI,IAAI,CAACgB,SAAS,KAAKC,SAAS,EAAE;QAC9BjB,CAAC,GAAG,IAAI,CAACgB,SAAS;MACtB,CAAC,MACI;QACDhB,CAAC,GAAG,CAACc,SAAS,GAAG,IAAI,CAAChB,SAAS,IAAI,IAAI,CAACC,IAAI;MAChD;MACA,IAAI,CAACC,CAAC,GAAGA,CAAC;MACV;MACAA,CAAC,IAAI,IAAI;MACT;MACAA,CAAC,GAAGkB,IAAI,CAACC,GAAG,CAACnB,CAAC,GAAGR,KAAK,EAAE,CAAC,CAAC;MAC1B;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACW,SAAS,KAAK,UAAU,IAAI,IAAI,CAACa,SAAS,KAAKC,SAAS,EAAE;QAC/DjB,CAAC,GAAG,IAAI,CAACE,aAAa;MAC1B;MACA;AACZ;AACA;AACA;AACA;MACY,MAAMkB,QAAQ,GAAGpB,CAAC,GAAG,IAAI,CAACV,QAAQ;MAClC;MACA;AACZ;AACA;AACA;MACY,IAAI+B,gBAAgB,GAAGH,IAAI,CAACI,KAAK,CAACF,QAAQ,CAAC;MAC3C;AACZ;AACA;AACA;MACY,IAAIG,iBAAiB,GAAGH,QAAQ,GAAG,GAAG;MACtC,IAAI,CAACG,iBAAiB,IAAIH,QAAQ,IAAI,CAAC,EAAE;QACrCG,iBAAiB,GAAG,CAAC;MACzB;MACA;AACZ;AACA;AACA;MACYA,iBAAiB,KAAK,CAAC,IAAIF,gBAAgB,EAAE;MAC7C;AACZ;AACA;MACY,MAAMG,cAAc,GAAGH,gBAAgB,GAAG,CAAC;MAC3C,IAAIzB,SAAS,KAAK,SAAS,IACtBA,SAAS,KAAK,WAAW,IAAI4B,cAAe,IAC5C5B,SAAS,KAAK,mBAAmB,IAAI,CAAC4B,cAAe,EAAE;QACxDD,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;MAC7C;MACA,MAAME,CAAC,GAAGzB,CAAC,IAAI,IAAI,CAACE,aAAa,GAAG,CAAC,GAAGgB,IAAI,CAACQ,GAAG,CAACH,iBAAiB,EAAE,CAAC,CAAC;MACtE,MAAMI,MAAM,GAAGhB,aAAa,CAAC,IAAI,CAACtB,MAAM,CAACoC,CAAC,CAAC,CAAC;MAC5CtC,MAAM,CAACwC,MAAM,CAAC;MACd,MAAMC,mBAAmB,GAAG,IAAI,CAACZ,SAAS,KAAKC,SAAS,KACnD,IAAI,CAACd,SAAS,KAAK,UAAU,IAAIH,CAAC,IAAI,IAAI,CAACE,aAAa,GAAGT,QAAQ,CAAC;MACzE,IAAImC,mBAAmB,EAAE;QACrB,IAAI,CAACzB,SAAS,GAAG,UAAU;QAC3B,CAACY,EAAE,GAAG,IAAI,CAACT,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;MAClF,CAAC,MACI,IAAI,IAAI,CAACxB,SAAS,KAAK,MAAM,EAAE;QAChC,IAAI,CAAC2B,cAAc,GAAGC,qBAAqB,CAAC,IAAI,CAAClB,IAAI,CAAC;MAC1D;IACJ,CAAC;IACD,IAAIhB,QAAQ,EACR,IAAI,CAACmC,IAAI,CAAC,CAAC;EACnB;EACAA,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAGC,WAAW,CAACD,GAAG,CAAC,CAAC;IAC7B,IAAI,CAAC9B,SAAS,GAAG,SAAS;IAC1B,IAAI,IAAI,CAACa,SAAS,KAAKC,SAAS,EAAE;MAC9B,IAAI,CAACnB,SAAS,GAAGmC,GAAG,GAAG,IAAI,CAACjB,SAAS;IACzC,CAAC,MACI,IAAI,CAAC,IAAI,CAAClB,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAGmC,GAAG;IACxB;IACA,IAAI,CAAChC,eAAe,GAAG,IAAI,CAACH,SAAS;IACrC,IAAI,CAACkB,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACa,cAAc,GAAGC,qBAAqB,CAAC,IAAI,CAAClB,IAAI,CAAC;EAC1D;EACAsB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAChC,SAAS,GAAG,QAAQ;IACzB,IAAI,CAACa,SAAS,GAAG,IAAI,CAAChB,CAAC;EAC3B;EACAoC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACjC,SAAS,GAAG,UAAU;IAC3B,IAAI,CAACU,IAAI,CAAC,CAAC,CAAC;EAChB;EACAwB,IAAIA,CAAA,EAAG;IACH,IAAItB,EAAE;IACN,IAAI,CAACZ,SAAS,GAAG,MAAM;IACvB,IAAI,IAAI,CAAC2B,cAAc,KAAKb,SAAS,EAAE;MACnCqB,oBAAoB,CAAC,IAAI,CAACR,cAAc,CAAC;IAC7C;IACA,CAACf,EAAE,GAAG,IAAI,CAACR,MAAM,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;EAChF;EACAU,MAAMA,CAAA,EAAG;IACL,IAAI,CAACF,IAAI,CAAC,CAAC;IACX,IAAI,CAACxB,IAAI,CAAC,IAAI,CAACZ,eAAe,CAAC;EACnC;EACAuC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACzC,IAAI,IAAI,CAAC,CAAC;EACnB;EACA0C,YAAYA,CAAA,EAAG,CAAE;EACjB/B,cAAcA,CAACpB,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACY,aAAa,GAAGZ,QAAQ,IAAI,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;EACrD;EACA,IAAIgD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1C,CAAC;EACjB;EACA,IAAI0C,WAAWA,CAAC1C,CAAC,EAAE;IACf,IAAI,IAAI,CAACgB,SAAS,KAAKC,SAAS,IAAI,IAAI,CAAClB,IAAI,KAAK,CAAC,EAAE;MACjD,IAAI,CAACiB,SAAS,GAAGhB,CAAC;IACtB,CAAC,MACI;MACD,IAAI,CAACF,SAAS,GAAGoC,WAAW,CAACD,GAAG,CAAC,CAAC,GAAGjC,CAAC,GAAG,IAAI,CAACD,IAAI;IACtD;EACJ;EACA,IAAI4C,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC5C,IAAI;EACpB;EACA,IAAI4C,YAAYA,CAAC5C,IAAI,EAAE;IACnB,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;AACJ;AAEA,SAASd,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}