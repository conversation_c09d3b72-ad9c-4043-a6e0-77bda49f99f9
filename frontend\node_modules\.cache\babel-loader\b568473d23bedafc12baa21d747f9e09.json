{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nfunction calcNextTime(current, next, prev, labels) {\n  var _a;\n  if (utils.isNumber(next)) {\n    return next;\n  } else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n    return Math.max(0, current + parseFloat(next));\n  } else if (next === \"<\") {\n    return prev;\n  } else {\n    return (_a = labels.get(next)) !== null && _a !== void 0 ? _a : current;\n  }\n}\nexports.calcNextTime = calcNextTime;", "map": {"version": 3, "names": ["utils", "require", "calcNextTime", "current", "next", "prev", "labels", "_a", "isNumber", "startsWith", "Math", "max", "parseFloat", "get", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/timeline/utils/calc-time.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\n\nfunction calcNextTime(current, next, prev, labels) {\n    var _a;\n    if (utils.isNumber(next)) {\n        return next;\n    }\n    else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n        return Math.max(0, current + parseFloat(next));\n    }\n    else if (next === \"<\") {\n        return prev;\n    }\n    else {\n        return (_a = labels.get(next)) !== null && _a !== void 0 ? _a : current;\n    }\n}\n\nexports.calcNextTime = calcNextTime;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,SAASC,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC/C,IAAIC,EAAE;EACN,IAAIP,KAAK,CAACQ,QAAQ,CAACJ,IAAI,CAAC,EAAE;IACtB,OAAOA,IAAI;EACf,CAAC,MACI,IAAIA,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,IAAIL,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,EAAE;IACnD,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,OAAO,GAAGS,UAAU,CAACR,IAAI,CAAC,CAAC;EAClD,CAAC,MACI,IAAIA,IAAI,KAAK,GAAG,EAAE;IACnB,OAAOC,IAAI;EACf,CAAC,MACI;IACD,OAAO,CAACE,EAAE,GAAGD,MAAM,CAACO,GAAG,CAACT,IAAI,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGJ,OAAO;EAC3E;AACJ;AAEAW,OAAO,CAACZ,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}