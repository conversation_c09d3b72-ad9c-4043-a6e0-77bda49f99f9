<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status - Cable Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Cable Management System Status</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Backend Status -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-semibold mb-4">Backend API Status</h2>
                <div id="backendStatus" class="space-y-2">
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-400 mr-2"></span>
                        <span>Checking connection...</span>
                    </div>
                </div>
                <button onclick="testBackend()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Test Backend
                </button>
            </div>

            <!-- Frontend Status -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-semibold mb-4">Frontend Status</h2>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                        <span>Frontend server running</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                        <span>Static files served via HTTP</span>
                    </div>
                </div>
                <a href="index.html" class="mt-4 inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Open Application
                </a>
            </div>

            <!-- API Endpoints -->
            <div class="bg-white p-6 rounded-lg shadow-md md:col-span-2">
                <h2 class="text-xl font-semibold mb-4">API Endpoints Test</h2>
                <div id="apiTests" class="space-y-2">
                    <!-- API test results will be populated here -->
                </div>
                <button onclick="testAllEndpoints()" class="mt-4 bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Test All Endpoints
                </button>
            </div>

            <!-- System Information -->
            <div class="bg-white p-6 rounded-lg shadow-md md:col-span-2">
                <h2 class="text-xl font-semibold mb-4">System Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <strong>Backend URL:</strong><br>
                        <code class="bg-gray-100 px-2 py-1 rounded">http://localhost:5000/api</code>
                    </div>
                    <div>
                        <strong>Frontend URL:</strong><br>
                        <code class="bg-gray-100 px-2 py-1 rounded">http://localhost:8000</code>
                    </div>
                    <div>
                        <strong>Default Credentials:</strong><br>
                        <code class="bg-gray-100 px-2 py-1 rounded">admin / admin123</code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';

        async function testBackend() {
            const statusDiv = document.getElementById('backendStatus');
            statusDiv.innerHTML = '<div class="flex items-center"><span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span><span>Testing...</span></div>';

            try {
                // Test basic connection
                const response = await fetch(`${API_BASE_URL}/stats`);
                const isConnected = response.status === 401 || response.status === 200;
                
                if (isConnected) {
                    statusDiv.innerHTML = `
                        <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span><span>Backend connected (${response.status})</span></div>
                        <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span><span>API responding correctly</span></div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span><span>Backend error (${response.status})</span></div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span><span>Connection failed</span></div>
                    <div class="text-sm text-red-600 mt-2">${error.message}</div>
                `;
            }
        }

        async function testAllEndpoints() {
            const testsDiv = document.getElementById('apiTests');
            testsDiv.innerHTML = '<div class="text-yellow-600">Testing endpoints...</div>';

            const endpoints = [
                { name: 'Health Check', url: '/stats', method: 'GET', expectAuth: true },
                { name: 'Login', url: '/auth/login', method: 'POST', expectAuth: false, body: { username: 'admin', password: 'admin123' } }
            ];

            let results = [];

            for (const endpoint of endpoints) {
                try {
                    const options = {
                        method: endpoint.method,
                        headers: { 'Content-Type': 'application/json' }
                    };

                    if (endpoint.body) {
                        options.body = JSON.stringify(endpoint.body);
                    }

                    const response = await fetch(`${API_BASE_URL}${endpoint.url}`, options);
                    const success = endpoint.expectAuth ? (response.status === 401) : (response.status === 200);
                    
                    results.push({
                        name: endpoint.name,
                        status: response.status,
                        success: success
                    });
                } catch (error) {
                    results.push({
                        name: endpoint.name,
                        status: 'Error',
                        success: false,
                        error: error.message
                    });
                }
            }

            testsDiv.innerHTML = results.map(result => `
                <div class="flex items-center">
                    <span class="w-3 h-3 rounded-full ${result.success ? 'bg-green-500' : 'bg-red-500'} mr-2"></span>
                    <span>${result.name}: ${result.status} ${result.error ? `(${result.error})` : ''}</span>
                </div>
            `).join('');
        }

        // Auto-test on load
        document.addEventListener('DOMContentLoaded', () => {
            testBackend();
            setTimeout(testAllEndpoints, 1000);
        });
    </script>
</body>
</html>
