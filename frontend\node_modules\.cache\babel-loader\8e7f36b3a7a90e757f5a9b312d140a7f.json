{"ast": null, "code": "import { easeInOut } from '../../easing/ease.mjs';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { isEasingArray, easingDefinitionToFunction } from '../utils/easing.mjs';\nfunction defaultEasing(values, easing) {\n  return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction defaultOffset(values) {\n  const numValues = values.length;\n  return values.map((_value, i) => i !== 0 ? i / (numValues - 1) : 0);\n}\nfunction convertOffsetToTimes(offset, duration) {\n  return offset.map(o => o * duration);\n}\nfunction keyframes({\n  keyframes: keyframeValues,\n  ease = easeInOut,\n  times,\n  duration = 300\n}) {\n  keyframeValues = [...keyframeValues];\n  const origin = keyframes[0];\n  /**\n   * Easing functions can be externally defined as strings. Here we convert them\n   * into actual functions.\n   */\n  const easingFunctions = isEasingArray(ease) ? ease.map(easingDefinitionToFunction) : easingDefinitionToFunction(ease);\n  /**\n   * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n   * to reduce GC during animation.\n   */\n  const state = {\n    done: false,\n    value: origin\n  };\n  /**\n   * Create a times array based on the provided 0-1 offsets\n   */\n  const absoluteTimes = convertOffsetToTimes(\n  // Only use the provided offsets if they're the correct length\n  // TODO Maybe we should warn here if there's a length mismatch\n  times && times.length === keyframes.length ? times : defaultOffset(keyframeValues), duration);\n  function createInterpolator() {\n    return interpolate(absoluteTimes, keyframeValues, {\n      ease: Array.isArray(easingFunctions) ? easingFunctions : defaultEasing(keyframeValues, easingFunctions)\n    });\n  }\n  let interpolator = createInterpolator();\n  return {\n    next: t => {\n      state.value = interpolator(t);\n      state.done = t >= duration;\n      return state;\n    },\n    flipTarget: () => {\n      keyframeValues.reverse();\n      interpolator = createInterpolator();\n    }\n  };\n}\nexport { convertOffsetToTimes, defaultEasing, defaultOffset, keyframes };", "map": {"version": 3, "names": ["easeInOut", "interpolate", "isEasingArray", "easingDefinitionToFunction", "defaultEasing", "values", "easing", "map", "splice", "length", "defaultOffset", "numValues", "_value", "i", "convertOffsetToTimes", "offset", "duration", "o", "keyframes", "keyframeValues", "ease", "times", "origin", "easingFunctions", "state", "done", "value", "absoluteTimes", "createInterpolator", "Array", "isArray", "interpolator", "next", "t", "flipTarget", "reverse"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/legacy-popmotion/keyframes.mjs"], "sourcesContent": ["import { easeInOut } from '../../easing/ease.mjs';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { isEasingArray, easingDefinitionToFunction } from '../utils/easing.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction defaultOffset(values) {\n    const numValues = values.length;\n    return values.map((_value, i) => i !== 0 ? i / (numValues - 1) : 0);\n}\nfunction convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\nfunction keyframes({ keyframes: keyframeValues, ease = easeInOut, times, duration = 300, }) {\n    keyframeValues = [...keyframeValues];\n    const origin = keyframes[0];\n    /**\n     * Easing functions can be externally defined as strings. Here we convert them\n     * into actual functions.\n     */\n    const easingFunctions = isEasingArray(ease)\n        ? ease.map(easingDefinitionToFunction)\n        : easingDefinitionToFunction(ease);\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    /**\n     * Create a times array based on the provided 0-1 offsets\n     */\n    const absoluteTimes = convertOffsetToTimes(\n    // Only use the provided offsets if they're the correct length\n    // TODO Maybe we should warn here if there's a length mismatch\n    times && times.length === keyframes.length\n        ? times\n        : defaultOffset(keyframeValues), duration);\n    function createInterpolator() {\n        return interpolate(absoluteTimes, keyframeValues, {\n            ease: Array.isArray(easingFunctions)\n                ? easingFunctions\n                : defaultEasing(keyframeValues, easingFunctions),\n        });\n    }\n    let interpolator = createInterpolator();\n    return {\n        next: (t) => {\n            state.value = interpolator(t);\n            state.done = t >= duration;\n            return state;\n        },\n        flipTarget: () => {\n            keyframeValues.reverse();\n            interpolator = createInterpolator();\n        },\n    };\n}\n\nexport { convertOffsetToTimes, defaultEasing, defaultOffset, keyframes };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,uBAAuB;AACjD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,aAAa,EAAEC,0BAA0B,QAAQ,qBAAqB;AAE/E,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,OAAOD,MAAM,CAACE,GAAG,CAAC,MAAMD,MAAM,IAAIN,SAAS,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC;AAC7E;AACA,SAASC,aAAaA,CAACL,MAAM,EAAE;EAC3B,MAAMM,SAAS,GAAGN,MAAM,CAACI,MAAM;EAC/B,OAAOJ,MAAM,CAACE,GAAG,CAAC,CAACK,MAAM,EAAEC,CAAC,KAAKA,CAAC,KAAK,CAAC,GAAGA,CAAC,IAAIF,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACvE;AACA,SAASG,oBAAoBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,OAAOD,MAAM,CAACR,GAAG,CAAEU,CAAC,IAAKA,CAAC,GAAGD,QAAQ,CAAC;AAC1C;AACA,SAASE,SAASA,CAAC;EAAEA,SAAS,EAAEC,cAAc;EAAEC,IAAI,GAAGpB,SAAS;EAAEqB,KAAK;EAAEL,QAAQ,GAAG;AAAK,CAAC,EAAE;EACxFG,cAAc,GAAG,CAAC,GAAGA,cAAc,CAAC;EACpC,MAAMG,MAAM,GAAGJ,SAAS,CAAC,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACI,MAAMK,eAAe,GAAGrB,aAAa,CAACkB,IAAI,CAAC,GACrCA,IAAI,CAACb,GAAG,CAACJ,0BAA0B,CAAC,GACpCA,0BAA0B,CAACiB,IAAI,CAAC;EACtC;AACJ;AACA;AACA;EACI,MAAMI,KAAK,GAAG;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAEJ;EAAO,CAAC;EAC5C;AACJ;AACA;EACI,MAAMK,aAAa,GAAGb,oBAAoB;EAC1C;EACA;EACAO,KAAK,IAAIA,KAAK,CAACZ,MAAM,KAAKS,SAAS,CAACT,MAAM,GACpCY,KAAK,GACLX,aAAa,CAACS,cAAc,CAAC,EAAEH,QAAQ,CAAC;EAC9C,SAASY,kBAAkBA,CAAA,EAAG;IAC1B,OAAO3B,WAAW,CAAC0B,aAAa,EAAER,cAAc,EAAE;MAC9CC,IAAI,EAAES,KAAK,CAACC,OAAO,CAACP,eAAe,CAAC,GAC9BA,eAAe,GACfnB,aAAa,CAACe,cAAc,EAAEI,eAAe;IACvD,CAAC,CAAC;EACN;EACA,IAAIQ,YAAY,GAAGH,kBAAkB,CAAC,CAAC;EACvC,OAAO;IACHI,IAAI,EAAGC,CAAC,IAAK;MACTT,KAAK,CAACE,KAAK,GAAGK,YAAY,CAACE,CAAC,CAAC;MAC7BT,KAAK,CAACC,IAAI,GAAGQ,CAAC,IAAIjB,QAAQ;MAC1B,OAAOQ,KAAK;IAChB,CAAC;IACDU,UAAU,EAAEA,CAAA,KAAM;MACdf,cAAc,CAACgB,OAAO,CAAC,CAAC;MACxBJ,YAAY,GAAGH,kBAAkB,CAAC,CAAC;IACvC;EACJ,CAAC;AACL;AAEA,SAASd,oBAAoB,EAAEV,aAAa,EAAEM,aAAa,EAAEQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}