{"name": "customer-management-system", "version": "1.0.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "react-scripts": "4.0.3", "axios": "^0.21.1", "react-router-dom": "^5.2.0", "tailwindcss": "^2.2.19", "framer-motion": "^7.6.16", "styled-components": "^5.3.11"}, "devDependencies": {"postcss": "^8.2.15", "autoprefixer": "^10.2.6"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}