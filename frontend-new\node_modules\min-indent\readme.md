# min-indent [![Build Status](https://travis-ci.org/thejameskyle/min-indent.svg?branch=master)](https://travis-ci.org/thejameskyle/min-indent)

> Get the shortest leading whitespace from lines in a string

The line with the least number of leading whitespace, ignoring empty lines, determines the number.

Useful for removing redundant indentation.


## Install

```
$ npm install --save min-indent
```


## Usage

```js
const minIndent = require('min-indent');

const str = '\tunicorn\n\t\tcake';
/*
	unicorn
		cake
*/

minIndent(str); // 1
```


## Related

- [strip-indent](https://github.com/sindresorhus/strip-indent) - Strip leading whitespace from each line in a string
- [strip-indent-cli](https://github.com/sindresorhus/strip-indent-cli) - CLI for this module
- [indent-string](https://github.com/sindresorhus/indent-string) - Indent each line in a string


## License

MIT © [<PERSON>](https://thejameskyle.com)
