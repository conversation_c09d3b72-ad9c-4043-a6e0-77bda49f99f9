'use strict';

/**
 * This file contains all implemented properties that are not a part of any
 * current specifications or drafts, but are handled by browsers nevertheless.
 */

module.exports = [
  'animation',
  'animation-delay',
  'animation-direction',
  'animation-duration',
  'animation-fill-mode',
  'animation-iteration-count',
  'animation-name',
  'animation-play-state',
  'animation-timing-function',
  'appearance',
  'aspect-ratio',
  'backface-visibility',
  'background-clip',
  'background-composite',
  'background-origin',
  'background-size',
  'border-after',
  'border-after-color',
  'border-after-style',
  'border-after-width',
  'border-before',
  'border-before-color',
  'border-before-style',
  'border-before-width',
  'border-end',
  'border-end-color',
  'border-end-style',
  'border-end-width',
  'border-fit',
  'border-horizontal-spacing',
  'border-image',
  'border-radius',
  'border-start',
  'border-start-color',
  'border-start-style',
  'border-start-width',
  'border-vertical-spacing',
  'box-align',
  'box-direction',
  'box-flex',
  'box-flex-group',
  'box-lines',
  'box-ordinal-group',
  'box-orient',
  'box-pack',
  'box-reflect',
  'box-shadow',
  'color-correction',
  'column-axis',
  'column-break-after',
  'column-break-before',
  'column-break-inside',
  'column-count',
  'column-gap',
  'column-rule',
  'column-rule-color',
  'column-rule-style',
  'column-rule-width',
  'columns',
  'column-span',
  'column-width',
  'filter',
  'flex-align',
  'flex-direction',
  'flex-flow',
  'flex-item-align',
  'flex-line-pack',
  'flex-order',
  'flex-pack',
  'flex-wrap',
  'flow-from',
  'flow-into',
  'font-feature-settings',
  'font-kerning',
  'font-size-delta',
  'font-smoothing',
  'font-variant-ligatures',
  'highlight',
  'hyphenate-character',
  'hyphenate-limit-after',
  'hyphenate-limit-before',
  'hyphenate-limit-lines',
  'hyphens',
  'line-align',
  'line-box-contain',
  'line-break',
  'line-clamp',
  'line-grid',
  'line-snap',
  'locale',
  'logical-height',
  'logical-width',
  'margin-after',
  'margin-after-collapse',
  'margin-before',
  'margin-before-collapse',
  'margin-bottom-collapse',
  'margin-collapse',
  'margin-end',
  'margin-start',
  'margin-top-collapse',
  'marquee',
  'marquee-direction',
  'marquee-increment',
  'marquee-repetition',
  'marquee-speed',
  'marquee-style',
  'mask',
  'mask-attachment',
  'mask-box-image',
  'mask-box-image-outset',
  'mask-box-image-repeat',
  'mask-box-image-slice',
  'mask-box-image-source',
  'mask-box-image-width',
  'mask-clip',
  'mask-composite',
  'mask-image',
  'mask-origin',
  'mask-position',
  'mask-position-x',
  'mask-position-y',
  'mask-repeat',
  'mask-repeat-x',
  'mask-repeat-y',
  'mask-size',
  'match-nearest-mail-blockquote-color',
  'max-logical-height',
  'max-logical-width',
  'min-logical-height',
  'min-logical-width',
  'nbsp-mode',
  'overflow-scrolling',
  'padding-after',
  'padding-before',
  'padding-end',
  'padding-start',
  'perspective',
  'perspective-origin',
  'perspective-origin-x',
  'perspective-origin-y',
  'print-color-adjust',
  'region-break-after',
  'region-break-before',
  'region-break-inside',
  'region-overflow',
  'rtl-ordering',
  'svg-shadow',
  'tap-highlight-color',
  'text-combine',
  'text-decorations-in-effect',
  'text-emphasis',
  'text-emphasis-color',
  'text-emphasis-position',
  'text-emphasis-style',
  'text-fill-color',
  'text-orientation',
  'text-security',
  'text-size-adjust',
  'text-stroke',
  'text-stroke-color',
  'text-stroke-width',
  'transform',
  'transform-origin',
  'transform-origin-x',
  'transform-origin-y',
  'transform-origin-z',
  'transform-style',
  'transition',
  'transition-delay',
  'transition-duration',
  'transition-property',
  'transition-timing-function',
  'user-drag',
  'user-modify',
  'user-select',
  'wrap',
  'wrap-flow',
  'wrap-margin',
  'wrap-padding',
  'wrap-shape-inside',
  'wrap-shape-outside',
  'wrap-through',
  'writing-mode',
  'zoom',
].map(prop => 'webkit-' + prop);
