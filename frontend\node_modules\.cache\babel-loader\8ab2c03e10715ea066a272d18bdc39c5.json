{"ast": null, "code": "'use strict';\n\nvar reflectGetProto = require('./Reflect.getPrototypeOf');\nvar originalGetProto = require('./Object.getPrototypeOf');\nvar getDunderProto = require('dunder-proto/get');\n\n/** @type {import('.')} */\nmodule.exports = reflectGetProto ? function getProto(O) {\n  // @ts-expect-error TS can't narrow inside a closure, for some reason\n  return reflectGetProto(O);\n} : originalGetProto ? function getProto(O) {\n  if (!O || typeof O !== 'object' && typeof O !== 'function') {\n    throw new TypeError('getProto: not an object');\n  }\n  // @ts-expect-error TS can't narrow inside a closure, for some reason\n  return originalGetProto(O);\n} : getDunderProto ? function getProto(O) {\n  // @ts-expect-error TS can't narrow inside a closure, for some reason\n  return getDunderProto(O);\n} : null;", "map": {"version": 3, "names": ["reflectGetProto", "require", "originalGetProto", "getDunderProto", "module", "exports", "getProto", "O", "TypeError"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/get-proto/index.js"], "sourcesContent": ["'use strict';\n\nvar reflectGetProto = require('./Reflect.getPrototypeOf');\nvar originalGetProto = require('./Object.getPrototypeOf');\n\nvar getDunderProto = require('dunder-proto/get');\n\n/** @type {import('.')} */\nmodule.exports = reflectGetProto\n\t? function getProto(O) {\n\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\treturn reflectGetProto(O);\n\t}\n\t: originalGetProto\n\t\t? function getProto(O) {\n\t\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\t\tthrow new TypeError('getProto: not an object');\n\t\t\t}\n\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\treturn originalGetProto(O);\n\t\t}\n\t\t: getDunderProto\n\t\t\t? function getProto(O) {\n\t\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\t\treturn getDunderProto(O);\n\t\t\t}\n\t\t\t: null;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACzD,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAEzD,IAAIE,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;;AAEhD;AACAG,MAAM,CAACC,OAAO,GAAGL,eAAe,GAC7B,SAASM,QAAQA,CAACC,CAAC,EAAE;EACtB;EACA,OAAOP,eAAe,CAACO,CAAC,CAAC;AAC1B,CAAC,GACCL,gBAAgB,GACf,SAASI,QAAQA,CAACC,CAAC,EAAE;EACtB,IAAI,CAACA,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,UAAW,EAAE;IAC7D,MAAM,IAAIC,SAAS,CAAC,yBAAyB,CAAC;EAC/C;EACA;EACA,OAAON,gBAAgB,CAACK,CAAC,CAAC;AAC3B,CAAC,GACCJ,cAAc,GACb,SAASG,QAAQA,CAACC,CAAC,EAAE;EACtB;EACA,OAAOJ,cAAc,CAACI,CAAC,CAAC;AACzB,CAAC,GACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script"}