{"ast": null, "code": "'use strict';\n\nvar transforms = require('./transforms.cjs.js');\nfunction getStyleName(key) {\n  if (transforms.transformAlias[key]) key = transforms.transformAlias[key];\n  return transforms.isTransform(key) ? transforms.asTransformCssVar(key) : key;\n}\nexports.getStyleName = getStyleName;", "map": {"version": 3, "names": ["transforms", "require", "getStyleName", "key", "transformAlias", "isTransform", "asTransformCssVar", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/get-style-name.cjs.js"], "sourcesContent": ["'use strict';\n\nvar transforms = require('./transforms.cjs.js');\n\nfunction getStyleName(key) {\n    if (transforms.transformAlias[key])\n        key = transforms.transformAlias[key];\n    return transforms.isTransform(key) ? transforms.asTransformCssVar(key) : key;\n}\n\nexports.getStyleName = getStyleName;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASC,YAAYA,CAACC,GAAG,EAAE;EACvB,IAAIH,UAAU,CAACI,cAAc,CAACD,GAAG,CAAC,EAC9BA,GAAG,GAAGH,UAAU,CAACI,cAAc,CAACD,GAAG,CAAC;EACxC,OAAOH,UAAU,CAACK,WAAW,CAACF,GAAG,CAAC,GAAGH,UAAU,CAACM,iBAAiB,CAACH,GAAG,CAAC,GAAGA,GAAG;AAChF;AAEAI,OAAO,CAACL,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}