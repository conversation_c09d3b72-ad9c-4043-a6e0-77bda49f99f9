"""
Common schemas used across the application
"""

from typing import Any, Dict, List, Optional, Generic, TypeVar
from pydantic import BaseModel, Field, validator
from datetime import datetime, date

T = TypeVar('T')


class BaseSchema(BaseModel):
    """Base schema with common configuration"""
    
    class Config:
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat(),
        }


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(20, ge=1, le=100, description="Page size")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries"""
        return (self.page - 1) * self.size


class SortParams(BaseModel):
    """Sorting parameters"""
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: str = Field("asc", regex="^(asc|desc)$", description="Sort order")


class SearchParams(BaseModel):
    """Search parameters"""
    search: Optional[str] = Field(None, description="Search query")
    search_fields: Optional[List[str]] = Field(None, description="Fields to search in")


class FilterParams(BaseModel):
    """Base filter parameters"""
    date_from: Optional[date] = Field(None, description="Filter from date")
    date_to: Optional[date] = Field(None, description="Filter to date")
    is_active: Optional[bool] = Field(None, description="Filter by active status")


class PaginatedResponse(BaseSchema, Generic[T]):
    """Paginated response wrapper"""
    items: List[T]
    total: int
    page: int
    size: int
    pages: int
    has_next: bool
    has_prev: bool
    
    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        page: int,
        size: int
    ):
        """Create paginated response"""
        pages = (total + size - 1) // size  # Ceiling division
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )


class ResponseMessage(BaseSchema):
    """Standard response message"""
    message: str
    success: bool = True
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseSchema):
    """Error response"""
    error: str
    detail: Optional[str] = None
    code: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthResponse(BaseSchema):
    """Health check response"""
    status: str
    version: str
    environment: str
    timestamp: float
    database: bool = True
    redis: bool = True


class FileUploadResponse(BaseSchema):
    """File upload response"""
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    url: str
    upload_date: datetime = Field(default_factory=datetime.utcnow)


class BulkOperationResponse(BaseSchema):
    """Bulk operation response"""
    total_processed: int
    successful: int
    failed: int
    errors: List[str] = []
    warnings: List[str] = []


class ExportResponse(BaseSchema):
    """Export operation response"""
    filename: str
    file_url: str
    file_size: int
    record_count: int
    export_date: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None


class StatsResponse(BaseSchema):
    """Statistics response"""
    period: str
    data: Dict[str, Any]
    generated_at: datetime = Field(default_factory=datetime.utcnow)


class AddressSchema(BaseSchema):
    """Address schema"""
    address_line1: str = Field(..., max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: str = Field(..., max_length=100)
    state: str = Field(..., max_length=100)
    postal_code: str = Field(..., max_length=20)
    country: str = Field("United States", max_length=100)


class ContactSchema(BaseSchema):
    """Contact information schema"""
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    alternate_phone: Optional[str] = Field(None, max_length=20)
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v
    
    @validator('phone', 'alternate_phone')
    def validate_phone(cls, v):
        if v:
            # Remove all non-digit characters for validation
            digits = ''.join(filter(str.isdigit, v))
            if len(digits) < 10:
                raise ValueError('Phone number must have at least 10 digits')
        return v


class MoneySchema(BaseSchema):
    """Money amount schema"""
    amount: float = Field(..., ge=0)
    currency: str = Field("USD", max_length=3)
    
    @validator('amount')
    def validate_amount(cls, v):
        # Round to 2 decimal places
        return round(v, 2)


class DateRangeSchema(BaseSchema):
    """Date range schema"""
    start_date: date
    end_date: date
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class TimeRangeSchema(BaseSchema):
    """Time range schema"""
    start_time: datetime
    end_time: datetime
    
    @validator('end_time')
    def validate_time_range(cls, v, values):
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('End time must be after start time')
        return v


class TagsSchema(BaseSchema):
    """Tags schema"""
    tags: List[str] = Field(default_factory=list)
    
    @validator('tags')
    def validate_tags(cls, v):
        # Remove empty tags and duplicates
        return list(set(tag.strip() for tag in v if tag.strip()))


class MetadataSchema(BaseSchema):
    """Metadata schema"""
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AuditSchema(BaseSchema):
    """Audit information schema"""
    created_at: datetime
    updated_at: datetime
    created_by_id: Optional[int] = None
    updated_by_id: Optional[int] = None


class SoftDeleteSchema(BaseSchema):
    """Soft delete schema"""
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None


# Utility functions for schema validation
def validate_positive_number(v: float, field_name: str = "value") -> float:
    """Validate that a number is positive"""
    if v < 0:
        raise ValueError(f"{field_name} must be positive")
    return v


def validate_percentage(v: float) -> float:
    """Validate percentage (0-100)"""
    if not 0 <= v <= 100:
        raise ValueError("Percentage must be between 0 and 100")
    return v


def validate_non_empty_string(v: str, field_name: str = "value") -> str:
    """Validate that string is not empty"""
    if not v or not v.strip():
        raise ValueError(f"{field_name} cannot be empty")
    return v.strip()


# Export all schemas and utilities
__all__ = [
    "BaseSchema",
    "PaginationParams",
    "SortParams", 
    "SearchParams",
    "FilterParams",
    "PaginatedResponse",
    "ResponseMessage",
    "ErrorResponse",
    "HealthResponse",
    "FileUploadResponse",
    "BulkOperationResponse",
    "ExportResponse",
    "StatsResponse",
    "AddressSchema",
    "ContactSchema",
    "MoneySchema",
    "DateRangeSchema",
    "TimeRangeSchema",
    "TagsSchema",
    "MetadataSchema",
    "AuditSchema",
    "SoftDeleteSchema",
    "validate_positive_number",
    "validate_percentage",
    "validate_non_empty_string",
]
