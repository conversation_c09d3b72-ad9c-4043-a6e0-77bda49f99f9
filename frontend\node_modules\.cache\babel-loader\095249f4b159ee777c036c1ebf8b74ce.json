{"ast": null, "code": "import { createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nfunction createUseRender(forwardMotionProps = false) {\n  const useRender = (Component, props, projectionId, ref, {\n    latestValues\n  }, isStatic) => {\n    const useVisualProps = isSVGComponent(Component) ? useSVGProps : useHTMLProps;\n    const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n    const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n    const elementProps = {\n      ...filteredProps,\n      ...visualProps,\n      ref\n    };\n    if (projectionId) {\n      elementProps[\"data-projection-id\"] = projectionId;\n    }\n    return createElement(Component, elementProps);\n  };\n  return useRender;\n}\nexport { createUseRender };", "map": {"version": 3, "names": ["createElement", "useHTMLProps", "filterProps", "isSVGComponent", "useSVGProps", "createUseRender", "forwardMotionProps", "useRender", "Component", "props", "projectionId", "ref", "latestValues", "isStatic", "useVisualProps", "visualProps", "filteredProps", "elementProps"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/dom/use-render.mjs"], "sourcesContent": ["import { createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, projectionId, ref, { latestValues }, isStatic) => {\n        const useVisualProps = isSVGComponent(Component)\n            ? useSVGProps\n            : useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = {\n            ...filteredProps,\n            ...visualProps,\n            ref,\n        };\n        if (projectionId) {\n            elementProps[\"data-projection-id\"] = projectionId;\n        }\n        return createElement(Component, elementProps);\n    };\n    return useRender;\n}\n\nexport { createUseRender };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,WAAW,QAAQ,sBAAsB;AAElD,SAASC,eAAeA,CAACC,kBAAkB,GAAG,KAAK,EAAE;EACjD,MAAMC,SAAS,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,GAAG,EAAE;IAAEC;EAAa,CAAC,EAAEC,QAAQ,KAAK;IACnF,MAAMC,cAAc,GAAGX,cAAc,CAACK,SAAS,CAAC,GAC1CJ,WAAW,GACXH,YAAY;IAClB,MAAMc,WAAW,GAAGD,cAAc,CAACL,KAAK,EAAEG,YAAY,EAAEC,QAAQ,EAAEL,SAAS,CAAC;IAC5E,MAAMQ,aAAa,GAAGd,WAAW,CAACO,KAAK,EAAE,OAAOD,SAAS,KAAK,QAAQ,EAAEF,kBAAkB,CAAC;IAC3F,MAAMW,YAAY,GAAG;MACjB,GAAGD,aAAa;MAChB,GAAGD,WAAW;MACdJ;IACJ,CAAC;IACD,IAAID,YAAY,EAAE;MACdO,YAAY,CAAC,oBAAoB,CAAC,GAAGP,YAAY;IACrD;IACA,OAAOV,aAAa,CAACQ,SAAS,EAAES,YAAY,CAAC;EACjD,CAAC;EACD,OAAOV,SAAS;AACpB;AAEA,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}