{"ast": null, "code": "import { isMouseEvent, isTouchEvent } from './utils/event-type.mjs';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { sync, cancelSync } from '../frameloop/index.mjs';\nimport { secondsToMilliseconds } from '../utils/time-conversion.mjs';\nimport { addPointerEvent } from '../events/use-pointer-event.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nimport { distance2D } from '../utils/distance.mjs';\nimport { frameData } from '../frameloop/data.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n  constructor(event, handlers, {\n    transformPagePoint\n  } = {}) {\n    /**\n     * @internal\n     */\n    this.startEvent = null;\n    /**\n     * @internal\n     */\n    this.lastMoveEvent = null;\n    /**\n     * @internal\n     */\n    this.lastMoveEventInfo = null;\n    /**\n     * @internal\n     */\n    this.handlers = {};\n    this.updatePoint = () => {\n      if (!(this.lastMoveEvent && this.lastMoveEventInfo)) return;\n      const info = getPanInfo(this.lastMoveEventInfo, this.history);\n      const isPanStarted = this.startEvent !== null;\n      // Only start panning if the offset is larger than 3 pixels. If we make it\n      // any larger than this we'll want to reset the pointer history\n      // on the first update to avoid visual snapping to the cursoe.\n      const isDistancePastThreshold = distance2D(info.offset, {\n        x: 0,\n        y: 0\n      }) >= 3;\n      if (!isPanStarted && !isDistancePastThreshold) return;\n      const {\n        point\n      } = info;\n      const {\n        timestamp\n      } = frameData;\n      this.history.push({\n        ...point,\n        timestamp\n      });\n      const {\n        onStart,\n        onMove\n      } = this.handlers;\n      if (!isPanStarted) {\n        onStart && onStart(this.lastMoveEvent, info);\n        this.startEvent = this.lastMoveEvent;\n      }\n      onMove && onMove(this.lastMoveEvent, info);\n    };\n    this.handlePointerMove = (event, info) => {\n      this.lastMoveEvent = event;\n      this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n      // Because Safari doesn't trigger mouseup events when it's above a `<select>`\n      if (isMouseEvent(event) && event.buttons === 0) {\n        this.handlePointerUp(event, info);\n        return;\n      }\n      // Throttle mouse move event to once per frame\n      sync.update(this.updatePoint, true);\n    };\n    this.handlePointerUp = (event, info) => {\n      this.end();\n      const {\n        onEnd,\n        onSessionEnd\n      } = this.handlers;\n      const panInfo = getPanInfo(transformPoint(info, this.transformPagePoint), this.history);\n      if (this.startEvent && onEnd) {\n        onEnd(event, panInfo);\n      }\n      onSessionEnd && onSessionEnd(event, panInfo);\n    };\n    // If we have more than one touch, don't start detecting this gesture\n    if (isTouchEvent(event) && event.touches.length > 1) return;\n    this.handlers = handlers;\n    this.transformPagePoint = transformPagePoint;\n    const info = extractEventInfo(event);\n    const initialInfo = transformPoint(info, this.transformPagePoint);\n    const {\n      point\n    } = initialInfo;\n    const {\n      timestamp\n    } = frameData;\n    this.history = [{\n      ...point,\n      timestamp\n    }];\n    const {\n      onSessionStart\n    } = handlers;\n    onSessionStart && onSessionStart(event, getPanInfo(initialInfo, this.history));\n    this.removeListeners = pipe(addPointerEvent(window, \"pointermove\", this.handlePointerMove), addPointerEvent(window, \"pointerup\", this.handlePointerUp), addPointerEvent(window, \"pointercancel\", this.handlePointerUp));\n  }\n  updateHandlers(handlers) {\n    this.handlers = handlers;\n  }\n  end() {\n    this.removeListeners && this.removeListeners();\n    cancelSync.update(this.updatePoint);\n  }\n}\nfunction transformPoint(info, transformPagePoint) {\n  return transformPagePoint ? {\n    point: transformPagePoint(info.point)\n  } : info;\n}\nfunction subtractPoint(a, b) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y\n  };\n}\nfunction getPanInfo({\n  point\n}, history) {\n  return {\n    point,\n    delta: subtractPoint(point, lastDevicePoint(history)),\n    offset: subtractPoint(point, startDevicePoint(history)),\n    velocity: getVelocity(history, 0.1)\n  };\n}\nfunction startDevicePoint(history) {\n  return history[0];\n}\nfunction lastDevicePoint(history) {\n  return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n  if (history.length < 2) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  let i = history.length - 1;\n  let timestampedPoint = null;\n  const lastPoint = lastDevicePoint(history);\n  while (i >= 0) {\n    timestampedPoint = history[i];\n    if (lastPoint.timestamp - timestampedPoint.timestamp > secondsToMilliseconds(timeDelta)) {\n      break;\n    }\n    i--;\n  }\n  if (!timestampedPoint) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  const time = (lastPoint.timestamp - timestampedPoint.timestamp) / 1000;\n  if (time === 0) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  const currentVelocity = {\n    x: (lastPoint.x - timestampedPoint.x) / time,\n    y: (lastPoint.y - timestampedPoint.y) / time\n  };\n  if (currentVelocity.x === Infinity) {\n    currentVelocity.x = 0;\n  }\n  if (currentVelocity.y === Infinity) {\n    currentVelocity.y = 0;\n  }\n  return currentVelocity;\n}\nexport { PanSession };", "map": {"version": 3, "names": ["isMouseEvent", "isTouchEvent", "extractEventInfo", "sync", "cancelSync", "secondsToMilliseconds", "addPointerEvent", "pipe", "distance2D", "frameData", "PanSession", "constructor", "event", "handlers", "transformPagePoint", "startEvent", "lastMoveEvent", "lastMoveEventInfo", "updatePoint", "info", "getPanInfo", "history", "isPanStarted", "isDistancePastThreshold", "offset", "x", "y", "point", "timestamp", "push", "onStart", "onMove", "handlePointerMove", "transformPoint", "buttons", "handlePointerUp", "update", "end", "onEnd", "onSessionEnd", "panInfo", "touches", "length", "initialInfo", "onSessionStart", "removeListeners", "window", "updateHandlers", "subtractPoint", "a", "b", "delta", "lastDevicePoint", "startDevicePoint", "velocity", "getVelocity", "<PERSON><PERSON><PERSON><PERSON>", "i", "timestampedPoint", "lastPoint", "time", "currentVelocity", "Infinity"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/PanSession.mjs"], "sourcesContent": ["import { isMouseEvent, isTouchEvent } from './utils/event-type.mjs';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { sync, cancelSync } from '../frameloop/index.mjs';\nimport { secondsToMilliseconds } from '../utils/time-conversion.mjs';\nimport { addPointerEvent } from '../events/use-pointer-event.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nimport { distance2D } from '../utils/distance.mjs';\nimport { frameData } from '../frameloop/data.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursoe.\n            const isDistancePastThreshold = distance2D(info.offset, { x: 0, y: 0 }) >= 3;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Because Safari doesn't trigger mouseup events when it's above a `<select>`\n            if (isMouseEvent(event) && event.buttons === 0) {\n                this.handlePointerUp(event, info);\n                return;\n            }\n            // Throttle mouse move event to once per frame\n            sync.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd } = this.handlers;\n            const panInfo = getPanInfo(transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (isTouchEvent(event) && event.touches.length > 1)\n            return;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        const info = extractEventInfo(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = pipe(addPointerEvent(window, \"pointermove\", this.handlePointerMove), addPointerEvent(window, \"pointerup\", this.handlePointerUp), addPointerEvent(window, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        cancelSync.update(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            secondsToMilliseconds(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = (lastPoint.timestamp - timestampedPoint.timestamp) / 1000;\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\nexport { PanSession };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,QAAQ,wBAAwB;AACnE,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,IAAI,EAAEC,UAAU,QAAQ,wBAAwB;AACzD,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,SAAS,QAAQ,uBAAuB;;AAEjD;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAAEC;EAAmB,CAAC,GAAG,CAAC,CAAC,EAAE;IACtD;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;AACR;AACA;IACQ,IAAI,CAACJ,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACK,WAAW,GAAG,MAAM;MACrB,IAAI,EAAE,IAAI,CAACF,aAAa,IAAI,IAAI,CAACC,iBAAiB,CAAC,EAC/C;MACJ,MAAME,IAAI,GAAGC,UAAU,CAAC,IAAI,CAACH,iBAAiB,EAAE,IAAI,CAACI,OAAO,CAAC;MAC7D,MAAMC,YAAY,GAAG,IAAI,CAACP,UAAU,KAAK,IAAI;MAC7C;MACA;MACA;MACA,MAAMQ,uBAAuB,GAAGf,UAAU,CAACW,IAAI,CAACK,MAAM,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,CAAC,IAAI,CAAC;MAC5E,IAAI,CAACJ,YAAY,IAAI,CAACC,uBAAuB,EACzC;MACJ,MAAM;QAAEI;MAAM,CAAC,GAAGR,IAAI;MACtB,MAAM;QAAES;MAAU,CAAC,GAAGnB,SAAS;MAC/B,IAAI,CAACY,OAAO,CAACQ,IAAI,CAAC;QAAE,GAAGF,KAAK;QAAEC;MAAU,CAAC,CAAC;MAC1C,MAAM;QAAEE,OAAO;QAAEC;MAAO,CAAC,GAAG,IAAI,CAAClB,QAAQ;MACzC,IAAI,CAACS,YAAY,EAAE;QACfQ,OAAO,IAAIA,OAAO,CAAC,IAAI,CAACd,aAAa,EAAEG,IAAI,CAAC;QAC5C,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACC,aAAa;MACxC;MACAe,MAAM,IAAIA,MAAM,CAAC,IAAI,CAACf,aAAa,EAAEG,IAAI,CAAC;IAC9C,CAAC;IACD,IAAI,CAACa,iBAAiB,GAAG,CAACpB,KAAK,EAAEO,IAAI,KAAK;MACtC,IAAI,CAACH,aAAa,GAAGJ,KAAK;MAC1B,IAAI,CAACK,iBAAiB,GAAGgB,cAAc,CAACd,IAAI,EAAE,IAAI,CAACL,kBAAkB,CAAC;MACtE;MACA,IAAId,YAAY,CAACY,KAAK,CAAC,IAAIA,KAAK,CAACsB,OAAO,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACC,eAAe,CAACvB,KAAK,EAAEO,IAAI,CAAC;QACjC;MACJ;MACA;MACAhB,IAAI,CAACiC,MAAM,CAAC,IAAI,CAAClB,WAAW,EAAE,IAAI,CAAC;IACvC,CAAC;IACD,IAAI,CAACiB,eAAe,GAAG,CAACvB,KAAK,EAAEO,IAAI,KAAK;MACpC,IAAI,CAACkB,GAAG,CAAC,CAAC;MACV,MAAM;QAAEC,KAAK;QAAEC;MAAa,CAAC,GAAG,IAAI,CAAC1B,QAAQ;MAC7C,MAAM2B,OAAO,GAAGpB,UAAU,CAACa,cAAc,CAACd,IAAI,EAAE,IAAI,CAACL,kBAAkB,CAAC,EAAE,IAAI,CAACO,OAAO,CAAC;MACvF,IAAI,IAAI,CAACN,UAAU,IAAIuB,KAAK,EAAE;QAC1BA,KAAK,CAAC1B,KAAK,EAAE4B,OAAO,CAAC;MACzB;MACAD,YAAY,IAAIA,YAAY,CAAC3B,KAAK,EAAE4B,OAAO,CAAC;IAChD,CAAC;IACD;IACA,IAAIvC,YAAY,CAACW,KAAK,CAAC,IAAIA,KAAK,CAAC6B,OAAO,CAACC,MAAM,GAAG,CAAC,EAC/C;IACJ,IAAI,CAAC7B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,MAAMK,IAAI,GAAGjB,gBAAgB,CAACU,KAAK,CAAC;IACpC,MAAM+B,WAAW,GAAGV,cAAc,CAACd,IAAI,EAAE,IAAI,CAACL,kBAAkB,CAAC;IACjE,MAAM;MAAEa;IAAM,CAAC,GAAGgB,WAAW;IAC7B,MAAM;MAAEf;IAAU,CAAC,GAAGnB,SAAS;IAC/B,IAAI,CAACY,OAAO,GAAG,CAAC;MAAE,GAAGM,KAAK;MAAEC;IAAU,CAAC,CAAC;IACxC,MAAM;MAAEgB;IAAe,CAAC,GAAG/B,QAAQ;IACnC+B,cAAc,IACVA,cAAc,CAAChC,KAAK,EAAEQ,UAAU,CAACuB,WAAW,EAAE,IAAI,CAACtB,OAAO,CAAC,CAAC;IAChE,IAAI,CAACwB,eAAe,GAAGtC,IAAI,CAACD,eAAe,CAACwC,MAAM,EAAE,aAAa,EAAE,IAAI,CAACd,iBAAiB,CAAC,EAAE1B,eAAe,CAACwC,MAAM,EAAE,WAAW,EAAE,IAAI,CAACX,eAAe,CAAC,EAAE7B,eAAe,CAACwC,MAAM,EAAE,eAAe,EAAE,IAAI,CAACX,eAAe,CAAC,CAAC;EAC3N;EACAY,cAAcA,CAAClC,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAwB,GAAGA,CAAA,EAAG;IACF,IAAI,CAACQ,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,CAAC;IAC9CzC,UAAU,CAACgC,MAAM,CAAC,IAAI,CAAClB,WAAW,CAAC;EACvC;AACJ;AACA,SAASe,cAAcA,CAACd,IAAI,EAAEL,kBAAkB,EAAE;EAC9C,OAAOA,kBAAkB,GAAG;IAAEa,KAAK,EAAEb,kBAAkB,CAACK,IAAI,CAACQ,KAAK;EAAE,CAAC,GAAGR,IAAI;AAChF;AACA,SAAS6B,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAO;IAAEzB,CAAC,EAAEwB,CAAC,CAACxB,CAAC,GAAGyB,CAAC,CAACzB,CAAC;IAAEC,CAAC,EAAEuB,CAAC,CAACvB,CAAC,GAAGwB,CAAC,CAACxB;EAAE,CAAC;AACzC;AACA,SAASN,UAAUA,CAAC;EAAEO;AAAM,CAAC,EAAEN,OAAO,EAAE;EACpC,OAAO;IACHM,KAAK;IACLwB,KAAK,EAAEH,aAAa,CAACrB,KAAK,EAAEyB,eAAe,CAAC/B,OAAO,CAAC,CAAC;IACrDG,MAAM,EAAEwB,aAAa,CAACrB,KAAK,EAAE0B,gBAAgB,CAAChC,OAAO,CAAC,CAAC;IACvDiC,QAAQ,EAAEC,WAAW,CAAClC,OAAO,EAAE,GAAG;EACtC,CAAC;AACL;AACA,SAASgC,gBAAgBA,CAAChC,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAAC,CAAC,CAAC;AACrB;AACA,SAAS+B,eAAeA,CAAC/B,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACA,OAAO,CAACqB,MAAM,GAAG,CAAC,CAAC;AACtC;AACA,SAASa,WAAWA,CAAClC,OAAO,EAAEmC,SAAS,EAAE;EACrC,IAAInC,OAAO,CAACqB,MAAM,GAAG,CAAC,EAAE;IACpB,OAAO;MAAEjB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,IAAI+B,CAAC,GAAGpC,OAAO,CAACqB,MAAM,GAAG,CAAC;EAC1B,IAAIgB,gBAAgB,GAAG,IAAI;EAC3B,MAAMC,SAAS,GAAGP,eAAe,CAAC/B,OAAO,CAAC;EAC1C,OAAOoC,CAAC,IAAI,CAAC,EAAE;IACXC,gBAAgB,GAAGrC,OAAO,CAACoC,CAAC,CAAC;IAC7B,IAAIE,SAAS,CAAC/B,SAAS,GAAG8B,gBAAgB,CAAC9B,SAAS,GAChDvB,qBAAqB,CAACmD,SAAS,CAAC,EAAE;MAClC;IACJ;IACAC,CAAC,EAAE;EACP;EACA,IAAI,CAACC,gBAAgB,EAAE;IACnB,OAAO;MAAEjC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,MAAMkC,IAAI,GAAG,CAACD,SAAS,CAAC/B,SAAS,GAAG8B,gBAAgB,CAAC9B,SAAS,IAAI,IAAI;EACtE,IAAIgC,IAAI,KAAK,CAAC,EAAE;IACZ,OAAO;MAAEnC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA,MAAMmC,eAAe,GAAG;IACpBpC,CAAC,EAAE,CAACkC,SAAS,CAAClC,CAAC,GAAGiC,gBAAgB,CAACjC,CAAC,IAAImC,IAAI;IAC5ClC,CAAC,EAAE,CAACiC,SAAS,CAACjC,CAAC,GAAGgC,gBAAgB,CAAChC,CAAC,IAAIkC;EAC5C,CAAC;EACD,IAAIC,eAAe,CAACpC,CAAC,KAAKqC,QAAQ,EAAE;IAChCD,eAAe,CAACpC,CAAC,GAAG,CAAC;EACzB;EACA,IAAIoC,eAAe,CAACnC,CAAC,KAAKoC,QAAQ,EAAE;IAChCD,eAAe,CAACnC,CAAC,GAAG,CAAC;EACzB;EACA,OAAOmC,eAAe;AAC1B;AAEA,SAASnD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}