{"ast": null, "code": "import { isNumber } from './is-number.es.js';\nconst isEasingList = easing => Array.isArray(easing) && !isNumber(easing[0]);\nexport { isEasingList };", "map": {"version": 3, "names": ["isNumber", "isEasingList", "easing", "Array", "isArray"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/is-easing-list.es.js"], "sourcesContent": ["import { isNumber } from './is-number.es.js';\n\nconst isEasingList = (easing) => Array.isArray(easing) && !isNumber(easing[0]);\n\nexport { isEasingList };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAE5C,MAAMC,YAAY,GAAIC,MAAM,IAAKC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAI,CAACF,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;AAE9E,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}