"""
Invoice model for generating and managing invoices
"""

import enum
from datetime import datetime, date
from sqlalchemy import Column, <PERSON>, <PERSON>olean, Foreign<PERSON>ey, Integer, Float, Date, DateTime, Text, Enum
from sqlalchemy.orm import relationship

from .base import BaseModel


class InvoiceStatus(enum.Enum):
    """Invoice status options"""
    DRAFT = "draft"
    SENT = "sent"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class Invoice(BaseModel):
    """Invoice model"""
    
    __tablename__ = "invoices"
    
    # Basic Information
    invoice_number = Column(String(50), nullable=False, unique=True, index=True)
    status = Column(Enum(InvoiceStatus), nullable=False, default=InvoiceStatus.DRAFT)
    
    # Dates
    issue_date = Column(Date, nullable=False, default=date.today)
    due_date = Column(Date, nullable=False)
    paid_date = Column(Date, nullable=True)
    
    # Billing Period
    billing_period_start = Column(Date, nullable=False)
    billing_period_end = Column(Date, nullable=False)
    
    # Amounts
    subtotal = Column(Float, nullable=False, default=0.0)
    tax_amount = Column(Float, nullable=False, default=0.0)
    discount_amount = Column(Float, nullable=False, default=0.0)
    total_amount = Column(Float, nullable=False, default=0.0)
    paid_amount = Column(Float, nullable=False, default=0.0)
    
    # References
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Invoice Details
    description = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # File Information
    pdf_file_path = Column(String(500), nullable=True)
    pdf_generated_at = Column(DateTime(timezone=True), nullable=True)
    
    # Email Information
    email_sent_at = Column(DateTime(timezone=True), nullable=True)
    email_sent_to = Column(String(255), nullable=True)
    email_opened_at = Column(DateTime(timezone=True), nullable=True)
    
    # Audit Information
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    customer = relationship("Customer", back_populates="invoices")
    company = relationship("Company", back_populates="invoices")
    created_by = relationship("User", foreign_keys=[created_by_id])
    payments = relationship("Payment", back_populates="invoice", cascade="all, delete-orphan")
    items = relationship("InvoiceItem", back_populates="invoice", cascade="all, delete-orphan")
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return ['invoice_number', 'description', 'notes']
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return [
            'status', 'customer_id', 'company_id', 'issue_date', 
            'due_date', 'paid_date', 'billing_period_start', 'billing_period_end'
        ]
    
    @property
    def is_paid(self) -> bool:
        """Check if invoice is fully paid"""
        return self.status == InvoiceStatus.PAID
    
    @property
    def is_overdue(self) -> bool:
        """Check if invoice is overdue"""
        return self.status == InvoiceStatus.OVERDUE or (
            self.status in [InvoiceStatus.SENT] and 
            self.due_date < date.today()
        )
    
    @property
    def days_overdue(self) -> int:
        """Get number of days overdue"""
        if not self.is_overdue:
            return 0
        return (date.today() - self.due_date).days
    
    @property
    def remaining_amount(self) -> float:
        """Get remaining amount to be paid"""
        return self.total_amount - self.paid_amount
    
    @property
    def payment_percentage(self) -> float:
        """Get payment percentage"""
        if self.total_amount <= 0:
            return 0.0
        return (self.paid_amount / self.total_amount) * 100
    
    def calculate_totals(self):
        """Calculate invoice totals from items"""
        self.subtotal = sum(item.total_amount for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
    
    def add_item(self, description: str, quantity: float, unit_price: float, 
                 plan_id: int = None) -> 'InvoiceItem':
        """Add an item to the invoice"""
        item = InvoiceItem(
            invoice_id=self.id,
            description=description,
            quantity=quantity,
            unit_price=unit_price,
            plan_id=plan_id
        )
        item.calculate_total()
        self.items.append(item)
        self.calculate_totals()
        return item
    
    def remove_item(self, item_id: int):
        """Remove an item from the invoice"""
        self.items = [item for item in self.items if item.id != item_id]
        self.calculate_totals()
    
    def apply_tax(self, tax_rate: float):
        """Apply tax to the invoice"""
        self.tax_amount = self.subtotal * tax_rate
        self.calculate_totals()
    
    def apply_discount(self, discount_amount: float, reason: str = None):
        """Apply a discount to the invoice"""
        self.discount_amount += discount_amount
        self.calculate_totals()
        
        if reason:
            self.add_note(f"Discount applied: ${discount_amount:.2f} - {reason}")
    
    def mark_as_sent(self, email_address: str = None):
        """Mark invoice as sent"""
        self.status = InvoiceStatus.SENT
        self.email_sent_at = datetime.utcnow()
        self.email_sent_to = email_address
    
    def mark_as_paid(self, payment_date: date = None, amount: float = None):
        """Mark invoice as paid"""
        self.status = InvoiceStatus.PAID
        self.paid_date = payment_date or date.today()
        self.paid_amount = amount or self.total_amount
    
    def mark_as_overdue(self):
        """Mark invoice as overdue"""
        if self.status == InvoiceStatus.SENT:
            self.status = InvoiceStatus.OVERDUE
    
    def add_payment(self, amount: float, payment_date: date = None):
        """Add a payment to the invoice"""
        self.paid_amount += amount
        
        if self.paid_amount >= self.total_amount:
            self.mark_as_paid(payment_date, self.paid_amount)
        elif self.paid_amount > 0:
            # Partial payment - keep current status unless it's draft
            if self.status == InvoiceStatus.DRAFT:
                self.status = InvoiceStatus.SENT
    
    def cancel(self, reason: str = None):
        """Cancel the invoice"""
        self.status = InvoiceStatus.CANCELLED
        if reason:
            self.add_note(f"Cancelled: {reason}")
    
    def refund(self, refund_amount: float = None, reason: str = None):
        """Process a refund"""
        refund_amount = refund_amount or self.paid_amount
        
        if refund_amount > self.paid_amount:
            raise ValueError("Refund amount cannot exceed paid amount")
        
        self.paid_amount -= refund_amount
        
        if self.paid_amount <= 0:
            self.status = InvoiceStatus.REFUNDED
        
        if reason:
            self.add_note(f"Refund: ${refund_amount:.2f} - {reason}")
    
    def add_note(self, note: str):
        """Add a note to the invoice"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_note = f"[{timestamp}] {note}"
        
        if self.notes:
            self.notes += f"\n{new_note}"
        else:
            self.notes = new_note
    
    def generate_pdf_filename(self) -> str:
        """Generate PDF filename"""
        return f"invoice_{self.invoice_number}_{self.customer_id}.pdf"
    
    def __repr__(self):
        return f"<Invoice(id={self.id}, number='{self.invoice_number}', total={self.total_amount})>"


class InvoiceItem(BaseModel):
    """Invoice item model"""
    
    __tablename__ = "invoice_items"
    
    # Basic Information
    description = Column(String(500), nullable=False)
    quantity = Column(Float, nullable=False, default=1.0)
    unit_price = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False, default=0.0)
    
    # References
    invoice_id = Column(Integer, ForeignKey("invoices.id"), nullable=False, index=True)
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=True, index=True)
    
    # Additional Information
    notes = Column(Text, nullable=True)
    sort_order = Column(Integer, nullable=False, default=0)
    
    # Relationships
    invoice = relationship("Invoice", back_populates="items")
    plan = relationship("Plan")
    
    def calculate_total(self):
        """Calculate total amount for this item"""
        self.total_amount = self.quantity * self.unit_price
    
    def __repr__(self):
        return f"<InvoiceItem(id={self.id}, description='{self.description}', total={self.total_amount})>"
