{"ast": null, "code": "/**\n * Returns true if the provided key is a CSS variable\n */\nfunction isCSSVariable(key) {\n  return key.startsWith(\"--\");\n}\nexport { isCSSVariable };", "map": {"version": 3, "names": ["isCSSVariable", "key", "startsWith"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs"], "sourcesContent": ["/**\n * Returns true if the provided key is a CSS variable\n */\nfunction isCSSVariable(key) {\n    return key.startsWith(\"--\");\n}\n\nexport { isCSSVariable };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC;AAC/B;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}