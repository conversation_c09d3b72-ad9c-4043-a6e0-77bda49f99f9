{"ast": null, "code": "import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\nconst easeIn = p => p * p;\nconst easeOut = reverseEasing(easeIn);\nconst easeInOut = mirrorEasing(easeIn);\nexport { easeIn, easeInOut, easeOut };", "map": {"version": 3, "names": ["mirrorEasing", "reverseEasing", "easeIn", "p", "easeOut", "easeInOut"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/easing/ease.mjs"], "sourcesContent": ["import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst easeIn = (p) => p * p;\nconst easeOut = reverseEasing(easeIn);\nconst easeInOut = mirrorEasing(easeIn);\n\nexport { easeIn, easeInOut, easeOut };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AAEvD,MAAMC,MAAM,GAAIC,CAAC,IAAKA,CAAC,GAAGA,CAAC;AAC3B,MAAMC,OAAO,GAAGH,aAAa,CAACC,MAAM,CAAC;AACrC,MAAMG,SAAS,GAAGL,YAAY,CAACE,MAAM,CAAC;AAEtC,SAASA,MAAM,EAAEG,SAAS,EAAED,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}