{"ast": null, "code": "const isEasingGenerator = easing => typeof easing === \"object\" && <PERSON><PERSON>an(easing.createAnimation);\nexport { isEasingGenerator };", "map": {"version": 3, "names": ["isEasingGenerator", "easing", "Boolean", "createAnimation"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/is-easing-generator.es.js"], "sourcesContent": ["const isEasingGenerator = (easing) => typeof easing === \"object\" &&\n    Boolean(easing.createAnimation);\n\nexport { isEasingGenerator };\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAIC,MAAM,IAAK,OAAOA,MAAM,KAAK,QAAQ,IAC5DC,OAAO,CAACD,MAAM,CAACE,eAAe,CAAC;AAEnC,SAASH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}