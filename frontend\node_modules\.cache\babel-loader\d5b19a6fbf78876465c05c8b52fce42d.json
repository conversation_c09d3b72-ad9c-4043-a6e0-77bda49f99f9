{"ast": null, "code": "import { isBrowser } from '../utils/is-browser.mjs';\n\n// We check for event support via functions in case they've been mocked by a testing suite.\nconst supportsPointerEvents = () => isBrowser && window.onpointerdown === null;\nconst supportsTouchEvents = () => isBrowser && window.ontouchstart === null;\nconst supportsMouseEvents = () => isBrowser && window.onmousedown === null;\nexport { supportsMouseEvents, supportsPointerEvents, supportsTouchEvents };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "supportsPointerEvents", "window", "onpointerdown", "supportsTouchEvents", "ontouchstart", "supportsMouseEvents", "onmousedown"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/events/utils.mjs"], "sourcesContent": ["import { isBrowser } from '../utils/is-browser.mjs';\n\n// We check for event support via functions in case they've been mocked by a testing suite.\nconst supportsPointerEvents = () => isBrowser && window.onpointerdown === null;\nconst supportsTouchEvents = () => isBrowser && window.ontouchstart === null;\nconst supportsMouseEvents = () => isBrowser && window.onmousedown === null;\n\nexport { supportsMouseEvents, supportsPointerEvents, supportsTouchEvents };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;;AAEnD;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAMD,SAAS,IAAIE,MAAM,CAACC,aAAa,KAAK,IAAI;AAC9E,MAAMC,mBAAmB,GAAGA,CAAA,KAAMJ,SAAS,IAAIE,MAAM,CAACG,YAAY,KAAK,IAAI;AAC3E,MAAMC,mBAAmB,GAAGA,CAAA,KAAMN,SAAS,IAAIE,MAAM,CAACK,WAAW,KAAK,IAAI;AAE1E,SAASD,mBAAmB,EAAEL,qBAAqB,EAAEG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}