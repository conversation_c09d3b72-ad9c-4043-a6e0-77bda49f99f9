{"ast": null, "code": "const createHtmlRenderState = () => ({\n  style: {},\n  transform: {},\n  transformKeys: [],\n  transformOrigin: {},\n  vars: {}\n});\nexport { createHtmlRenderState };", "map": {"version": 3, "names": ["createHtmlRenderState", "style", "transform", "transformKeys", "transform<PERSON><PERSON>in", "vars"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs"], "sourcesContent": ["const createHtmlRenderState = () => ({\n    style: {},\n    transform: {},\n    transformKeys: [],\n    transformOrigin: {},\n    vars: {},\n});\n\nexport { createHtmlRenderState };\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAGA,CAAA,MAAO;EACjCC,KAAK,EAAE,CAAC,CAAC;EACTC,SAAS,EAAE,CAAC,CAAC;EACbC,aAAa,EAAE,EAAE;EACjBC,eAAe,EAAE,CAAC,CAAC;EACnBC,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAEF,SAASL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}