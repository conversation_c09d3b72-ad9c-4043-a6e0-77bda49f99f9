{"ast": null, "code": "import { isEasingList } from './is-easing-list.es.js';\nimport { wrap } from './wrap.es.js';\nfunction getEasingForSegment(easing, i) {\n  return isEasingList(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\nexport { getEasingForSegment };", "map": {"version": 3, "names": ["isEasingList", "wrap", "getEasingForSegment", "easing", "i", "length"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/easing.es.js"], "sourcesContent": ["import { isEasingList } from './is-easing-list.es.js';\nimport { wrap } from './wrap.es.js';\n\nfunction getEasingForSegment(easing, i) {\n    return isEasingList(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\n\nexport { getEasingForSegment };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,wBAAwB;AACrD,SAASC,IAAI,QAAQ,cAAc;AAEnC,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,CAAC,EAAE;EACpC,OAAOJ,YAAY,CAACG,MAAM,CAAC,GAAGA,MAAM,CAACF,IAAI,CAAC,CAAC,EAAEE,MAAM,CAACE,MAAM,EAAED,CAAC,CAAC,CAAC,GAAGD,MAAM;AAC5E;AAEA,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}