{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar inset = require('./inset.cjs.js');\nvar presets = require('./presets.cjs.js');\nvar offset = require('./offset.cjs.js');\nconst point = {\n  x: 0,\n  y: 0\n};\nfunction resolveOffsets(container, info, options) {\n  let {\n    offset: offsetDefinition = presets.ScrollOffset.All\n  } = options;\n  const {\n    target = container,\n    axis = \"y\"\n  } = options;\n  const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n  const inset$1 = target !== container ? inset.calcInset(target, container) : point;\n  /**\n   * Measure the target and container. If they're the same thing then we\n   * use the container's scrollWidth/Height as the target, from there\n   * all other calculations can remain the same.\n   */\n  const targetSize = target === container ? {\n    width: container.scrollWidth,\n    height: container.scrollHeight\n  } : {\n    width: target.clientWidth,\n    height: target.clientHeight\n  };\n  const containerSize = {\n    width: container.clientWidth,\n    height: container.clientHeight\n  };\n  /**\n   * Reset the length of the resolved offset array rather than creating a new one.\n   * TODO: More reusable data structures for targetSize/containerSize would also be good.\n   */\n  info[axis].offset.length = 0;\n  /**\n   * Populate the offset array by resolving the user's offset definition into\n   * a list of pixel scroll offets.\n   */\n  let hasChanged = !info[axis].interpolate;\n  const numOffsets = offsetDefinition.length;\n  for (let i = 0; i < numOffsets; i++) {\n    const offset$1 = offset.resolveOffset(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset$1[axis]);\n    if (!hasChanged && offset$1 !== info[axis].interpolatorOffsets[i]) {\n      hasChanged = true;\n    }\n    info[axis].offset[i] = offset$1;\n  }\n  /**\n   * If the pixel scroll offsets have changed, create a new interpolator function\n   * to map scroll value into a progress.\n   */\n  if (hasChanged) {\n    info[axis].interpolate = utils.interpolate(utils.defaultOffset(numOffsets), info[axis].offset);\n    info[axis].interpolatorOffsets = [...info[axis].offset];\n  }\n  info[axis].progress = info[axis].interpolate(info[axis].current);\n}\nexports.resolveOffsets = resolveOffsets;", "map": {"version": 3, "names": ["utils", "require", "inset", "presets", "offset", "point", "x", "y", "resolveOffsets", "container", "info", "options", "offsetDefinition", "ScrollOffset", "All", "target", "axis", "lengthLabel", "inset$1", "calcInset", "targetSize", "width", "scrollWidth", "height", "scrollHeight", "clientWidth", "clientHeight", "containerSize", "length", "has<PERSON><PERSON>ed", "interpolate", "numOffsets", "i", "offset$1", "resolveOffset", "interpolatorOffsets", "defaultOffset", "progress", "current", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/offsets/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar inset = require('./inset.cjs.js');\nvar presets = require('./presets.cjs.js');\nvar offset = require('./offset.cjs.js');\n\nconst point = { x: 0, y: 0 };\nfunction resolveOffsets(container, info, options) {\n    let { offset: offsetDefinition = presets.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset$1 = target !== container ? inset.calcInset(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : { width: target.clientWidth, height: target.clientHeight };\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset$1 = offset.resolveOffset(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset$1[axis]);\n        if (!hasChanged && offset$1 !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset$1;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = utils.interpolate(utils.defaultOffset(numOffsets), info[axis].offset);\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = info[axis].interpolate(info[axis].current);\n}\n\nexports.resolveOffsets = resolveOffsets;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,KAAK,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACrC,IAAIE,OAAO,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AACzC,IAAIG,MAAM,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AAEvC,MAAMI,KAAK,GAAG;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE;AAAE,CAAC;AAC5B,SAASC,cAAcA,CAACC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC9C,IAAI;IAAEP,MAAM,EAAEQ,gBAAgB,GAAGT,OAAO,CAACU,YAAY,CAACC;EAAI,CAAC,GAAGH,OAAO;EACrE,MAAM;IAAEI,MAAM,GAAGN,SAAS;IAAEO,IAAI,GAAG;EAAI,CAAC,GAAGL,OAAO;EAClD,MAAMM,WAAW,GAAGD,IAAI,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;EACrD,MAAME,OAAO,GAAGH,MAAM,KAAKN,SAAS,GAAGP,KAAK,CAACiB,SAAS,CAACJ,MAAM,EAAEN,SAAS,CAAC,GAAGJ,KAAK;EACjF;AACJ;AACA;AACA;AACA;EACI,MAAMe,UAAU,GAAGL,MAAM,KAAKN,SAAS,GACjC;IAAEY,KAAK,EAAEZ,SAAS,CAACa,WAAW;IAAEC,MAAM,EAAEd,SAAS,CAACe;EAAa,CAAC,GAChE;IAAEH,KAAK,EAAEN,MAAM,CAACU,WAAW;IAAEF,MAAM,EAAER,MAAM,CAACW;EAAa,CAAC;EAChE,MAAMC,aAAa,GAAG;IAClBN,KAAK,EAAEZ,SAAS,CAACgB,WAAW;IAC5BF,MAAM,EAAEd,SAAS,CAACiB;EACtB,CAAC;EACD;AACJ;AACA;AACA;EACIhB,IAAI,CAACM,IAAI,CAAC,CAACZ,MAAM,CAACwB,MAAM,GAAG,CAAC;EAC5B;AACJ;AACA;AACA;EACI,IAAIC,UAAU,GAAG,CAACnB,IAAI,CAACM,IAAI,CAAC,CAACc,WAAW;EACxC,MAAMC,UAAU,GAAGnB,gBAAgB,CAACgB,MAAM;EAC1C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;IACjC,MAAMC,QAAQ,GAAG7B,MAAM,CAAC8B,aAAa,CAACtB,gBAAgB,CAACoB,CAAC,CAAC,EAAEL,aAAa,CAACV,WAAW,CAAC,EAAEG,UAAU,CAACH,WAAW,CAAC,EAAEC,OAAO,CAACF,IAAI,CAAC,CAAC;IAC9H,IAAI,CAACa,UAAU,IAAII,QAAQ,KAAKvB,IAAI,CAACM,IAAI,CAAC,CAACmB,mBAAmB,CAACH,CAAC,CAAC,EAAE;MAC/DH,UAAU,GAAG,IAAI;IACrB;IACAnB,IAAI,CAACM,IAAI,CAAC,CAACZ,MAAM,CAAC4B,CAAC,CAAC,GAAGC,QAAQ;EACnC;EACA;AACJ;AACA;AACA;EACI,IAAIJ,UAAU,EAAE;IACZnB,IAAI,CAACM,IAAI,CAAC,CAACc,WAAW,GAAG9B,KAAK,CAAC8B,WAAW,CAAC9B,KAAK,CAACoC,aAAa,CAACL,UAAU,CAAC,EAAErB,IAAI,CAACM,IAAI,CAAC,CAACZ,MAAM,CAAC;IAC9FM,IAAI,CAACM,IAAI,CAAC,CAACmB,mBAAmB,GAAG,CAAC,GAAGzB,IAAI,CAACM,IAAI,CAAC,CAACZ,MAAM,CAAC;EAC3D;EACAM,IAAI,CAACM,IAAI,CAAC,CAACqB,QAAQ,GAAG3B,IAAI,CAACM,IAAI,CAAC,CAACc,WAAW,CAACpB,IAAI,CAACM,IAAI,CAAC,CAACsB,OAAO,CAAC;AACpE;AAEAC,OAAO,CAAC/B,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script"}