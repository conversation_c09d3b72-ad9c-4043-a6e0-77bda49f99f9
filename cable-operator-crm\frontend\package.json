{"name": "cable-operator-crm-frontend", "version": "1.0.0", "description": "Production-ready Cable Operator CRM Frontend", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^1.9.7", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "axios": "^1.6.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "jspdf": "^2.5.1", "jspdf-autotable": "^3.6.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-redux": "^8.1.3", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "typescript": "^5.2.2", "web-vitals": "^3.5.0", "xlsx": "^0.18.5", "yup": "^1.3.3", "zustand": "^4.4.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/react-table": "^7.7.17", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}