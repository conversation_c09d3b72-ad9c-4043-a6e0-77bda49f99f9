{"ast": null, "code": "import { useDrag } from '../../gestures/drag/use-drag.mjs';\nimport { usePanGesture } from '../../gestures/use-pan-gesture.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\nconst drag = {\n  pan: makeRenderlessComponent(usePanGesture),\n  drag: makeRenderlessComponent(useDrag)\n};\nexport { drag };", "map": {"version": 3, "names": ["useDrag", "usePanGesture", "makeRenderlessComponent", "drag", "pan"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/drag.mjs"], "sourcesContent": ["import { useDrag } from '../../gestures/drag/use-drag.mjs';\nimport { usePanGesture } from '../../gestures/use-pan-gesture.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\n\nconst drag = {\n    pan: makeRenderlessComponent(usePanGesture),\n    drag: makeRenderlessComponent(useDrag),\n};\n\nexport { drag };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,MAAMC,IAAI,GAAG;EACTC,GAAG,EAAEF,uBAAuB,CAACD,aAAa,CAAC;EAC3CE,IAAI,EAAED,uBAAuB,CAACF,OAAO;AACzC,CAAC;AAED,SAASG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}