{"ast": null, "code": "/*\n  Detect and load appropriate clock setting for the execution environment\n */\nconst defaultTimestep = 1 / 60 * 1000;\nconst getCurrentTime = typeof performance !== \"undefined\" ? () => performance.now() : () => Date.now();\nconst onNextFrame = typeof window !== \"undefined\" ? callback => window.requestAnimationFrame(callback) : callback => setTimeout(() => callback(getCurrentTime()), defaultTimestep);\nexport { defaultTimestep, onNextFrame };", "map": {"version": 3, "names": ["defaultTimestep", "getCurrentTime", "performance", "now", "Date", "onNextFrame", "window", "callback", "requestAnimationFrame", "setTimeout"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/frameloop/on-next-frame.mjs"], "sourcesContent": ["/*\n  Detect and load appropriate clock setting for the execution environment\n */\nconst defaultTimestep = (1 / 60) * 1000;\nconst getCurrentTime = typeof performance !== \"undefined\"\n    ? () => performance.now()\n    : () => Date.now();\nconst onNextFrame = typeof window !== \"undefined\"\n    ? (callback) => window.requestAnimationFrame(callback)\n    : (callback) => setTimeout(() => callback(getCurrentTime()), defaultTimestep);\n\nexport { defaultTimestep, onNextFrame };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,eAAe,GAAI,CAAC,GAAG,EAAE,GAAI,IAAI;AACvC,MAAMC,cAAc,GAAG,OAAOC,WAAW,KAAK,WAAW,GACnD,MAAMA,WAAW,CAACC,GAAG,CAAC,CAAC,GACvB,MAAMC,IAAI,CAACD,GAAG,CAAC,CAAC;AACtB,MAAME,WAAW,GAAG,OAAOC,MAAM,KAAK,WAAW,GAC1CC,QAAQ,IAAKD,MAAM,CAACE,qBAAqB,CAACD,QAAQ,CAAC,GACnDA,QAAQ,IAAKE,UAAU,CAAC,MAAMF,QAAQ,CAACN,cAAc,CAAC,CAAC,CAAC,EAAED,eAAe,CAAC;AAEjF,SAASA,eAAe,EAAEK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}