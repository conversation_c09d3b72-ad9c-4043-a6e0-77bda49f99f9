{"ast": null, "code": "import { warning } from 'hey-listen';\nimport { secondsToMilliseconds } from '../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from './waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from './create-instant-animation.mjs';\nimport { animate } from './legacy-popmotion/index.mjs';\nimport { inertia } from './legacy-popmotion/inertia.mjs';\nimport { getDefaultTransition } from './utils/default-transitions.mjs';\nimport { isAnimatable } from './utils/is-animatable.mjs';\nimport { getKeyframes } from './utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from './utils/transitions.mjs';\nimport { supports } from './waapi/supports.mjs';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\"]);\nconst createMotionValueAnimation = (valueName, value, target, transition = {}) => {\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, valueName) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const keyframes = getKeyframes(value, valueName, target, valueTransition);\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n    const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n    let options = {\n      keyframes,\n      velocity: value.getVelocity(),\n      ...valueTransition,\n      elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      }\n    };\n    if (!isOriginAnimatable || !isTargetAnimatable || instantAnimationState.current || valueTransition.type === false) {\n      /**\n       * If we can't animate this value, or the global instant animation flag is set,\n       * or this is simply defined as an instant transition, return an instant transition.\n       */\n      return createInstantAnimation(options);\n    } else if (valueTransition.type === \"inertia\") {\n      /**\n       * If this is an inertia animation, we currently don't support pre-generating\n       * keyframes for this as such it must always run on the main thread.\n       */\n      const animation = inertia(options);\n      return () => animation.stop();\n    }\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      options = {\n        ...options,\n        ...getDefaultTransition(valueName, options)\n      };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n      options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n      options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    const visualElement = value.owner;\n    const element = visualElement && visualElement.current;\n    const canAccelerateAnimation = supports.waapi() && acceleratedValues.has(valueName) && !options.repeatDelay && options.repeatType !== \"mirror\" && options.damping !== 0 && visualElement && element instanceof HTMLElement && !visualElement.getProps().onUpdate;\n    if (canAccelerateAnimation) {\n      /**\n       * If this animation is capable of being run via WAAPI, then do so.\n       */\n      return createAcceleratedAnimation(value, valueName, options);\n    } else {\n      /**\n       * Otherwise, fall back to the main thread.\n       */\n      const animation = animate(options);\n      return () => animation.stop();\n    }\n  };\n};\nexport { createMotionValueAnimation };", "map": {"version": 3, "names": ["warning", "secondsToMilliseconds", "instantAnimationState", "createAcceleratedAnimation", "createInstantAnimation", "animate", "inertia", "getDefaultTransition", "isAnimatable", "getKeyframes", "getValueTransition", "isTransitionDefined", "supports", "acceleratedValues", "Set", "createMotionValueAnimation", "valueName", "value", "target", "transition", "onComplete", "valueTransition", "delay", "elapsed", "keyframes", "originKeyframe", "targetKeyframe", "length", "isOriginAnimatable", "isTargetAnimatable", "options", "velocity", "getVelocity", "onUpdate", "v", "set", "current", "type", "animation", "stop", "duration", "repeatDelay", "visualElement", "owner", "element", "canAccelerateAnimation", "waapi", "has", "repeatType", "damping", "HTMLElement", "getProps"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/index.mjs"], "sourcesContent": ["import { warning } from 'hey-listen';\nimport { secondsToMilliseconds } from '../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from './waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from './create-instant-animation.mjs';\nimport { animate } from './legacy-popmotion/index.mjs';\nimport { inertia } from './legacy-popmotion/inertia.mjs';\nimport { getDefaultTransition } from './utils/default-transitions.mjs';\nimport { isAnimatable } from './utils/is-animatable.mjs';\nimport { getKeyframes } from './utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from './utils/transitions.mjs';\nimport { supports } from './waapi/supports.mjs';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\"]);\nconst createMotionValueAnimation = (valueName, value, target, transition = {}) => {\n    return (onComplete) => {\n        const valueTransition = getValueTransition(transition, valueName) || {};\n        /**\n         * Most transition values are currently completely overwritten by value-specific\n         * transitions. In the future it'd be nicer to blend these transitions. But for now\n         * delay actually does inherit from the root transition if not value-specific.\n         */\n        const delay = valueTransition.delay || transition.delay || 0;\n        /**\n         * Elapsed isn't a public transition option but can be passed through from\n         * optimized appear effects in milliseconds.\n         */\n        let { elapsed = 0 } = transition;\n        elapsed = elapsed - secondsToMilliseconds(delay);\n        const keyframes = getKeyframes(value, valueName, target, valueTransition);\n        /**\n         * Check if we're able to animate between the start and end keyframes,\n         * and throw a warning if we're attempting to animate between one that's\n         * animatable and another that isn't.\n         */\n        const originKeyframe = keyframes[0];\n        const targetKeyframe = keyframes[keyframes.length - 1];\n        const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n        const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n        warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n        let options = {\n            keyframes,\n            velocity: value.getVelocity(),\n            ...valueTransition,\n            elapsed,\n            onUpdate: (v) => {\n                value.set(v);\n                valueTransition.onUpdate && valueTransition.onUpdate(v);\n            },\n            onComplete: () => {\n                onComplete();\n                valueTransition.onComplete && valueTransition.onComplete();\n            },\n        };\n        if (!isOriginAnimatable ||\n            !isTargetAnimatable ||\n            instantAnimationState.current ||\n            valueTransition.type === false) {\n            /**\n             * If we can't animate this value, or the global instant animation flag is set,\n             * or this is simply defined as an instant transition, return an instant transition.\n             */\n            return createInstantAnimation(options);\n        }\n        else if (valueTransition.type === \"inertia\") {\n            /**\n             * If this is an inertia animation, we currently don't support pre-generating\n             * keyframes for this as such it must always run on the main thread.\n             */\n            const animation = inertia(options);\n            return () => animation.stop();\n        }\n        /**\n         * If there's no transition defined for this value, we can generate\n         * unqiue transition settings for this value.\n         */\n        if (!isTransitionDefined(valueTransition)) {\n            options = {\n                ...options,\n                ...getDefaultTransition(valueName, options),\n            };\n        }\n        /**\n         * Both WAAPI and our internal animation functions use durations\n         * as defined by milliseconds, while our external API defines them\n         * as seconds.\n         */\n        if (options.duration) {\n            options.duration = secondsToMilliseconds(options.duration);\n        }\n        if (options.repeatDelay) {\n            options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n        }\n        const visualElement = value.owner;\n        const element = visualElement && visualElement.current;\n        const canAccelerateAnimation = supports.waapi() &&\n            acceleratedValues.has(valueName) &&\n            !options.repeatDelay &&\n            options.repeatType !== \"mirror\" &&\n            options.damping !== 0 &&\n            visualElement &&\n            element instanceof HTMLElement &&\n            !visualElement.getProps().onUpdate;\n        if (canAccelerateAnimation) {\n            /**\n             * If this animation is capable of being run via WAAPI, then do so.\n             */\n            return createAcceleratedAnimation(value, valueName, options);\n        }\n        else {\n            /**\n             * Otherwise, fall back to the main thread.\n             */\n            const animation = animate(options);\n            return () => animation.stop();\n        }\n    };\n};\n\nexport { createMotionValueAnimation };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,0BAA0B,QAAQ,0CAA0C;AACrF,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACjF,SAASC,QAAQ,QAAQ,sBAAsB;;AAE/C;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;AAC9C,MAAMC,0BAA0B,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,GAAG,CAAC,CAAC,KAAK;EAC9E,OAAQC,UAAU,IAAK;IACnB,MAAMC,eAAe,GAAGX,kBAAkB,CAACS,UAAU,EAAEH,SAAS,CAAC,IAAI,CAAC,CAAC;IACvE;AACR;AACA;AACA;AACA;IACQ,MAAMM,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIH,UAAU,CAACG,KAAK,IAAI,CAAC;IAC5D;AACR;AACA;AACA;IACQ,IAAI;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGJ,UAAU;IAChCI,OAAO,GAAGA,OAAO,GAAGtB,qBAAqB,CAACqB,KAAK,CAAC;IAChD,MAAME,SAAS,GAAGf,YAAY,CAACQ,KAAK,EAAED,SAAS,EAAEE,MAAM,EAAEG,eAAe,CAAC;IACzE;AACR;AACA;AACA;AACA;IACQ,MAAMI,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnC,MAAME,cAAc,GAAGF,SAAS,CAACA,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC;IACtD,MAAMC,kBAAkB,GAAGpB,YAAY,CAACQ,SAAS,EAAES,cAAc,CAAC;IAClE,MAAMI,kBAAkB,GAAGrB,YAAY,CAACQ,SAAS,EAAEU,cAAc,CAAC;IAClE1B,OAAO,CAAC4B,kBAAkB,KAAKC,kBAAkB,EAAE,6BAA6Bb,SAAS,UAAUS,cAAc,SAASC,cAAc,MAAMD,cAAc,8DAA8DA,cAAc,6BAA6BC,cAAc,8BAA8B,CAAC;IAClT,IAAII,OAAO,GAAG;MACVN,SAAS;MACTO,QAAQ,EAAEd,KAAK,CAACe,WAAW,CAAC,CAAC;MAC7B,GAAGX,eAAe;MAClBE,OAAO;MACPU,QAAQ,EAAGC,CAAC,IAAK;QACbjB,KAAK,CAACkB,GAAG,CAACD,CAAC,CAAC;QACZb,eAAe,CAACY,QAAQ,IAAIZ,eAAe,CAACY,QAAQ,CAACC,CAAC,CAAC;MAC3D,CAAC;MACDd,UAAU,EAAEA,CAAA,KAAM;QACdA,UAAU,CAAC,CAAC;QACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,CAAC,CAAC;MAC9D;IACJ,CAAC;IACD,IAAI,CAACQ,kBAAkB,IACnB,CAACC,kBAAkB,IACnB3B,qBAAqB,CAACkC,OAAO,IAC7Bf,eAAe,CAACgB,IAAI,KAAK,KAAK,EAAE;MAChC;AACZ;AACA;AACA;MACY,OAAOjC,sBAAsB,CAAC0B,OAAO,CAAC;IAC1C,CAAC,MACI,IAAIT,eAAe,CAACgB,IAAI,KAAK,SAAS,EAAE;MACzC;AACZ;AACA;AACA;MACY,MAAMC,SAAS,GAAGhC,OAAO,CAACwB,OAAO,CAAC;MAClC,OAAO,MAAMQ,SAAS,CAACC,IAAI,CAAC,CAAC;IACjC;IACA;AACR;AACA;AACA;IACQ,IAAI,CAAC5B,mBAAmB,CAACU,eAAe,CAAC,EAAE;MACvCS,OAAO,GAAG;QACN,GAAGA,OAAO;QACV,GAAGvB,oBAAoB,CAACS,SAAS,EAAEc,OAAO;MAC9C,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIA,OAAO,CAACU,QAAQ,EAAE;MAClBV,OAAO,CAACU,QAAQ,GAAGvC,qBAAqB,CAAC6B,OAAO,CAACU,QAAQ,CAAC;IAC9D;IACA,IAAIV,OAAO,CAACW,WAAW,EAAE;MACrBX,OAAO,CAACW,WAAW,GAAGxC,qBAAqB,CAAC6B,OAAO,CAACW,WAAW,CAAC;IACpE;IACA,MAAMC,aAAa,GAAGzB,KAAK,CAAC0B,KAAK;IACjC,MAAMC,OAAO,GAAGF,aAAa,IAAIA,aAAa,CAACN,OAAO;IACtD,MAAMS,sBAAsB,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,CAAC,IAC3CjC,iBAAiB,CAACkC,GAAG,CAAC/B,SAAS,CAAC,IAChC,CAACc,OAAO,CAACW,WAAW,IACpBX,OAAO,CAACkB,UAAU,KAAK,QAAQ,IAC/BlB,OAAO,CAACmB,OAAO,KAAK,CAAC,IACrBP,aAAa,IACbE,OAAO,YAAYM,WAAW,IAC9B,CAACR,aAAa,CAACS,QAAQ,CAAC,CAAC,CAAClB,QAAQ;IACtC,IAAIY,sBAAsB,EAAE;MACxB;AACZ;AACA;MACY,OAAO1C,0BAA0B,CAACc,KAAK,EAAED,SAAS,EAAEc,OAAO,CAAC;IAChE,CAAC,MACI;MACD;AACZ;AACA;MACY,MAAMQ,SAAS,GAAGjC,OAAO,CAACyB,OAAO,CAAC;MAClC,OAAO,MAAMQ,SAAS,CAACC,IAAI,CAAC,CAAC;IACjC;EACJ,CAAC;AACL,CAAC;AAED,SAASxB,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}