{"ast": null, "code": "'use strict';\n\nvar $Object = require('es-object-atoms');\n\n/** @type {import('./Object.getPrototypeOf')} */\nmodule.exports = $Object.getPrototypeOf || null;", "map": {"version": 3, "names": ["$Object", "require", "module", "exports", "getPrototypeOf"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/get-proto/Object.getPrototypeOf.js"], "sourcesContent": ["'use strict';\n\nvar $Object = require('es-object-atoms');\n\n/** @type {import('./Object.getPrototypeOf')} */\nmodule.exports = $Object.getPrototypeOf || null;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAExC;AACAC,MAAM,CAACC,OAAO,GAAGH,OAAO,CAACI,cAAc,IAAI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script"}