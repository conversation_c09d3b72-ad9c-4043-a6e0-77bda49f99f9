"""
Payment model for tracking customer payments
"""

import enum
from datetime import datetime, date
from sqlalchemy import Column, String, <PERSON>olean, Foreign<PERSON>ey, Integer, Float, Date, DateTime, Text, Enum
from sqlalchemy.orm import relationship

from .base import BaseModel


class PaymentStatus(enum.Enum):
    """Payment status options"""
    PENDING = "pending"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    PARTIAL = "partial"


class PaymentMethod(enum.Enum):
    """Payment method options"""
    CASH = "cash"
    CHECK = "check"
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    BANK_TRANSFER = "bank_transfer"
    ONLINE = "online"
    MOBILE_PAYMENT = "mobile_payment"
    OTHER = "other"


class Payment(BaseModel):
    """Payment model"""
    
    __tablename__ = "payments"
    
    # Basic Information
    amount = Column(Float, nullable=False)
    currency = Column(String(3), nullable=False, default="USD")
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING)
    payment_method = Column(Enum(PaymentMethod), nullable=False, default=PaymentMethod.CASH)
    
    # Dates
    due_date = Column(Date, nullable=False)
    paid_date = Column(Date, nullable=True)
    
    # Billing Period
    billing_month = Column(Integer, nullable=False)  # 1-12
    billing_year = Column(Integer, nullable=False)
    billing_period_start = Column(Date, nullable=False)
    billing_period_end = Column(Date, nullable=False)
    
    # References
    invoice_id = Column(Integer, ForeignKey("invoices.id"), nullable=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, index=True)
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Payment Details
    reference_number = Column(String(100), nullable=True, unique=True)
    transaction_id = Column(String(255), nullable=True)
    confirmation_number = Column(String(255), nullable=True)
    
    # Amounts Breakdown
    base_amount = Column(Float, nullable=False, default=0.0)
    tax_amount = Column(Float, nullable=False, default=0.0)
    discount_amount = Column(Float, nullable=False, default=0.0)
    late_fee = Column(Float, nullable=False, default=0.0)
    
    # Payment Processing
    payment_processor = Column(String(100), nullable=True)
    processor_transaction_id = Column(String(255), nullable=True)
    processor_fee = Column(Float, nullable=False, default=0.0)
    
    # Partial Payments
    partial_amount_paid = Column(Float, nullable=False, default=0.0)
    remaining_amount = Column(Float, nullable=False, default=0.0)
    
    # Audit Information
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    processed_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Additional Information
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    tags = Column(String(500), nullable=True)  # Comma-separated tags
    
    # Relationships
    customer = relationship("Customer", back_populates="payments")
    plan = relationship("Plan")
    company = relationship("Company", back_populates="payments")
    invoice = relationship("Invoice", back_populates="payments")
    created_by = relationship("User", foreign_keys=[created_by_id])
    processed_by = relationship("User", foreign_keys=[processed_by_id])
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return [
            'reference_number', 'transaction_id', 'confirmation_number',
            'description', 'notes'
        ]
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return [
            'status', 'payment_method', 'billing_month', 'billing_year',
            'customer_id', 'plan_id', 'company_id', 'due_date', 'paid_date'
        ]
    
    @property
    def is_paid(self) -> bool:
        """Check if payment is fully paid"""
        return self.status == PaymentStatus.PAID
    
    @property
    def is_overdue(self) -> bool:
        """Check if payment is overdue"""
        return self.status == PaymentStatus.OVERDUE or (
            self.status == PaymentStatus.PENDING and 
            self.due_date < date.today()
        )
    
    @property
    def days_overdue(self) -> int:
        """Get number of days overdue"""
        if not self.is_overdue:
            return 0
        return (date.today() - self.due_date).days
    
    @property
    def billing_period_display(self) -> str:
        """Get billing period as display string"""
        months = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ]
        return f"{months[self.billing_month - 1]} {self.billing_year}"
    
    def calculate_total_amount(self) -> float:
        """Calculate total amount including taxes and fees"""
        return self.base_amount + self.tax_amount + self.late_fee - self.discount_amount
    
    def update_amount(self):
        """Update the main amount field based on breakdown"""
        self.amount = self.calculate_total_amount()
        self.remaining_amount = self.amount - self.partial_amount_paid
    
    def mark_as_paid(self, payment_date: date = None, processed_by_id: int = None):
        """Mark payment as paid"""
        self.status = PaymentStatus.PAID
        self.paid_date = payment_date or date.today()
        self.partial_amount_paid = self.amount
        self.remaining_amount = 0.0
        self.processed_by_id = processed_by_id
        self.processed_at = datetime.utcnow()
    
    def mark_as_overdue(self):
        """Mark payment as overdue"""
        if self.status == PaymentStatus.PENDING:
            self.status = PaymentStatus.OVERDUE
    
    def add_partial_payment(self, amount: float, payment_date: date = None):
        """Add a partial payment"""
        self.partial_amount_paid += amount
        self.remaining_amount = self.amount - self.partial_amount_paid
        
        if self.remaining_amount <= 0:
            self.mark_as_paid(payment_date)
        else:
            self.status = PaymentStatus.PARTIAL
    
    def refund(self, refund_amount: float = None, reason: str = None):
        """Process a refund"""
        refund_amount = refund_amount or self.partial_amount_paid
        
        if refund_amount > self.partial_amount_paid:
            raise ValueError("Refund amount cannot exceed paid amount")
        
        self.partial_amount_paid -= refund_amount
        self.remaining_amount = self.amount - self.partial_amount_paid
        
        if self.partial_amount_paid <= 0:
            self.status = PaymentStatus.REFUNDED
        else:
            self.status = PaymentStatus.PARTIAL
        
        if reason:
            self.add_note(f"Refund: ${refund_amount:.2f} - {reason}")
    
    def cancel(self, reason: str = None):
        """Cancel the payment"""
        self.status = PaymentStatus.CANCELLED
        if reason:
            self.add_note(f"Cancelled: {reason}")
    
    def add_late_fee(self, fee_amount: float, reason: str = None):
        """Add a late fee"""
        self.late_fee += fee_amount
        self.update_amount()
        
        note = f"Late fee added: ${fee_amount:.2f}"
        if reason:
            note += f" - {reason}"
        self.add_note(note)
    
    def apply_discount(self, discount_amount: float, reason: str = None):
        """Apply a discount"""
        self.discount_amount += discount_amount
        self.update_amount()
        
        note = f"Discount applied: ${discount_amount:.2f}"
        if reason:
            note += f" - {reason}"
        self.add_note(note)
    
    def add_note(self, note: str):
        """Add a note to the payment"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_note = f"[{timestamp}] {note}"
        
        if self.notes:
            self.notes += f"\n{new_note}"
        else:
            self.notes = new_note
    
    def get_tags_list(self) -> list:
        """Get tags as a list"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]
    
    def add_tag(self, tag: str):
        """Add a tag to payment"""
        current_tags = self.get_tags_list()
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = ", ".join(current_tags)
    
    def remove_tag(self, tag: str):
        """Remove a tag from payment"""
        current_tags = self.get_tags_list()
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = ", ".join(current_tags) if current_tags else None
    
    def generate_reference_number(self):
        """Generate a unique reference number"""
        if not self.reference_number:
            # Format: PAY-YYYYMM-XXXXXX
            year_month = f"{self.billing_year}{self.billing_month:02d}"
            # Use ID or timestamp for uniqueness
            unique_part = str(self.id).zfill(6) if self.id else str(int(datetime.now().timestamp()))[-6:]
            self.reference_number = f"PAY-{year_month}-{unique_part}"
    
    def __repr__(self):
        return f"<Payment(id={self.id}, amount={self.amount}, status='{self.status.value}')>"
