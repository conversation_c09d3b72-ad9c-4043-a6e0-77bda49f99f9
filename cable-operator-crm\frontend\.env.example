# API Configuration
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_API_TIMEOUT=30000

# Environment
REACT_APP_ENVIRONMENT=development
REACT_APP_VERSION=1.0.0

# Features
REACT_APP_ENABLE_DARK_MODE=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_PWA=true

# File Upload
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Maps & Location (if needed)
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Analytics (if enabled)
REACT_APP_GOOGLE_ANALYTICS_ID=your-ga-id
REACT_APP_MIXPANEL_TOKEN=your-mixpanel-token

# Error Monitoring
REACT_APP_SENTRY_DSN=your-sentry-dsn

# Company Branding
REACT_APP_COMPANY_NAME=Cable Operator CRM
REACT_APP_COMPANY_LOGO=/logo.png
REACT_APP_PRIMARY_COLOR=#3B82F6
REACT_APP_SECONDARY_COLOR=#1F2937

# Social Links
REACT_APP_SUPPORT_EMAIL=<EMAIL>
REACT_APP_SUPPORT_PHONE=******-123-4567
REACT_APP_WEBSITE_URL=https://cablecrm.com

# Development
REACT_APP_DEBUG=true
REACT_APP_MOCK_API=false

# PWA
REACT_APP_PWA_NAME=Cable CRM
REACT_APP_PWA_SHORT_NAME=CableCRM
REACT_APP_PWA_DESCRIPTION=Cable Operator Customer Relationship Management System

# Localization
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_SUPPORTED_LANGUAGES=en,es,fr

# Cache
REACT_APP_CACHE_DURATION=300000
REACT_APP_OFFLINE_SUPPORT=true

# Security
REACT_APP_SESSION_TIMEOUT=1800000
REACT_APP_IDLE_TIMEOUT=900000

# UI Settings
REACT_APP_DEFAULT_PAGE_SIZE=20
REACT_APP_MAX_PAGE_SIZE=100
REACT_APP_ANIMATION_DURATION=300

# Chart Colors
REACT_APP_CHART_COLORS=#3B82F6,#10B981,#F59E0B,#EF4444,#8B5CF6,#06B6D4

# Date/Time
REACT_APP_DEFAULT_TIMEZONE=America/New_York
REACT_APP_DATE_FORMAT=MM/dd/yyyy
REACT_APP_TIME_FORMAT=HH:mm

# Currency
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_CURRENCY_SYMBOL=$

# Backup & Export
REACT_APP_EXPORT_FORMATS=csv,xlsx,pdf
REACT_APP_MAX_EXPORT_RECORDS=10000
