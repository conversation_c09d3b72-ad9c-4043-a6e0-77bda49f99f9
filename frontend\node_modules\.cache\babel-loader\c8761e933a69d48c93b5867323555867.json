{"ast": null, "code": "import { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { warnOnce } from '../../utils/warn-once.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction updateMotionValuesFromProps(element, next, prev) {\n  const {\n    willChange\n  } = next;\n  for (const key in next) {\n    const nextValue = next[key];\n    const prevValue = prev[key];\n    if (isMotionValue(nextValue)) {\n      /**\n       * If this is a motion value found in props or style, we want to add it\n       * to our visual element's motion value map.\n       */\n      element.addValue(key, nextValue);\n      if (isWillChangeMotionValue(willChange)) {\n        willChange.add(key);\n      }\n      /**\n       * Check the version of the incoming motion value with this version\n       * and warn against mismatches.\n       */\n      if (process.env.NODE_ENV === \"development\") {\n        warnOnce(nextValue.version === \"7.10.3\", `Attempting to mix Framer Motion versions ${nextValue.version} with 7.10.3 may not work as expected.`);\n      }\n    } else if (isMotionValue(prevValue)) {\n      /**\n       * If we're swapping from a motion value to a static value,\n       * create a new motion value from that\n       */\n      element.addValue(key, motionValue(nextValue, {\n        owner: element\n      }));\n      if (isWillChangeMotionValue(willChange)) {\n        willChange.remove(key);\n      }\n    } else if (prevValue !== nextValue) {\n      /**\n       * If this is a flat value that has changed, update the motion value\n       * or create one if it doesn't exist. We only want to do this if we're\n       * not handling the value with our animation state.\n       */\n      if (element.hasValue(key)) {\n        const existingValue = element.getValue(key);\n        // TODO: Only update values that aren't being animated or even looked at\n        !existingValue.hasAnimated && existingValue.set(nextValue);\n      } else {\n        const latestValue = element.getStaticValue(key);\n        element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue));\n      }\n    }\n  }\n  // Handle removed values\n  for (const key in prev) {\n    if (next[key] === undefined) element.removeValue(key);\n  }\n  return next;\n}\nexport { updateMotionValuesFromProps };", "map": {"version": 3, "names": ["isWillChangeMotionValue", "warnOnce", "motionValue", "isMotionValue", "updateMotionValuesFromProps", "element", "next", "prev", "<PERSON><PERSON><PERSON><PERSON>", "key", "nextValue", "prevValue", "addValue", "add", "process", "env", "NODE_ENV", "version", "owner", "remove", "hasValue", "existingValue", "getValue", "hasAnimated", "set", "latestValue", "getStaticValue", "undefined", "removeValue"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs"], "sourcesContent": ["import { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { warnOnce } from '../../utils/warn-once.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    const { willChange } = next;\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n            if (isWillChangeMotionValue(willChange)) {\n                willChange.add(key);\n            }\n            /**\n             * Check the version of the incoming motion value with this version\n             * and warn against mismatches.\n             */\n            if (process.env.NODE_ENV === \"development\") {\n                warnOnce(nextValue.version === \"7.10.3\", `Attempting to mix Framer Motion versions ${nextValue.version} with 7.10.3 may not work as expected.`);\n            }\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n            if (isWillChangeMotionValue(willChange)) {\n                willChange.remove(key);\n            }\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                // TODO: Only update values that aren't being animated or even looked at\n                !existingValue.hasAnimated && existingValue.set(nextValue);\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,2BAA2BA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtD,MAAM;IAAEC;EAAW,CAAC,GAAGF,IAAI;EAC3B,KAAK,MAAMG,GAAG,IAAIH,IAAI,EAAE;IACpB,MAAMI,SAAS,GAAGJ,IAAI,CAACG,GAAG,CAAC;IAC3B,MAAME,SAAS,GAAGJ,IAAI,CAACE,GAAG,CAAC;IAC3B,IAAIN,aAAa,CAACO,SAAS,CAAC,EAAE;MAC1B;AACZ;AACA;AACA;MACYL,OAAO,CAACO,QAAQ,CAACH,GAAG,EAAEC,SAAS,CAAC;MAChC,IAAIV,uBAAuB,CAACQ,UAAU,CAAC,EAAE;QACrCA,UAAU,CAACK,GAAG,CAACJ,GAAG,CAAC;MACvB;MACA;AACZ;AACA;AACA;MACY,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QACxCf,QAAQ,CAACS,SAAS,CAACO,OAAO,KAAK,QAAQ,EAAE,4CAA4CP,SAAS,CAACO,OAAO,wCAAwC,CAAC;MACnJ;IACJ,CAAC,MACI,IAAId,aAAa,CAACQ,SAAS,CAAC,EAAE;MAC/B;AACZ;AACA;AACA;MACYN,OAAO,CAACO,QAAQ,CAACH,GAAG,EAAEP,WAAW,CAACQ,SAAS,EAAE;QAAEQ,KAAK,EAAEb;MAAQ,CAAC,CAAC,CAAC;MACjE,IAAIL,uBAAuB,CAACQ,UAAU,CAAC,EAAE;QACrCA,UAAU,CAACW,MAAM,CAACV,GAAG,CAAC;MAC1B;IACJ,CAAC,MACI,IAAIE,SAAS,KAAKD,SAAS,EAAE;MAC9B;AACZ;AACA;AACA;AACA;MACY,IAAIL,OAAO,CAACe,QAAQ,CAACX,GAAG,CAAC,EAAE;QACvB,MAAMY,aAAa,GAAGhB,OAAO,CAACiB,QAAQ,CAACb,GAAG,CAAC;QAC3C;QACA,CAACY,aAAa,CAACE,WAAW,IAAIF,aAAa,CAACG,GAAG,CAACd,SAAS,CAAC;MAC9D,CAAC,MACI;QACD,MAAMe,WAAW,GAAGpB,OAAO,CAACqB,cAAc,CAACjB,GAAG,CAAC;QAC/CJ,OAAO,CAACO,QAAQ,CAACH,GAAG,EAAEP,WAAW,CAACuB,WAAW,KAAKE,SAAS,GAAGF,WAAW,GAAGf,SAAS,CAAC,CAAC;MAC3F;IACJ;EACJ;EACA;EACA,KAAK,MAAMD,GAAG,IAAIF,IAAI,EAAE;IACpB,IAAID,IAAI,CAACG,GAAG,CAAC,KAAKkB,SAAS,EACvBtB,OAAO,CAACuB,WAAW,CAACnB,GAAG,CAAC;EAChC;EACA,OAAOH,IAAI;AACf;AAEA,SAASF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}