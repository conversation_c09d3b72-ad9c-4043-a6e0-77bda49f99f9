import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

// Store
import { useAuthStore } from './store/authStore';
import { useThemeStore } from './store/themeStore';

// Components
import LoadingSpinner from './components/ui/LoadingSpinner';
import ErrorBoundary from './components/ui/ErrorBoundary';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/layout/Layout';

// Pages
import LoginPage from './pages/auth/LoginPage';
import SetupPage from './pages/setup/SetupPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import CustomersPage from './pages/customers/CustomersPage';
import PlansPage from './pages/plans/PlansPage';
import PaymentsPage from './pages/payments/PaymentsPage';
import InvoicesPage from './pages/invoices/InvoicesPage';
import ReportsPage from './pages/reports/ReportsPage';
import SettingsPage from './pages/settings/SettingsPage';
import UsersPage from './pages/users/UsersPage';
import NotFoundPage from './pages/NotFoundPage';

// Styles
import './index.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

// Page transition variants
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3,
};

function App() {
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore();
  const { isDarkMode, initializeTheme } = useThemeStore();

  useEffect(() => {
    // Initialize theme
    initializeTheme();
    
    // Check authentication status
    checkAuth();
  }, [initializeTheme, checkAuth]);

  useEffect(() => {
    // Apply dark mode class to document
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
            <AnimatePresence mode="wait">
              <Suspense
                fallback={
                  <div className="min-h-screen flex items-center justify-center">
                    <LoadingSpinner size="lg" />
                  </div>
                }
              >
                <Routes>
                  {/* Public Routes */}
                  <Route
                    path="/login"
                    element={
                      isAuthenticated ? (
                        <Navigate to="/" replace />
                      ) : (
                        <motion.div
                          initial="initial"
                          animate="in"
                          exit="out"
                          variants={pageVariants}
                          transition={pageTransition}
                        >
                          <LoginPage />
                        </motion.div>
                      )
                    }
                  />
                  
                  <Route
                    path="/setup"
                    element={
                      <motion.div
                        initial="initial"
                        animate="in"
                        exit="out"
                        variants={pageVariants}
                        transition={pageTransition}
                      >
                        <SetupPage />
                      </motion.div>
                    }
                  />

                  {/* Protected Routes */}
                  <Route
                    path="/"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <DashboardPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/customers/*"
                    element={
                      <ProtectedRoute requiredPermission="customer:read">
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <CustomersPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/plans/*"
                    element={
                      <ProtectedRoute requiredPermission="plan:read">
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <PlansPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/payments/*"
                    element={
                      <ProtectedRoute requiredPermission="payment:read">
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <PaymentsPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/invoices/*"
                    element={
                      <ProtectedRoute requiredPermission="invoice:read">
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <InvoicesPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/reports/*"
                    element={
                      <ProtectedRoute requiredPermission="report:view">
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <ReportsPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/users/*"
                    element={
                      <ProtectedRoute requiredPermission="user:read">
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <UsersPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/settings/*"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <SettingsPage />
                          </motion.div>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  {/* 404 Page */}
                  <Route
                    path="*"
                    element={
                      <motion.div
                        initial="initial"
                        animate="in"
                        exit="out"
                        variants={pageVariants}
                        transition={pageTransition}
                      >
                        <NotFoundPage />
                      </motion.div>
                    }
                  />
                </Routes>
              </Suspense>
            </AnimatePresence>

            {/* Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                className: 'dark:bg-gray-800 dark:text-white',
                success: {
                  iconTheme: {
                    primary: '#10B981',
                    secondary: '#FFFFFF',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#EF4444',
                    secondary: '#FFFFFF',
                  },
                },
              }}
            />

            {/* React Query Devtools (development only) */}
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </div>
        </Router>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
