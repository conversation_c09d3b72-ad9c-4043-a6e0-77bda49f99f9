{"ast": null, "code": "import { invariant } from 'hey-listen';\nimport { PanSession } from '../PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/use-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\nimport { AnimationType } from '../../render/utils/types.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/use-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { createMotionValueAnimation } from '../../animation/index.mjs';\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: AnyPointerEvent\nclass VisualElementDragControls {\n  constructor(visualElement) {\n    // This is a reference to the global drag gesture lock, ensuring only one component\n    // can \"capture\" the drag of one or both axes.\n    // TODO: Look into moving this into pansession?\n    this.openGlobalLock = null;\n    this.isDragging = false;\n    this.currentDirection = null;\n    this.originPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * The permitted boundaries of travel, in pixels.\n     */\n    this.constraints = false;\n    this.hasMutatedConstraints = false;\n    /**\n     * The per-axis resolved elastic values.\n     */\n    this.elastic = createBox();\n    this.visualElement = visualElement;\n  }\n  start(originEvent, {\n    snapToCursor = false\n  } = {}) {\n    /**\n     * Don't start dragging if this component is exiting\n     */\n    if (this.visualElement.isPresent === false) return;\n    const onSessionStart = event => {\n      // Stop any animations on both axis values immediately. This allows the user to throw and catch\n      // the component.\n      this.stopAnimation();\n      if (snapToCursor) {\n        this.snapToCursor(extractEventInfo(event, \"page\").point);\n      }\n    };\n    const onStart = (event, info) => {\n      var _a;\n      // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n      const {\n        drag,\n        dragPropagation,\n        onDragStart\n      } = this.getProps();\n      if (drag && !dragPropagation) {\n        if (this.openGlobalLock) this.openGlobalLock();\n        this.openGlobalLock = getGlobalLock(drag);\n        // If we don 't have the lock, don't start dragging\n        if (!this.openGlobalLock) return;\n      }\n      this.isDragging = true;\n      this.currentDirection = null;\n      this.resolveConstraints();\n      if (this.visualElement.projection) {\n        this.visualElement.projection.isAnimationBlocked = true;\n        this.visualElement.projection.target = undefined;\n      }\n      /**\n       * Record gesture origin\n       */\n      eachAxis(axis => {\n        var _a, _b;\n        let current = this.getAxisMotionValue(axis).get() || 0;\n        /**\n         * If the MotionValue is a percentage value convert to px\n         */\n        if (percent.test(current)) {\n          const measuredAxis = (_b = (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout) === null || _b === void 0 ? void 0 : _b.layoutBox[axis];\n          if (measuredAxis) {\n            const length = calcLength(measuredAxis);\n            current = length * (parseFloat(current) / 100);\n          }\n        }\n        this.originPoint[axis] = current;\n      });\n      // Fire onDragStart event\n      onDragStart === null || onDragStart === void 0 ? void 0 : onDragStart(event, info);\n      (_a = this.visualElement.animationState) === null || _a === void 0 ? void 0 : _a.setActive(AnimationType.Drag, true);\n    };\n    const onMove = (event, info) => {\n      // latestPointerEvent = event\n      const {\n        dragPropagation,\n        dragDirectionLock,\n        onDirectionLock,\n        onDrag\n      } = this.getProps();\n      // If we didn't successfully receive the gesture lock, early return.\n      if (!dragPropagation && !this.openGlobalLock) return;\n      const {\n        offset\n      } = info;\n      // Attempt to detect drag direction if directionLock is true\n      if (dragDirectionLock && this.currentDirection === null) {\n        this.currentDirection = getCurrentDirection(offset);\n        // If we've successfully set a direction, notify listener\n        if (this.currentDirection !== null) {\n          onDirectionLock === null || onDirectionLock === void 0 ? void 0 : onDirectionLock(this.currentDirection);\n        }\n        return;\n      }\n      // Update each point with the latest position\n      this.updateAxis(\"x\", info.point, offset);\n      this.updateAxis(\"y\", info.point, offset);\n      /**\n       * Ideally we would leave the renderer to fire naturally at the end of\n       * this frame but if the element is about to change layout as the result\n       * of a re-render we want to ensure the browser can read the latest\n       * bounding box to ensure the pointer and element don't fall out of sync.\n       */\n      this.visualElement.render();\n      /**\n       * This must fire after the render call as it might trigger a state\n       * change which itself might trigger a layout update.\n       */\n      onDrag === null || onDrag === void 0 ? void 0 : onDrag(event, info);\n    };\n    const onSessionEnd = (event, info) => this.stop(event, info);\n    this.panSession = new PanSession(originEvent, {\n      onSessionStart,\n      onStart,\n      onMove,\n      onSessionEnd\n    }, {\n      transformPagePoint: this.visualElement.getTransformPagePoint()\n    });\n  }\n  stop(event, info) {\n    const isDragging = this.isDragging;\n    this.cancel();\n    if (!isDragging) return;\n    const {\n      velocity\n    } = info;\n    this.startAnimation(velocity);\n    const {\n      onDragEnd\n    } = this.getProps();\n    onDragEnd === null || onDragEnd === void 0 ? void 0 : onDragEnd(event, info);\n  }\n  cancel() {\n    var _a, _b;\n    this.isDragging = false;\n    if (this.visualElement.projection) {\n      this.visualElement.projection.isAnimationBlocked = false;\n    }\n    (_a = this.panSession) === null || _a === void 0 ? void 0 : _a.end();\n    this.panSession = undefined;\n    const {\n      dragPropagation\n    } = this.getProps();\n    if (!dragPropagation && this.openGlobalLock) {\n      this.openGlobalLock();\n      this.openGlobalLock = null;\n    }\n    (_b = this.visualElement.animationState) === null || _b === void 0 ? void 0 : _b.setActive(AnimationType.Drag, false);\n  }\n  updateAxis(axis, _point, offset) {\n    const {\n      drag\n    } = this.getProps();\n    // If we're not dragging this axis, do an early return.\n    if (!offset || !shouldDrag(axis, drag, this.currentDirection)) return;\n    const axisValue = this.getAxisMotionValue(axis);\n    let next = this.originPoint[axis] + offset[axis];\n    // Apply constraints\n    if (this.constraints && this.constraints[axis]) {\n      next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n    }\n    axisValue.set(next);\n  }\n  resolveConstraints() {\n    const {\n      dragConstraints,\n      dragElastic\n    } = this.getProps();\n    const {\n      layout\n    } = this.visualElement.projection || {};\n    const prevConstraints = this.constraints;\n    if (dragConstraints && isRefObject(dragConstraints)) {\n      if (!this.constraints) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    } else {\n      if (dragConstraints && layout) {\n        this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n      } else {\n        this.constraints = false;\n      }\n    }\n    this.elastic = resolveDragElastic(dragElastic);\n    /**\n     * If we're outputting to external MotionValues, we want to rebase the measured constraints\n     * from viewport-relative to component-relative.\n     */\n    if (prevConstraints !== this.constraints && layout && this.constraints && !this.hasMutatedConstraints) {\n      eachAxis(axis => {\n        if (this.getAxisMotionValue(axis)) {\n          this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n        }\n      });\n    }\n  }\n  resolveRefConstraints() {\n    const {\n      dragConstraints: constraints,\n      onMeasureDragConstraints\n    } = this.getProps();\n    if (!constraints || !isRefObject(constraints)) return false;\n    const constraintsElement = constraints.current;\n    invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n    const {\n      projection\n    } = this.visualElement;\n    // TODO\n    if (!projection || !projection.layout) return false;\n    const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n    let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n    /**\n     * If there's an onMeasureDragConstraints listener we call it and\n     * if different constraints are returned, set constraints to that\n     */\n    if (onMeasureDragConstraints) {\n      const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n      this.hasMutatedConstraints = !!userConstraints;\n      if (userConstraints) {\n        measuredConstraints = convertBoundingBoxToBox(userConstraints);\n      }\n    }\n    return measuredConstraints;\n  }\n  startAnimation(velocity) {\n    const {\n      drag,\n      dragMomentum,\n      dragElastic,\n      dragTransition,\n      dragSnapToOrigin,\n      onDragTransitionEnd\n    } = this.getProps();\n    const constraints = this.constraints || {};\n    const momentumAnimations = eachAxis(axis => {\n      if (!shouldDrag(axis, drag, this.currentDirection)) {\n        return;\n      }\n      let transition = (constraints === null || constraints === void 0 ? void 0 : constraints[axis]) || {};\n      if (dragSnapToOrigin) transition = {\n        min: 0,\n        max: 0\n      };\n      /**\n       * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n       * of spring animations so we should look into adding a disable spring option to `inertia`.\n       * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n       * using the value of `dragElastic`.\n       */\n      const bounceStiffness = dragElastic ? 200 : 1000000;\n      const bounceDamping = dragElastic ? 40 : 10000000;\n      const inertia = {\n        type: \"inertia\",\n        velocity: dragMomentum ? velocity[axis] : 0,\n        bounceStiffness,\n        bounceDamping,\n        timeConstant: 750,\n        restDelta: 1,\n        restSpeed: 10,\n        ...dragTransition,\n        ...transition\n      };\n      // If we're not animating on an externally-provided `MotionValue` we can use the\n      // component's animation controls which will handle interactions with whileHover (etc),\n      // otherwise we just have to animate the `MotionValue` itself.\n      return this.startAxisValueAnimation(axis, inertia);\n    });\n    // Run all animations and then resolve the new drag constraints.\n    return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n  }\n  startAxisValueAnimation(axis, transition) {\n    const axisValue = this.getAxisMotionValue(axis);\n    return axisValue.start(createMotionValueAnimation(axis, axisValue, 0, transition));\n  }\n  stopAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).stop());\n  }\n  /**\n   * Drag works differently depending on which props are provided.\n   *\n   * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n   * - Otherwise, we apply the delta to the x/y motion values.\n   */\n  getAxisMotionValue(axis) {\n    var _a;\n    const dragKey = \"_drag\" + axis.toUpperCase();\n    const externalMotionValue = this.visualElement.getProps()[dragKey];\n    return externalMotionValue ? externalMotionValue : this.visualElement.getValue(axis, ((_a = this.visualElement.getProps().initial) === null || _a === void 0 ? void 0 : _a[axis]) || 0);\n  }\n  snapToCursor(point) {\n    eachAxis(axis => {\n      const {\n        drag\n      } = this.getProps();\n      // If we're not dragging this axis, do an early return.\n      if (!shouldDrag(axis, drag, this.currentDirection)) return;\n      const {\n        projection\n      } = this.visualElement;\n      const axisValue = this.getAxisMotionValue(axis);\n      if (projection && projection.layout) {\n        const {\n          min,\n          max\n        } = projection.layout.layoutBox[axis];\n        axisValue.set(point[axis] - mix(min, max, 0.5));\n      }\n    });\n  }\n  /**\n   * When the viewport resizes we want to check if the measured constraints\n   * have changed and, if so, reposition the element within those new constraints\n   * relative to where it was before the resize.\n   */\n  scalePositionWithinConstraints() {\n    var _a;\n    if (!this.visualElement.current) return;\n    const {\n      drag,\n      dragConstraints\n    } = this.getProps();\n    const {\n      projection\n    } = this.visualElement;\n    if (!isRefObject(dragConstraints) || !projection || !this.constraints) return;\n    /**\n     * Stop current animations as there can be visual glitching if we try to do\n     * this mid-animation\n     */\n    this.stopAnimation();\n    /**\n     * Record the relative position of the dragged element relative to the\n     * constraints box and save as a progress value.\n     */\n    const boxProgress = {\n      x: 0,\n      y: 0\n    };\n    eachAxis(axis => {\n      const axisValue = this.getAxisMotionValue(axis);\n      if (axisValue) {\n        const latest = axisValue.get();\n        boxProgress[axis] = calcOrigin({\n          min: latest,\n          max: latest\n        }, this.constraints[axis]);\n      }\n    });\n    /**\n     * Update the layout of this element and resolve the latest drag constraints\n     */\n    const {\n      transformTemplate\n    } = this.visualElement.getProps();\n    this.visualElement.current.style.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n    (_a = projection.root) === null || _a === void 0 ? void 0 : _a.updateScroll();\n    projection.updateLayout();\n    this.resolveConstraints();\n    /**\n     * For each axis, calculate the current progress of the layout axis\n     * within the new constraints.\n     */\n    eachAxis(axis => {\n      if (!shouldDrag(axis, drag, null)) return;\n      /**\n       * Calculate a new transform based on the previous box progress\n       */\n      const axisValue = this.getAxisMotionValue(axis);\n      const {\n        min,\n        max\n      } = this.constraints[axis];\n      axisValue.set(mix(min, max, boxProgress[axis]));\n    });\n  }\n  addListeners() {\n    var _a;\n    if (!this.visualElement.current) return;\n    elementDragControls.set(this.visualElement, this);\n    const element = this.visualElement.current;\n    /**\n     * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n     */\n    const stopPointerListener = addPointerEvent(element, \"pointerdown\", event => {\n      const {\n        drag,\n        dragListener = true\n      } = this.getProps();\n      drag && dragListener && this.start(event);\n    });\n    const measureDragConstraints = () => {\n      const {\n        dragConstraints\n      } = this.getProps();\n      if (isRefObject(dragConstraints)) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    };\n    const {\n      projection\n    } = this.visualElement;\n    const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n    if (projection && !projection.layout) {\n      (_a = projection.root) === null || _a === void 0 ? void 0 : _a.updateScroll();\n      projection.updateLayout();\n    }\n    measureDragConstraints();\n    /**\n     * Attach a window resize listener to scale the draggable target within its defined\n     * constraints as the window resizes.\n     */\n    const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n    /**\n     * If the element's layout changes, calculate the delta and apply that to\n     * the drag gesture's origin point.\n     */\n    const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", ({\n      delta,\n      hasLayoutChanged\n    }) => {\n      if (this.isDragging && hasLayoutChanged) {\n        eachAxis(axis => {\n          const motionValue = this.getAxisMotionValue(axis);\n          if (!motionValue) return;\n          this.originPoint[axis] += delta[axis].translate;\n          motionValue.set(motionValue.get() + delta[axis].translate);\n        });\n        this.visualElement.render();\n      }\n    });\n    return () => {\n      stopResizeListener();\n      stopPointerListener();\n      stopMeasureLayoutListener();\n      stopLayoutUpdateListener === null || stopLayoutUpdateListener === void 0 ? void 0 : stopLayoutUpdateListener();\n    };\n  }\n  getProps() {\n    const props = this.visualElement.getProps();\n    const {\n      drag = false,\n      dragDirectionLock = false,\n      dragPropagation = false,\n      dragConstraints = false,\n      dragElastic = defaultElastic,\n      dragMomentum = true\n    } = props;\n    return {\n      ...props,\n      drag,\n      dragDirectionLock,\n      dragPropagation,\n      dragConstraints,\n      dragElastic,\n      dragMomentum\n    };\n  }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n  return (drag === true || drag === direction) && (currentDirection === null || currentDirection === direction);\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n  let direction = null;\n  if (Math.abs(offset.y) > lockThreshold) {\n    direction = \"y\";\n  } else if (Math.abs(offset.x) > lockThreshold) {\n    direction = \"x\";\n  }\n  return direction;\n}\nexport { VisualElementDragControls, elementDragControls };", "map": {"version": 3, "names": ["invariant", "PanSession", "getGlobalLock", "isRefObject", "addPointerEvent", "applyConstraints", "calcRelativeConstraints", "resolveDragElastic", "rebaseAxisConstraints", "calcViewportConstraints", "calcOrigin", "defaultElastic", "AnimationType", "createBox", "eachAxis", "measurePageBox", "extractEventInfo", "convertBoxToBoundingBox", "convertBoundingBoxToBox", "addDomEvent", "calcLength", "mix", "percent", "createMotionValueAnimation", "elementDragControls", "WeakMap", "VisualElementDragControls", "constructor", "visualElement", "openGlobalLock", "isDragging", "currentDirection", "originPoint", "x", "y", "constraints", "hasMutatedConstraints", "elastic", "start", "originEvent", "snapToCursor", "isPresent", "onSessionStart", "event", "stopAnimation", "point", "onStart", "info", "_a", "drag", "dragPropagation", "onDragStart", "getProps", "resolveConstraints", "projection", "isAnimationBlocked", "target", "undefined", "axis", "_b", "current", "getAxisMotionValue", "get", "test", "measuredAxis", "layout", "layoutBox", "length", "parseFloat", "animationState", "setActive", "Drag", "onMove", "dragDirectionLock", "onDirectionLock", "onDrag", "offset", "getCurrentDirection", "updateAxis", "render", "onSessionEnd", "stop", "panSession", "transformPagePoint", "getTransformPagePoint", "cancel", "velocity", "startAnimation", "onDragEnd", "end", "_point", "shouldDrag", "axisValue", "next", "set", "dragConstraints", "dragElastic", "prevConstraints", "resolveRefConstraints", "onMeasureDragConstraints", "constraintsElement", "constraintsBox", "root", "measuredConstraints", "userConstraints", "dragMomentum", "dragTransition", "dragSnapToO<PERSON>in", "onDragTransitionEnd", "momentumAnimations", "transition", "min", "max", "bounceStiffness", "bounceDamping", "inertia", "type", "timeConstant", "restDelta", "restSpeed", "startAxisValueAnimation", "Promise", "all", "then", "drag<PERSON>ey", "toUpperCase", "externalMotionValue", "getValue", "initial", "scalePositionWithinConstraints", "boxProgress", "latest", "transformTemplate", "style", "transform", "updateScroll", "updateLayout", "addListeners", "element", "stopPointerListener", "dragListener", "measureDragConstraints", "stopMeasureLayoutListener", "addEventListener", "stopResizeListener", "window", "stopLayoutUpdateListener", "delta", "hasLayoutChanged", "motionValue", "translate", "props", "direction", "lockThreshold", "Math", "abs"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { invariant } from 'hey-listen';\nimport { PanSession } from '../PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/use-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\nimport { AnimationType } from '../../render/utils/types.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/use-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { createMotionValueAnimation } from '../../animation/index.mjs';\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: AnyPointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        // This is a reference to the global drag gesture lock, ensuring only one component\n        // can \"capture\" the drag of one or both axes.\n        // TODO: Look into moving this into pansession?\n        this.openGlobalLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        if (this.visualElement.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            // Stop any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event, \"page\").point);\n            }\n        };\n        const onStart = (event, info) => {\n            var _a;\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openGlobalLock)\n                    this.openGlobalLock();\n                this.openGlobalLock = getGlobalLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openGlobalLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                var _a, _b;\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const measuredAxis = (_b = (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout) === null || _b === void 0 ? void 0 : _b.layoutBox[axis];\n                    if (measuredAxis) {\n                        const length = calcLength(measuredAxis);\n                        current = length * (parseFloat(current) / 100);\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            onDragStart === null || onDragStart === void 0 ? void 0 : onDragStart(event, info);\n            (_a = this.visualElement.animationState) === null || _a === void 0 ? void 0 : _a.setActive(AnimationType.Drag, true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openGlobalLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock === null || onDirectionLock === void 0 ? void 0 : onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag === null || onDrag === void 0 ? void 0 : onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n        }, { transformPagePoint: this.visualElement.getTransformPagePoint() });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        onDragEnd === null || onDragEnd === void 0 ? void 0 : onDragEnd(event, info);\n    }\n    cancel() {\n        var _a, _b;\n        this.isDragging = false;\n        if (this.visualElement.projection) {\n            this.visualElement.projection.isAnimationBlocked = false;\n        }\n        (_a = this.panSession) === null || _a === void 0 ? void 0 : _a.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openGlobalLock) {\n            this.openGlobalLock();\n            this.openGlobalLock = null;\n        }\n        (_b = this.visualElement.animationState) === null || _b === void 0 ? void 0 : _b.setActive(AnimationType.Drag, false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const { layout } = this.visualElement.projection || {};\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints === null || constraints === void 0 ? void 0 : constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        return axisValue.start(createMotionValueAnimation(axis, axisValue, 0, transition));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        var _a;\n        const dragKey = \"_drag\" + axis.toUpperCase();\n        const externalMotionValue = this.visualElement.getProps()[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, ((_a = this.visualElement.getProps().initial) === null || _a === void 0 ? void 0 : _a[axis]) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mix(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        var _a;\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        (_a = projection.root) === null || _a === void 0 ? void 0 : _a.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mix(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        var _a;\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints)) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            (_a = projection.root) === null || _a === void 0 ? void 0 : _a.updateScroll();\n            projection.updateLayout();\n        }\n        measureDragConstraints();\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener === null || stopLayoutUpdateListener === void 0 ? void 0 : stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,cAAc,QAAQ,yBAAyB;AACnL,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,0CAA0C;AAC3G,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,0BAA0B,QAAQ,2BAA2B;AAEtE,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAE;IACvB;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAGxB,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACe,aAAa,GAAGA,aAAa;EACtC;EACAU,KAAKA,CAACC,WAAW,EAAE;IAAEC,YAAY,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9C;AACR;AACA;IACQ,IAAI,IAAI,CAACZ,aAAa,CAACa,SAAS,KAAK,KAAK,EACtC;IACJ,MAAMC,cAAc,GAAIC,KAAK,IAAK;MAC9B;MACA;MACA,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAIJ,YAAY,EAAE;QACd,IAAI,CAACA,YAAY,CAACxB,gBAAgB,CAAC2B,KAAK,EAAE,MAAM,CAAC,CAACE,KAAK,CAAC;MAC5D;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGA,CAACH,KAAK,EAAEI,IAAI,KAAK;MAC7B,IAAIC,EAAE;MACN;MACA,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC9D,IAAIH,IAAI,IAAI,CAACC,eAAe,EAAE;QAC1B,IAAI,IAAI,CAACrB,cAAc,EACnB,IAAI,CAACA,cAAc,CAAC,CAAC;QACzB,IAAI,CAACA,cAAc,GAAG3B,aAAa,CAAC+C,IAAI,CAAC;QACzC;QACA,IAAI,CAAC,IAAI,CAACpB,cAAc,EACpB;MACR;MACA,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACsB,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAACzB,aAAa,CAAC0B,UAAU,EAAE;QAC/B,IAAI,CAAC1B,aAAa,CAAC0B,UAAU,CAACC,kBAAkB,GAAG,IAAI;QACvD,IAAI,CAAC3B,aAAa,CAAC0B,UAAU,CAACE,MAAM,GAAGC,SAAS;MACpD;MACA;AACZ;AACA;MACY3C,QAAQ,CAAE4C,IAAI,IAAK;QACf,IAAIV,EAAE,EAAEW,EAAE;QACV,IAAIC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACH,IAAI,CAAC,CAACI,GAAG,CAAC,CAAC,IAAI,CAAC;QACtD;AAChB;AACA;QACgB,IAAIxC,OAAO,CAACyC,IAAI,CAACH,OAAO,CAAC,EAAE;UACvB,MAAMI,YAAY,GAAG,CAACL,EAAE,GAAG,CAACX,EAAE,GAAG,IAAI,CAACpB,aAAa,CAAC0B,UAAU,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,SAAS,CAACR,IAAI,CAAC;UACvK,IAAIM,YAAY,EAAE;YACd,MAAMG,MAAM,GAAG/C,UAAU,CAAC4C,YAAY,CAAC;YACvCJ,OAAO,GAAGO,MAAM,IAAIC,UAAU,CAACR,OAAO,CAAC,GAAG,GAAG,CAAC;UAClD;QACJ;QACA,IAAI,CAAC5B,WAAW,CAAC0B,IAAI,CAAC,GAAGE,OAAO;MACpC,CAAC,CAAC;MACF;MACAT,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACR,KAAK,EAAEI,IAAI,CAAC;MAClF,CAACC,EAAE,GAAG,IAAI,CAACpB,aAAa,CAACyC,cAAc,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,SAAS,CAAC1D,aAAa,CAAC2D,IAAI,EAAE,IAAI,CAAC;IACxH,CAAC;IACD,MAAMC,MAAM,GAAGA,CAAC7B,KAAK,EAAEI,IAAI,KAAK;MAC5B;MACA,MAAM;QAAEG,eAAe;QAAEuB,iBAAiB;QAAEC,eAAe;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAACvB,QAAQ,CAAC,CAAC;MACxF;MACA,IAAI,CAACF,eAAe,IAAI,CAAC,IAAI,CAACrB,cAAc,EACxC;MACJ,MAAM;QAAE+C;MAAO,CAAC,GAAG7B,IAAI;MACvB;MACA,IAAI0B,iBAAiB,IAAI,IAAI,CAAC1C,gBAAgB,KAAK,IAAI,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAG8C,mBAAmB,CAACD,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAAC7C,gBAAgB,KAAK,IAAI,EAAE;UAChC2C,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC,IAAI,CAAC3C,gBAAgB,CAAC;QAC5G;QACA;MACJ;MACA;MACA,IAAI,CAAC+C,UAAU,CAAC,GAAG,EAAE/B,IAAI,CAACF,KAAK,EAAE+B,MAAM,CAAC;MACxC,IAAI,CAACE,UAAU,CAAC,GAAG,EAAE/B,IAAI,CAACF,KAAK,EAAE+B,MAAM,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAAChD,aAAa,CAACmD,MAAM,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;MACYJ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAChC,KAAK,EAAEI,IAAI,CAAC;IACvE,CAAC;IACD,MAAMiC,YAAY,GAAGA,CAACrC,KAAK,EAAEI,IAAI,KAAK,IAAI,CAACkC,IAAI,CAACtC,KAAK,EAAEI,IAAI,CAAC;IAC5D,IAAI,CAACmC,UAAU,GAAG,IAAIjF,UAAU,CAACsC,WAAW,EAAE;MAC1CG,cAAc;MACdI,OAAO;MACP0B,MAAM;MACNQ;IACJ,CAAC,EAAE;MAAEG,kBAAkB,EAAE,IAAI,CAACvD,aAAa,CAACwD,qBAAqB,CAAC;IAAE,CAAC,CAAC;EAC1E;EACAH,IAAIA,CAACtC,KAAK,EAAEI,IAAI,EAAE;IACd,MAAMjB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAACuD,MAAM,CAAC,CAAC;IACb,IAAI,CAACvD,UAAU,EACX;IACJ,MAAM;MAAEwD;IAAS,CAAC,GAAGvC,IAAI;IACzB,IAAI,CAACwC,cAAc,CAACD,QAAQ,CAAC;IAC7B,MAAM;MAAEE;IAAU,CAAC,GAAG,IAAI,CAACpC,QAAQ,CAAC,CAAC;IACrCoC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC7C,KAAK,EAAEI,IAAI,CAAC;EAChF;EACAsC,MAAMA,CAAA,EAAG;IACL,IAAIrC,EAAE,EAAEW,EAAE;IACV,IAAI,CAAC7B,UAAU,GAAG,KAAK;IACvB,IAAI,IAAI,CAACF,aAAa,CAAC0B,UAAU,EAAE;MAC/B,IAAI,CAAC1B,aAAa,CAAC0B,UAAU,CAACC,kBAAkB,GAAG,KAAK;IAC5D;IACA,CAACP,EAAE,GAAG,IAAI,CAACkC,UAAU,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyC,GAAG,CAAC,CAAC;IACpE,IAAI,CAACP,UAAU,GAAGzB,SAAS;IAC3B,MAAM;MAAEP;IAAgB,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACF,eAAe,IAAI,IAAI,CAACrB,cAAc,EAAE;MACzC,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA,CAAC8B,EAAE,GAAG,IAAI,CAAC/B,aAAa,CAACyC,cAAc,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,SAAS,CAAC1D,aAAa,CAAC2D,IAAI,EAAE,KAAK,CAAC;EACzH;EACAO,UAAUA,CAACpB,IAAI,EAAEgC,MAAM,EAAEd,MAAM,EAAE;IAC7B,MAAM;MAAE3B;IAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAAC,CAAC;IAChC;IACA,IAAI,CAACwB,MAAM,IAAI,CAACe,UAAU,CAACjC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAClB,gBAAgB,CAAC,EACzD;IACJ,MAAM6D,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACH,IAAI,CAAC;IAC/C,IAAImC,IAAI,GAAG,IAAI,CAAC7D,WAAW,CAAC0B,IAAI,CAAC,GAAGkB,MAAM,CAAClB,IAAI,CAAC;IAChD;IACA,IAAI,IAAI,CAACvB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,IAAI,CAAC,EAAE;MAC5CmC,IAAI,GAAGxF,gBAAgB,CAACwF,IAAI,EAAE,IAAI,CAAC1D,WAAW,CAACuB,IAAI,CAAC,EAAE,IAAI,CAACrB,OAAO,CAACqB,IAAI,CAAC,CAAC;IAC7E;IACAkC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC;EACvB;EACAxC,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAE0C,eAAe;MAAEC;IAAY,CAAC,GAAG,IAAI,CAAC5C,QAAQ,CAAC,CAAC;IACxD,MAAM;MAAEa;IAAO,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC0B,UAAU,IAAI,CAAC,CAAC;IACtD,MAAM2C,eAAe,GAAG,IAAI,CAAC9D,WAAW;IACxC,IAAI4D,eAAe,IAAI5F,WAAW,CAAC4F,eAAe,CAAC,EAAE;MACjD,IAAI,CAAC,IAAI,CAAC5D,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAAC+D,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC,MACI;MACD,IAAIH,eAAe,IAAI9B,MAAM,EAAE;QAC3B,IAAI,CAAC9B,WAAW,GAAG7B,uBAAuB,CAAC2D,MAAM,CAACC,SAAS,EAAE6B,eAAe,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAAC5D,WAAW,GAAG,KAAK;MAC5B;IACJ;IACA,IAAI,CAACE,OAAO,GAAG9B,kBAAkB,CAACyF,WAAW,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAIC,eAAe,KAAK,IAAI,CAAC9D,WAAW,IACpC8B,MAAM,IACN,IAAI,CAAC9B,WAAW,IAChB,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7BtB,QAAQ,CAAE4C,IAAI,IAAK;QACf,IAAI,IAAI,CAACG,kBAAkB,CAACH,IAAI,CAAC,EAAE;UAC/B,IAAI,CAACvB,WAAW,CAACuB,IAAI,CAAC,GAAGlD,qBAAqB,CAACyD,MAAM,CAACC,SAAS,CAACR,IAAI,CAAC,EAAE,IAAI,CAACvB,WAAW,CAACuB,IAAI,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;IACN;EACJ;EACAwC,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEH,eAAe,EAAE5D,WAAW;MAAEgE;IAAyB,CAAC,GAAG,IAAI,CAAC/C,QAAQ,CAAC,CAAC;IAClF,IAAI,CAACjB,WAAW,IAAI,CAAChC,WAAW,CAACgC,WAAW,CAAC,EACzC,OAAO,KAAK;IAChB,MAAMiE,kBAAkB,GAAGjE,WAAW,CAACyB,OAAO;IAC9C5D,SAAS,CAACoG,kBAAkB,KAAK,IAAI,EAAE,wGAAwG,CAAC;IAChJ,MAAM;MAAE9C;IAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;IACzC;IACA,IAAI,CAAC0B,UAAU,IAAI,CAACA,UAAU,CAACW,MAAM,EACjC,OAAO,KAAK;IAChB,MAAMoC,cAAc,GAAGtF,cAAc,CAACqF,kBAAkB,EAAE9C,UAAU,CAACgD,IAAI,EAAE,IAAI,CAAC1E,aAAa,CAACwD,qBAAqB,CAAC,CAAC,CAAC;IACtH,IAAImB,mBAAmB,GAAG9F,uBAAuB,CAAC6C,UAAU,CAACW,MAAM,CAACC,SAAS,EAAEmC,cAAc,CAAC;IAC9F;AACR;AACA;AACA;IACQ,IAAIF,wBAAwB,EAAE;MAC1B,MAAMK,eAAe,GAAGL,wBAAwB,CAAClF,uBAAuB,CAACsF,mBAAmB,CAAC,CAAC;MAC9F,IAAI,CAACnE,qBAAqB,GAAG,CAAC,CAACoE,eAAe;MAC9C,IAAIA,eAAe,EAAE;QACjBD,mBAAmB,GAAGrF,uBAAuB,CAACsF,eAAe,CAAC;MAClE;IACJ;IACA,OAAOD,mBAAmB;EAC9B;EACAhB,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAErC,IAAI;MAAEwD,YAAY;MAAET,WAAW;MAAEU,cAAc;MAAEC,gBAAgB;MAAEC;IAAqB,CAAC,GAAG,IAAI,CAACxD,QAAQ,CAAC,CAAC;IACnH,MAAMjB,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAM0E,kBAAkB,GAAG/F,QAAQ,CAAE4C,IAAI,IAAK;MAC1C,IAAI,CAACiC,UAAU,CAACjC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAClB,gBAAgB,CAAC,EAAE;QAChD;MACJ;MACA,IAAI+E,UAAU,GAAG,CAAC3E,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACuB,IAAI,CAAC,KAAK,CAAC,CAAC;MACpG,IAAIiD,gBAAgB,EAChBG,UAAU,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,eAAe,GAAGjB,WAAW,GAAG,GAAG,GAAG,OAAO;MACnD,MAAMkB,aAAa,GAAGlB,WAAW,GAAG,EAAE,GAAG,QAAQ;MACjD,MAAMmB,OAAO,GAAG;QACZC,IAAI,EAAE,SAAS;QACf9B,QAAQ,EAAEmB,YAAY,GAAGnB,QAAQ,CAAC5B,IAAI,CAAC,GAAG,CAAC;QAC3CuD,eAAe;QACfC,aAAa;QACbG,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,EAAE;QACb,GAAGb,cAAc;QACjB,GAAGI;MACP,CAAC;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACU,uBAAuB,CAAC9D,IAAI,EAAEyD,OAAO,CAAC;IACtD,CAAC,CAAC;IACF;IACA,OAAOM,OAAO,CAACC,GAAG,CAACb,kBAAkB,CAAC,CAACc,IAAI,CAACf,mBAAmB,CAAC;EACpE;EACAY,uBAAuBA,CAAC9D,IAAI,EAAEoD,UAAU,EAAE;IACtC,MAAMlB,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACH,IAAI,CAAC;IAC/C,OAAOkC,SAAS,CAACtD,KAAK,CAACf,0BAA0B,CAACmC,IAAI,EAAEkC,SAAS,EAAE,CAAC,EAAEkB,UAAU,CAAC,CAAC;EACtF;EACAlE,aAAaA,CAAA,EAAG;IACZ9B,QAAQ,CAAE4C,IAAI,IAAK,IAAI,CAACG,kBAAkB,CAACH,IAAI,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpB,kBAAkBA,CAACH,IAAI,EAAE;IACrB,IAAIV,EAAE;IACN,MAAM4E,OAAO,GAAG,OAAO,GAAGlE,IAAI,CAACmE,WAAW,CAAC,CAAC;IAC5C,MAAMC,mBAAmB,GAAG,IAAI,CAAClG,aAAa,CAACwB,QAAQ,CAAC,CAAC,CAACwE,OAAO,CAAC;IAClE,OAAOE,mBAAmB,GACpBA,mBAAmB,GACnB,IAAI,CAAClG,aAAa,CAACmG,QAAQ,CAACrE,IAAI,EAAE,CAAC,CAACV,EAAE,GAAG,IAAI,CAACpB,aAAa,CAACwB,QAAQ,CAAC,CAAC,CAAC4E,OAAO,MAAM,IAAI,IAAIhF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,IAAI,CAAC,KAAK,CAAC,CAAC;EAC9I;EACAlB,YAAYA,CAACK,KAAK,EAAE;IAChB/B,QAAQ,CAAE4C,IAAI,IAAK;MACf,MAAM;QAAET;MAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAAC,CAAC;MAChC;MACA,IAAI,CAACuC,UAAU,CAACjC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAClB,gBAAgB,CAAC,EAC9C;MACJ,MAAM;QAAEuB;MAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;MACzC,MAAMgE,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACH,IAAI,CAAC;MAC/C,IAAIJ,UAAU,IAAIA,UAAU,CAACW,MAAM,EAAE;QACjC,MAAM;UAAE8C,GAAG;UAAEC;QAAI,CAAC,GAAG1D,UAAU,CAACW,MAAM,CAACC,SAAS,CAACR,IAAI,CAAC;QACtDkC,SAAS,CAACE,GAAG,CAACjD,KAAK,CAACa,IAAI,CAAC,GAAGrC,GAAG,CAAC0F,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIiB,8BAA8BA,CAAA,EAAG;IAC7B,IAAIjF,EAAE;IACN,IAAI,CAAC,IAAI,CAACpB,aAAa,CAACgC,OAAO,EAC3B;IACJ,MAAM;MAAEX,IAAI;MAAE8C;IAAgB,CAAC,GAAG,IAAI,CAAC3C,QAAQ,CAAC,CAAC;IACjD,MAAM;MAAEE;IAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;IACzC,IAAI,CAACzB,WAAW,CAAC4F,eAAe,CAAC,IAAI,CAACzC,UAAU,IAAI,CAAC,IAAI,CAACnB,WAAW,EACjE;IACJ;AACR;AACA;AACA;IACQ,IAAI,CAACS,aAAa,CAAC,CAAC;IACpB;AACR;AACA;AACA;IACQ,MAAMsF,WAAW,GAAG;MAAEjG,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClCpB,QAAQ,CAAE4C,IAAI,IAAK;MACf,MAAMkC,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACH,IAAI,CAAC;MAC/C,IAAIkC,SAAS,EAAE;QACX,MAAMuC,MAAM,GAAGvC,SAAS,CAAC9B,GAAG,CAAC,CAAC;QAC9BoE,WAAW,CAACxE,IAAI,CAAC,GAAGhD,UAAU,CAAC;UAAEqG,GAAG,EAAEoB,MAAM;UAAEnB,GAAG,EAAEmB;QAAO,CAAC,EAAE,IAAI,CAAChG,WAAW,CAACuB,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAM;MAAE0E;IAAkB,CAAC,GAAG,IAAI,CAACxG,aAAa,CAACwB,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAACxB,aAAa,CAACgC,OAAO,CAACyE,KAAK,CAACC,SAAS,GAAGF,iBAAiB,GACxDA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;IACZ,CAACpF,EAAE,GAAGM,UAAU,CAACgD,IAAI,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,YAAY,CAAC,CAAC;IAC7EjF,UAAU,CAACkF,YAAY,CAAC,CAAC;IACzB,IAAI,CAACnF,kBAAkB,CAAC,CAAC;IACzB;AACR;AACA;AACA;IACQvC,QAAQ,CAAE4C,IAAI,IAAK;MACf,IAAI,CAACiC,UAAU,CAACjC,IAAI,EAAET,IAAI,EAAE,IAAI,CAAC,EAC7B;MACJ;AACZ;AACA;MACY,MAAM2C,SAAS,GAAG,IAAI,CAAC/B,kBAAkB,CAACH,IAAI,CAAC;MAC/C,MAAM;QAAEqD,GAAG;QAAEC;MAAI,CAAC,GAAG,IAAI,CAAC7E,WAAW,CAACuB,IAAI,CAAC;MAC3CkC,SAAS,CAACE,GAAG,CAACzE,GAAG,CAAC0F,GAAG,EAAEC,GAAG,EAAEkB,WAAW,CAACxE,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;EACN;EACA+E,YAAYA,CAAA,EAAG;IACX,IAAIzF,EAAE;IACN,IAAI,CAAC,IAAI,CAACpB,aAAa,CAACgC,OAAO,EAC3B;IACJpC,mBAAmB,CAACsE,GAAG,CAAC,IAAI,CAAClE,aAAa,EAAE,IAAI,CAAC;IACjD,MAAM8G,OAAO,GAAG,IAAI,CAAC9G,aAAa,CAACgC,OAAO;IAC1C;AACR;AACA;IACQ,MAAM+E,mBAAmB,GAAGvI,eAAe,CAACsI,OAAO,EAAE,aAAa,EAAG/F,KAAK,IAAK;MAC3E,MAAM;QAAEM,IAAI;QAAE2F,YAAY,GAAG;MAAK,CAAC,GAAG,IAAI,CAACxF,QAAQ,CAAC,CAAC;MACrDH,IAAI,IAAI2F,YAAY,IAAI,IAAI,CAACtG,KAAK,CAACK,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,MAAMkG,sBAAsB,GAAGA,CAAA,KAAM;MACjC,MAAM;QAAE9C;MAAgB,CAAC,GAAG,IAAI,CAAC3C,QAAQ,CAAC,CAAC;MAC3C,IAAIjD,WAAW,CAAC4F,eAAe,CAAC,EAAE;QAC9B,IAAI,CAAC5D,WAAW,GAAG,IAAI,CAAC+D,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC;IACD,MAAM;MAAE5C;IAAW,CAAC,GAAG,IAAI,CAAC1B,aAAa;IACzC,MAAMkH,yBAAyB,GAAGxF,UAAU,CAACyF,gBAAgB,CAAC,SAAS,EAAEF,sBAAsB,CAAC;IAChG,IAAIvF,UAAU,IAAI,CAACA,UAAU,CAACW,MAAM,EAAE;MAClC,CAACjB,EAAE,GAAGM,UAAU,CAACgD,IAAI,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,YAAY,CAAC,CAAC;MAC7EjF,UAAU,CAACkF,YAAY,CAAC,CAAC;IAC7B;IACAK,sBAAsB,CAAC,CAAC;IACxB;AACR;AACA;AACA;IACQ,MAAMG,kBAAkB,GAAG7H,WAAW,CAAC8H,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAChB,8BAA8B,CAAC,CAAC,CAAC;IACrG;AACR;AACA;AACA;IACQ,MAAMiB,wBAAwB,GAAG5F,UAAU,CAACyF,gBAAgB,CAAC,WAAW,EAAG,CAAC;MAAEI,KAAK;MAAEC;IAAiB,CAAC,KAAK;MACxG,IAAI,IAAI,CAACtH,UAAU,IAAIsH,gBAAgB,EAAE;QACrCtI,QAAQ,CAAE4C,IAAI,IAAK;UACf,MAAM2F,WAAW,GAAG,IAAI,CAACxF,kBAAkB,CAACH,IAAI,CAAC;UACjD,IAAI,CAAC2F,WAAW,EACZ;UACJ,IAAI,CAACrH,WAAW,CAAC0B,IAAI,CAAC,IAAIyF,KAAK,CAACzF,IAAI,CAAC,CAAC4F,SAAS;UAC/CD,WAAW,CAACvD,GAAG,CAACuD,WAAW,CAACvF,GAAG,CAAC,CAAC,GAAGqF,KAAK,CAACzF,IAAI,CAAC,CAAC4F,SAAS,CAAC;QAC9D,CAAC,CAAC;QACF,IAAI,CAAC1H,aAAa,CAACmD,MAAM,CAAC,CAAC;MAC/B;IACJ,CAAE,CAAC;IACH,OAAO,MAAM;MACTiE,kBAAkB,CAAC,CAAC;MACpBL,mBAAmB,CAAC,CAAC;MACrBG,yBAAyB,CAAC,CAAC;MAC3BI,wBAAwB,KAAK,IAAI,IAAIA,wBAAwB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,wBAAwB,CAAC,CAAC;IAClH,CAAC;EACL;EACA9F,QAAQA,CAAA,EAAG;IACP,MAAMmG,KAAK,GAAG,IAAI,CAAC3H,aAAa,CAACwB,QAAQ,CAAC,CAAC;IAC3C,MAAM;MAAEH,IAAI,GAAG,KAAK;MAAEwB,iBAAiB,GAAG,KAAK;MAAEvB,eAAe,GAAG,KAAK;MAAE6C,eAAe,GAAG,KAAK;MAAEC,WAAW,GAAGrF,cAAc;MAAE8F,YAAY,GAAG;IAAM,CAAC,GAAG8C,KAAK;IAC/J,OAAO;MACH,GAAGA,KAAK;MACRtG,IAAI;MACJwB,iBAAiB;MACjBvB,eAAe;MACf6C,eAAe;MACfC,WAAW;MACXS;IACJ,CAAC;EACL;AACJ;AACA,SAASd,UAAUA,CAAC6D,SAAS,EAAEvG,IAAI,EAAElB,gBAAgB,EAAE;EACnD,OAAQ,CAACkB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKuG,SAAS,MACvCzH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKyH,SAAS,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS3E,mBAAmBA,CAACD,MAAM,EAAE6E,aAAa,GAAG,EAAE,EAAE;EACrD,IAAID,SAAS,GAAG,IAAI;EACpB,IAAIE,IAAI,CAACC,GAAG,CAAC/E,MAAM,CAAC1C,CAAC,CAAC,GAAGuH,aAAa,EAAE;IACpCD,SAAS,GAAG,GAAG;EACnB,CAAC,MACI,IAAIE,IAAI,CAACC,GAAG,CAAC/E,MAAM,CAAC3C,CAAC,CAAC,GAAGwH,aAAa,EAAE;IACzCD,SAAS,GAAG,GAAG;EACnB;EACA,OAAOA,SAAS;AACpB;AAEA,SAAS9H,yBAAyB,EAAEF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}