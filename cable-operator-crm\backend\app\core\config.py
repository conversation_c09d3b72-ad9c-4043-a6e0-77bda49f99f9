"""
Configuration settings for the Cable Operator CRM application
"""

from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import validator
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings"""
    
    # Basic Configuration
    PROJECT_NAME: str = "Cable Operator CRM"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database
    DATABASE_URL: str = "sqlite:///./cable_operator.db"
    
    # CORS
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
    ]
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    # Email Configuration
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_FROM_EMAIL: Optional[str] = None
    SMTP_FROM_NAME: str = "Cable Operator CRM"
    SMTP_USE_TLS: bool = True
    
    # File Storage
    UPLOAD_DIR: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [
        "image/jpeg",
        "image/png",
        "image/gif",
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "text/csv",
    ]
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # Company Defaults
    DEFAULT_COMPANY_NAME: str = "Cable Operator CRM"
    DEFAULT_CURRENCY: str = "USD"
    DEFAULT_TAX_RATE: float = 0.18
    DEFAULT_TIMEZONE: str = "UTC"
    
    # Backup
    BACKUP_DIR: str = "./backups"
    AUTO_BACKUP_ENABLED: bool = True
    BACKUP_RETENTION_DAYS: int = 30
    
    # API Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 100
    
    # Pagination
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # Invoice Settings
    INVOICE_PREFIX: str = "INV"
    INVOICE_NUMBER_LENGTH: int = 6
    
    # Payment Settings
    PAYMENT_DUE_DAYS: int = 30
    OVERDUE_GRACE_DAYS: int = 7
    
    # Notification Settings
    SEND_EMAIL_NOTIFICATIONS: bool = True
    SEND_SMS_NOTIFICATIONS: bool = False
    
    # Sentry (Error Monitoring)
    SENTRY_DSN: Optional[str] = None
    
    # Feature Flags
    ENABLE_CUSTOMER_PORTAL: bool = False
    ENABLE_MULTI_COMPANY: bool = False
    ENABLE_ADVANCED_REPORTING: bool = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Create necessary directories
        Path(self.UPLOAD_DIR).mkdir(parents=True, exist_ok=True)
        Path(self.BACKUP_DIR).mkdir(parents=True, exist_ok=True)
        Path(os.path.dirname(self.LOG_FILE)).mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Database URL for different environments
def get_database_url() -> str:
    """Get database URL based on environment"""
    if settings.ENVIRONMENT == "test":
        return "sqlite:///./test_cable_operator.db"
    return settings.DATABASE_URL


# Email configuration validation
def validate_email_config() -> bool:
    """Validate email configuration"""
    required_fields = [
        settings.SMTP_HOST,
        settings.SMTP_USERNAME,
        settings.SMTP_PASSWORD,
        settings.SMTP_FROM_EMAIL,
    ]
    return all(field is not None for field in required_fields)


# Feature flags
class FeatureFlags:
    """Feature flags for enabling/disabling features"""
    
    CUSTOMER_PORTAL = settings.ENABLE_CUSTOMER_PORTAL
    MULTI_COMPANY = settings.ENABLE_MULTI_COMPANY
    ADVANCED_REPORTING = settings.ENABLE_ADVANCED_REPORTING
    EMAIL_NOTIFICATIONS = settings.SEND_EMAIL_NOTIFICATIONS
    SMS_NOTIFICATIONS = settings.SEND_SMS_NOTIFICATIONS


# Export settings
__all__ = ["settings", "get_database_url", "validate_email_config", "FeatureFlags"]
