{"ast": null, "code": "const mix = (min, max, progress) => -progress * min + progress * max + min;\nexport { mix };", "map": {"version": 3, "names": ["mix", "min", "max", "progress"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/mix.es.js"], "sourcesContent": ["const mix = (min, max, progress) => -progress * min + progress * max + min;\n\nexport { mix };\n"], "mappings": "AAAA,MAAMA,GAAG,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,KAAK,CAACA,QAAQ,GAAGF,GAAG,GAAGE,QAAQ,GAAGD,GAAG,GAAGD,GAAG;AAE1E,SAASD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}