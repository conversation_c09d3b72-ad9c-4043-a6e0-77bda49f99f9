"""
Database configuration and session management
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.core.config import settings, get_database_url

# Database URL
DATABASE_URL = get_database_url()

# Create async engine for PostgreSQL or sync engine for SQLite
if DATABASE_URL.startswith("postgresql"):
    # Async PostgreSQL
    async_database_url = DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    engine = create_async_engine(
        async_database_url,
        echo=settings.DEBUG,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    AsyncSessionLocal = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
else:
    # Sync SQLite for development
    engine = create_engine(
        DATABASE_URL,
        echo=settings.DEBUG,
        connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {},
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Metadata for migrations
metadata = MetaData()

# Base class for models
Base = declarative_base(metadata=metadata)


# Dependency to get database session
async def get_db():
    """Get database session"""
    if DATABASE_URL.startswith("postgresql"):
        async with AsyncSessionLocal() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    else:
        db = SessionLocal()
        try:
            yield db
            db.commit()
        except Exception:
            db.rollback()
            raise
        finally:
            db.close()


# Database utilities
class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    async def create_tables():
        """Create all database tables"""
        if DATABASE_URL.startswith("postgresql"):
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
        else:
            Base.metadata.create_all(bind=engine)
    
    @staticmethod
    async def drop_tables():
        """Drop all database tables"""
        if DATABASE_URL.startswith("postgresql"):
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
        else:
            Base.metadata.drop_all(bind=engine)
    
    @staticmethod
    async def check_connection():
        """Check database connection"""
        try:
            if DATABASE_URL.startswith("postgresql"):
                async with AsyncSessionLocal() as session:
                    await session.execute("SELECT 1")
            else:
                with SessionLocal() as session:
                    session.execute("SELECT 1")
            return True
        except Exception as e:
            print(f"Database connection failed: {e}")
            return False


# Export database components
__all__ = [
    "engine",
    "Base",
    "get_db",
    "DatabaseManager",
    "metadata",
]
