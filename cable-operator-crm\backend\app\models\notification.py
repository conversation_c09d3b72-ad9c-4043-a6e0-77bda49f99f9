"""
Notification model for managing system notifications
"""

import enum
from datetime import datetime
from sqlalchemy import Column, String, Boolean, ForeignKey, Integer, DateTime, Text, Enum, JSON
from sqlalchemy.orm import relationship

from .base import BaseModel


class NotificationType(enum.Enum):
    """Notification type options"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    IN_APP = "in_app"
    WEBHOOK = "webhook"


class Notification(BaseModel):
    """Notification model"""
    
    __tablename__ = "notifications"
    
    # Basic Information
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(Enum(NotificationType), nullable=False)
    
    # Recipients
    recipient_email = Column(String(255), nullable=True)
    recipient_phone = Column(String(20), nullable=True)
    recipient_user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    recipient_customer_id = Column(Integer, Foreign<PERSON>ey("customers.id"), nullable=True, index=True)
    
    # Status
    is_sent = Column(Boolean, nullable=False, default=False)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    is_read = Column(Boolean, nullable=False, default=False)
    read_at = Column(DateTime(timezone=True), nullable=True)
    
    # Delivery Information
    delivery_status = Column(String(50), nullable=True)  # delivered, failed, bounced, etc.
    delivery_error = Column(Text, nullable=True)
    delivery_attempts = Column(Integer, nullable=False, default=0)
    max_delivery_attempts = Column(Integer, nullable=False, default=3)
    
    # Scheduling
    scheduled_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Template and Data
    template_name = Column(String(100), nullable=True)
    template_data = Column(JSON, nullable=True)
    
    # References
    related_entity_type = Column(String(50), nullable=True)  # customer, payment, invoice, etc.
    related_entity_id = Column(Integer, nullable=True)
    
    # Company Association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Priority and Category
    priority = Column(String(20), nullable=False, default="normal")  # low, normal, high, urgent
    category = Column(String(50), nullable=True)  # billing, service, marketing, etc.
    
    # Additional Information
    metadata = Column(JSON, nullable=True)
    tags = Column(String(500), nullable=True)  # Comma-separated tags
    
    # Relationships
    company = relationship("Company")
    recipient_user = relationship("User", foreign_keys=[recipient_user_id])
    recipient_customer = relationship("Customer", foreign_keys=[recipient_customer_id])
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return ['title', 'message', 'recipient_email', 'recipient_phone', 'category']
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return [
            'notification_type', 'is_sent', 'is_read', 'delivery_status',
            'priority', 'category', 'company_id', 'recipient_user_id',
            'recipient_customer_id', 'related_entity_type'
        ]
    
    @property
    def is_delivered(self) -> bool:
        """Check if notification was delivered"""
        return self.delivery_status == "delivered"
    
    @property
    def is_failed(self) -> bool:
        """Check if notification delivery failed"""
        return self.delivery_status in ["failed", "bounced", "rejected"]
    
    @property
    def can_retry(self) -> bool:
        """Check if notification can be retried"""
        return (
            not self.is_sent and 
            self.delivery_attempts < self.max_delivery_attempts and
            not self.is_expired
        )
    
    @property
    def is_expired(self) -> bool:
        """Check if notification has expired"""
        return self.expires_at and self.expires_at < datetime.utcnow()
    
    @property
    def is_scheduled(self) -> bool:
        """Check if notification is scheduled for future delivery"""
        return self.scheduled_at and self.scheduled_at > datetime.utcnow()
    
    def mark_as_sent(self, delivery_status: str = "sent"):
        """Mark notification as sent"""
        self.is_sent = True
        self.sent_at = datetime.utcnow()
        self.delivery_status = delivery_status
    
    def mark_as_delivered(self):
        """Mark notification as delivered"""
        self.delivery_status = "delivered"
        if not self.is_sent:
            self.mark_as_sent("delivered")
    
    def mark_as_failed(self, error_message: str = None):
        """Mark notification as failed"""
        self.delivery_status = "failed"
        self.delivery_error = error_message
        self.delivery_attempts += 1
    
    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.read_at = datetime.utcnow()
    
    def retry_delivery(self):
        """Retry delivery of failed notification"""
        if self.can_retry:
            self.delivery_attempts += 1
            self.delivery_status = "pending"
            self.delivery_error = None
    
    def get_recipient_display(self) -> str:
        """Get display name for recipient"""
        if self.recipient_user:
            return self.recipient_user.full_name
        elif self.recipient_customer:
            return self.recipient_customer.full_name
        elif self.recipient_email:
            return self.recipient_email
        elif self.recipient_phone:
            return self.recipient_phone
        else:
            return "Unknown Recipient"
    
    def get_tags_list(self) -> list:
        """Get tags as a list"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]
    
    def add_tag(self, tag: str):
        """Add a tag to notification"""
        current_tags = self.get_tags_list()
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = ", ".join(current_tags)
    
    def remove_tag(self, tag: str):
        """Remove a tag from notification"""
        current_tags = self.get_tags_list()
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = ", ".join(current_tags) if current_tags else None
    
    def set_metadata(self, key: str, value):
        """Set metadata value"""
        if not self.metadata:
            self.metadata = {}
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default=None):
        """Get metadata value"""
        return self.metadata.get(key, default) if self.metadata else default
    
    def set_template_data(self, key: str, value):
        """Set template data value"""
        if not self.template_data:
            self.template_data = {}
        self.template_data[key] = value
    
    def get_template_data(self, key: str, default=None):
        """Get template data value"""
        return self.template_data.get(key, default) if self.template_data else default
    
    @classmethod
    def create_email_notification(
        cls,
        title: str,
        message: str,
        recipient_email: str,
        company_id: int,
        template_name: str = None,
        template_data: dict = None,
        category: str = None,
        priority: str = "normal",
        scheduled_at: datetime = None
    ):
        """Create an email notification"""
        return cls(
            title=title,
            message=message,
            notification_type=NotificationType.EMAIL,
            recipient_email=recipient_email,
            company_id=company_id,
            template_name=template_name,
            template_data=template_data or {},
            category=category,
            priority=priority,
            scheduled_at=scheduled_at
        )
    
    @classmethod
    def create_sms_notification(
        cls,
        title: str,
        message: str,
        recipient_phone: str,
        company_id: int,
        category: str = None,
        priority: str = "normal",
        scheduled_at: datetime = None
    ):
        """Create an SMS notification"""
        return cls(
            title=title,
            message=message,
            notification_type=NotificationType.SMS,
            recipient_phone=recipient_phone,
            company_id=company_id,
            category=category,
            priority=priority,
            scheduled_at=scheduled_at
        )
    
    @classmethod
    def create_user_notification(
        cls,
        title: str,
        message: str,
        recipient_user_id: int,
        company_id: int,
        notification_type: NotificationType = NotificationType.IN_APP,
        category: str = None,
        priority: str = "normal",
        related_entity_type: str = None,
        related_entity_id: int = None
    ):
        """Create a user notification"""
        return cls(
            title=title,
            message=message,
            notification_type=notification_type,
            recipient_user_id=recipient_user_id,
            company_id=company_id,
            category=category,
            priority=priority,
            related_entity_type=related_entity_type,
            related_entity_id=related_entity_id
        )
    
    def __repr__(self):
        return f"<Notification(id={self.id}, type='{self.notification_type.value}', title='{self.title}')>"
