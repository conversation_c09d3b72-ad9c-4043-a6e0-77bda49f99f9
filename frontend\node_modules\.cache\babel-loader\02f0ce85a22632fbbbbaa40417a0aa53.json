{"ast": null, "code": "var isarray = require('isarray');\n\n/**\n * Expose `pathToRegexp`.\n */\nmodule.exports = pathToRegexp;\nmodule.exports.parse = parse;\nmodule.exports.compile = compile;\nmodule.exports.tokensToFunction = tokensToFunction;\nmodule.exports.tokensToRegExp = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n// Match escaped characters that would otherwise appear in future matches.\n// This allows the user to escape special characters that won't transform.\n'(\\\\\\\\.)',\n// Match Express-style parameters and un-named parameters with a prefix\n// and optional suffixes. Matches appear as:\n//\n// \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n// \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n// \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n'([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse(str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue;\n    }\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7];\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = prefix || defaultDelimiter;\n    var pattern = capture || group;\n    var prevText = prefix || (typeof tokens[tokens.length - 1] === 'string' ? tokens[tokens.length - 1] : '');\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : asterisk ? '.*' : restrictBacktrack(delimiter, prevText)\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n  return tokens;\n}\nfunction restrictBacktrack(delimiter, prevText) {\n  if (!prevText || prevText.indexOf(delimiter) > -1) {\n    return '[^' + escapeString(delimiter) + ']+?';\n  }\n  return escapeString(prevText) + '|(?:(?!' + escapeString(prevText) + ')[^' + escapeString(delimiter) + '])+?';\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile(str, options) {\n  return tokensToFunction(parse(str, options), options);\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty(str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk(str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction(tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n      if (typeof token === 'string') {\n        path += token;\n        continue;\n      }\n      var value = data[token.name];\n      var segment;\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n          continue;\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined');\n        }\n      }\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`');\n        }\n        if (value.length === 0) {\n          if (token.optional) {\n            continue;\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty');\n          }\n        }\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`');\n          }\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n        continue;\n      }\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"');\n      }\n      path += token.prefix + segment;\n    }\n    return path;\n  };\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString(str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1');\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup(group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1');\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys(re, keys) {\n  re.keys = keys;\n  return re;\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags(options) {\n  return options && options.sensitive ? '' : 'i';\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp(path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n  return attachKeys(path, keys);\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp(path, keys, options) {\n  var parts = [];\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n  return attachKeys(regexp, keys);\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp(path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options);\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp(tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */keys || options;\n    keys = [];\n  }\n  options = options || {};\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n      keys.push(token);\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n      route += capture;\n    }\n  }\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter;\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n  return attachKeys(new RegExp('^' + route, flags(options)), keys);\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp(path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */keys || options;\n    keys = [];\n  }\n  options = options || {};\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */keys);\n  }\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */path, /** @type {!Array} */keys, options);\n  }\n  return stringToRegexp(/** @type {string} */path, /** @type {!Array} */keys, options);\n}", "map": {"version": 3, "names": ["isarray", "require", "module", "exports", "pathToRegexp", "parse", "compile", "tokensToFunction", "tokensToRegExp", "PATH_REGEXP", "RegExp", "join", "str", "options", "tokens", "key", "index", "path", "defaultDelimiter", "delimiter", "res", "exec", "m", "escaped", "offset", "slice", "length", "next", "prefix", "name", "capture", "group", "modifier", "asterisk", "push", "partial", "repeat", "optional", "pattern", "prevText", "escapeGroup", "restrictBacktrack", "substr", "indexOf", "escapeString", "encodeURIComponentPretty", "encodeURI", "replace", "c", "charCodeAt", "toString", "toUpperCase", "encodeAsterisk", "matches", "Array", "i", "flags", "obj", "opts", "data", "encode", "pretty", "encodeURIComponent", "token", "value", "segment", "TypeError", "JSON", "stringify", "j", "test", "attachKeys", "re", "keys", "sensitive", "regexpToRegexp", "groups", "source", "match", "arrayToRegexp", "parts", "regexp", "stringToRegexp", "strict", "end", "route", "endsWithDelimiter"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/path-to-regexp/index.js"], "sourcesContent": ["var isarray = require('isarray')\n\n/**\n * Expose `pathToRegexp`.\n */\nmodule.exports = pathToRegexp\nmodule.exports.parse = parse\nmodule.exports.compile = compile\nmodule.exports.tokensToFunction = tokensToFunction\nmodule.exports.tokensToRegExp = tokensToRegExp\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n  // Match escaped characters that would otherwise appear in future matches.\n  // This allows the user to escape special characters that won't transform.\n  '(\\\\\\\\.)',\n  // Match Express-style parameters and un-named parameters with a prefix\n  // and optional suffixes. Matches appear as:\n  //\n  // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n  // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n  // \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n  '([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'\n].join('|'), 'g')\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse (str, options) {\n  var tokens = []\n  var key = 0\n  var index = 0\n  var path = ''\n  var defaultDelimiter = options && options.delimiter || '/'\n  var res\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0]\n    var escaped = res[1]\n    var offset = res.index\n    path += str.slice(index, offset)\n    index = offset + m.length\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1]\n      continue\n    }\n\n    var next = str[index]\n    var prefix = res[2]\n    var name = res[3]\n    var capture = res[4]\n    var group = res[5]\n    var modifier = res[6]\n    var asterisk = res[7]\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path)\n      path = ''\n    }\n\n    var partial = prefix != null && next != null && next !== prefix\n    var repeat = modifier === '+' || modifier === '*'\n    var optional = modifier === '?' || modifier === '*'\n    var delimiter = prefix || defaultDelimiter\n    var pattern = capture || group\n    var prevText = prefix || (typeof tokens[tokens.length - 1] === 'string' ? tokens[tokens.length - 1] : '')\n\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : (asterisk ? '.*' : restrictBacktrack(delimiter, prevText))\n    })\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index)\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path)\n  }\n\n  return tokens\n}\n\nfunction restrictBacktrack(delimiter, prevText) {\n  if (!prevText || prevText.indexOf(delimiter) > -1) {\n    return '[^' + escapeString(delimiter) + ']+?'\n  }\n\n  return escapeString(prevText) + '|(?:(?!' + escapeString(prevText) + ')[^' + escapeString(delimiter) + '])+?'\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile (str, options) {\n  return tokensToFunction(parse(str, options), options)\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty (str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk (str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction (tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length)\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options))\n    }\n  }\n\n  return function (obj, opts) {\n    var path = ''\n    var data = obj || {}\n    var options = opts || {}\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i]\n\n      if (typeof token === 'string') {\n        path += token\n\n        continue\n      }\n\n      var value = data[token.name]\n      var segment\n\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix\n          }\n\n          continue\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined')\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`')\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty')\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j])\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`')\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment\n        }\n\n        continue\n      }\n\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value)\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n      }\n\n      path += token.prefix + segment\n    }\n\n    return path\n  }\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString (str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1')\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup (group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1')\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys (re, keys) {\n  re.keys = keys\n  return re\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags (options) {\n  return options && options.sensitive ? '' : 'i'\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp (path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g)\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      })\n    }\n  }\n\n  return attachKeys(path, keys)\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp (path, keys, options) {\n  var parts = []\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source)\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options))\n\n  return attachKeys(regexp, keys)\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp (path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options)\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp (tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options)\n    keys = []\n  }\n\n  options = options || {}\n\n  var strict = options.strict\n  var end = options.end !== false\n  var route = ''\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i]\n\n    if (typeof token === 'string') {\n      route += escapeString(token)\n    } else {\n      var prefix = escapeString(token.prefix)\n      var capture = '(?:' + token.pattern + ')'\n\n      keys.push(token)\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*'\n      }\n\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?'\n        } else {\n          capture = prefix + '(' + capture + ')?'\n        }\n      } else {\n        capture = prefix + '(' + capture + ')'\n      }\n\n      route += capture\n    }\n  }\n\n  var delimiter = escapeString(options.delimiter || '/')\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?'\n  }\n\n  if (end) {\n    route += '$'\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)'\n  }\n\n  return attachKeys(new RegExp('^' + route, flags(options)), keys)\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp (path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options)\n    keys = []\n  }\n\n  options = options || {}\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */ (keys))\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */ (path), /** @type {!Array} */ (keys), options)\n  }\n\n  return stringToRegexp(/** @type {string} */ (path), /** @type {!Array} */ (keys), options)\n}\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAEhC;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BF,MAAM,CAACC,OAAO,CAACE,KAAK,GAAGA,KAAK;AAC5BH,MAAM,CAACC,OAAO,CAACG,OAAO,GAAGA,OAAO;AAChCJ,MAAM,CAACC,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB;AAClDL,MAAM,CAACC,OAAO,CAACK,cAAc,GAAGA,cAAc;;AAE9C;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,IAAIC,MAAM,CAAC;AAC3B;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,wGAAwG,CACzG,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,KAAKA,CAAEO,GAAG,EAAEC,OAAO,EAAE;EAC5B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,gBAAgB,GAAGL,OAAO,IAAIA,OAAO,CAACM,SAAS,IAAI,GAAG;EAC1D,IAAIC,GAAG;EAEP,OAAO,CAACA,GAAG,GAAGX,WAAW,CAACY,IAAI,CAACT,GAAG,CAAC,KAAK,IAAI,EAAE;IAC5C,IAAIU,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC;IACd,IAAIG,OAAO,GAAGH,GAAG,CAAC,CAAC,CAAC;IACpB,IAAII,MAAM,GAAGJ,GAAG,CAACJ,KAAK;IACtBC,IAAI,IAAIL,GAAG,CAACa,KAAK,CAACT,KAAK,EAAEQ,MAAM,CAAC;IAChCR,KAAK,GAAGQ,MAAM,GAAGF,CAAC,CAACI,MAAM;;IAEzB;IACA,IAAIH,OAAO,EAAE;MACXN,IAAI,IAAIM,OAAO,CAAC,CAAC,CAAC;MAClB;IACF;IAEA,IAAII,IAAI,GAAGf,GAAG,CAACI,KAAK,CAAC;IACrB,IAAIY,MAAM,GAAGR,GAAG,CAAC,CAAC,CAAC;IACnB,IAAIS,IAAI,GAAGT,GAAG,CAAC,CAAC,CAAC;IACjB,IAAIU,OAAO,GAAGV,GAAG,CAAC,CAAC,CAAC;IACpB,IAAIW,KAAK,GAAGX,GAAG,CAAC,CAAC,CAAC;IAClB,IAAIY,QAAQ,GAAGZ,GAAG,CAAC,CAAC,CAAC;IACrB,IAAIa,QAAQ,GAAGb,GAAG,CAAC,CAAC,CAAC;;IAErB;IACA,IAAIH,IAAI,EAAE;MACRH,MAAM,CAACoB,IAAI,CAACjB,IAAI,CAAC;MACjBA,IAAI,GAAG,EAAE;IACX;IAEA,IAAIkB,OAAO,GAAGP,MAAM,IAAI,IAAI,IAAID,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAKC,MAAM;IAC/D,IAAIQ,MAAM,GAAGJ,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG;IACjD,IAAIK,QAAQ,GAAGL,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG;IACnD,IAAIb,SAAS,GAAGS,MAAM,IAAIV,gBAAgB;IAC1C,IAAIoB,OAAO,GAAGR,OAAO,IAAIC,KAAK;IAC9B,IAAIQ,QAAQ,GAAGX,MAAM,KAAK,OAAOd,MAAM,CAACA,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,GAAGZ,MAAM,CAACA,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAEzGZ,MAAM,CAACoB,IAAI,CAAC;MACVL,IAAI,EAAEA,IAAI,IAAId,GAAG,EAAE;MACnBa,MAAM,EAAEA,MAAM,IAAI,EAAE;MACpBT,SAAS,EAAEA,SAAS;MACpBkB,QAAQ,EAAEA,QAAQ;MAClBD,MAAM,EAAEA,MAAM;MACdD,OAAO,EAAEA,OAAO;MAChBF,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBK,OAAO,EAAEA,OAAO,GAAGE,WAAW,CAACF,OAAO,CAAC,GAAIL,QAAQ,GAAG,IAAI,GAAGQ,iBAAiB,CAACtB,SAAS,EAAEoB,QAAQ;IACpG,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIvB,KAAK,GAAGJ,GAAG,CAACc,MAAM,EAAE;IACtBT,IAAI,IAAIL,GAAG,CAAC8B,MAAM,CAAC1B,KAAK,CAAC;EAC3B;;EAEA;EACA,IAAIC,IAAI,EAAE;IACRH,MAAM,CAACoB,IAAI,CAACjB,IAAI,CAAC;EACnB;EAEA,OAAOH,MAAM;AACf;AAEA,SAAS2B,iBAAiBA,CAACtB,SAAS,EAAEoB,QAAQ,EAAE;EAC9C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACI,OAAO,CAACxB,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;IACjD,OAAO,IAAI,GAAGyB,YAAY,CAACzB,SAAS,CAAC,GAAG,KAAK;EAC/C;EAEA,OAAOyB,YAAY,CAACL,QAAQ,CAAC,GAAG,SAAS,GAAGK,YAAY,CAACL,QAAQ,CAAC,GAAG,KAAK,GAAGK,YAAY,CAACzB,SAAS,CAAC,GAAG,MAAM;AAC/G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASb,OAAOA,CAAEM,GAAG,EAAEC,OAAO,EAAE;EAC9B,OAAON,gBAAgB,CAACF,KAAK,CAACO,GAAG,EAAEC,OAAO,CAAC,EAAEA,OAAO,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgC,wBAAwBA,CAAEjC,GAAG,EAAE;EACtC,OAAOkC,SAAS,CAAClC,GAAG,CAAC,CAACmC,OAAO,CAAC,SAAS,EAAE,UAAUC,CAAC,EAAE;IACpD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAExC,GAAG,EAAE;EAC5B,OAAOkC,SAAS,CAAClC,GAAG,CAAC,CAACmC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;IAClD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAAS5C,gBAAgBA,CAAEO,MAAM,EAAED,OAAO,EAAE;EAC1C;EACA,IAAIwC,OAAO,GAAG,IAAIC,KAAK,CAACxC,MAAM,CAACY,MAAM,CAAC;;EAEtC;EACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,CAACY,MAAM,EAAE6B,CAAC,EAAE,EAAE;IACtC,IAAI,OAAOzC,MAAM,CAACyC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjCF,OAAO,CAACE,CAAC,CAAC,GAAG,IAAI7C,MAAM,CAAC,MAAM,GAAGI,MAAM,CAACyC,CAAC,CAAC,CAACjB,OAAO,GAAG,IAAI,EAAEkB,KAAK,CAAC3C,OAAO,CAAC,CAAC;IAC5E;EACF;EAEA,OAAO,UAAU4C,GAAG,EAAEC,IAAI,EAAE;IAC1B,IAAIzC,IAAI,GAAG,EAAE;IACb,IAAI0C,IAAI,GAAGF,GAAG,IAAI,CAAC,CAAC;IACpB,IAAI5C,OAAO,GAAG6C,IAAI,IAAI,CAAC,CAAC;IACxB,IAAIE,MAAM,GAAG/C,OAAO,CAACgD,MAAM,GAAGhB,wBAAwB,GAAGiB,kBAAkB;IAE3E,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,CAACY,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACtC,IAAIQ,KAAK,GAAGjD,MAAM,CAACyC,CAAC,CAAC;MAErB,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAE;QAC7B9C,IAAI,IAAI8C,KAAK;QAEb;MACF;MAEA,IAAIC,KAAK,GAAGL,IAAI,CAACI,KAAK,CAAClC,IAAI,CAAC;MAC5B,IAAIoC,OAAO;MAEX,IAAID,KAAK,IAAI,IAAI,EAAE;QACjB,IAAID,KAAK,CAAC1B,QAAQ,EAAE;UAClB;UACA,IAAI0B,KAAK,CAAC5B,OAAO,EAAE;YACjBlB,IAAI,IAAI8C,KAAK,CAACnC,MAAM;UACtB;UAEA;QACF,CAAC,MAAM;UACL,MAAM,IAAIsC,SAAS,CAAC,YAAY,GAAGH,KAAK,CAAClC,IAAI,GAAG,iBAAiB,CAAC;QACpE;MACF;MAEA,IAAI7B,OAAO,CAACgE,KAAK,CAAC,EAAE;QAClB,IAAI,CAACD,KAAK,CAAC3B,MAAM,EAAE;UACjB,MAAM,IAAI8B,SAAS,CAAC,YAAY,GAAGH,KAAK,CAAClC,IAAI,GAAG,iCAAiC,GAAGsC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,GAAG,GAAG,CAAC;QAClH;QAEA,IAAIA,KAAK,CAACtC,MAAM,KAAK,CAAC,EAAE;UACtB,IAAIqC,KAAK,CAAC1B,QAAQ,EAAE;YAClB;UACF,CAAC,MAAM;YACL,MAAM,IAAI6B,SAAS,CAAC,YAAY,GAAGH,KAAK,CAAClC,IAAI,GAAG,mBAAmB,CAAC;UACtE;QACF;QAEA,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACtC,MAAM,EAAE2C,CAAC,EAAE,EAAE;UACrCJ,OAAO,GAAGL,MAAM,CAACI,KAAK,CAACK,CAAC,CAAC,CAAC;UAE1B,IAAI,CAAChB,OAAO,CAACE,CAAC,CAAC,CAACe,IAAI,CAACL,OAAO,CAAC,EAAE;YAC7B,MAAM,IAAIC,SAAS,CAAC,gBAAgB,GAAGH,KAAK,CAAClC,IAAI,GAAG,cAAc,GAAGkC,KAAK,CAACzB,OAAO,GAAG,mBAAmB,GAAG6B,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,GAAG,GAAG,CAAC;UAC3I;UAEAhD,IAAI,IAAI,CAACoD,CAAC,KAAK,CAAC,GAAGN,KAAK,CAACnC,MAAM,GAAGmC,KAAK,CAAC5C,SAAS,IAAI8C,OAAO;QAC9D;QAEA;MACF;MAEAA,OAAO,GAAGF,KAAK,CAAC9B,QAAQ,GAAGmB,cAAc,CAACY,KAAK,CAAC,GAAGJ,MAAM,CAACI,KAAK,CAAC;MAEhE,IAAI,CAACX,OAAO,CAACE,CAAC,CAAC,CAACe,IAAI,CAACL,OAAO,CAAC,EAAE;QAC7B,MAAM,IAAIC,SAAS,CAAC,YAAY,GAAGH,KAAK,CAAClC,IAAI,GAAG,cAAc,GAAGkC,KAAK,CAACzB,OAAO,GAAG,mBAAmB,GAAG2B,OAAO,GAAG,GAAG,CAAC;MACvH;MAEAhD,IAAI,IAAI8C,KAAK,CAACnC,MAAM,GAAGqC,OAAO;IAChC;IAEA,OAAOhD,IAAI;EACb,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,YAAYA,CAAEhC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACmC,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,WAAWA,CAAET,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACgB,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,UAAUA,CAAEC,EAAE,EAAEC,IAAI,EAAE;EAC7BD,EAAE,CAACC,IAAI,GAAGA,IAAI;EACd,OAAOD,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShB,KAAKA,CAAE3C,OAAO,EAAE;EACvB,OAAOA,OAAO,IAAIA,OAAO,CAAC6D,SAAS,GAAG,EAAE,GAAG,GAAG;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAE1D,IAAI,EAAEwD,IAAI,EAAE;EACnC;EACA,IAAIG,MAAM,GAAG3D,IAAI,CAAC4D,MAAM,CAACC,KAAK,CAAC,WAAW,CAAC;EAE3C,IAAIF,MAAM,EAAE;IACV,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,MAAM,CAAClD,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACtCkB,IAAI,CAACvC,IAAI,CAAC;QACRL,IAAI,EAAE0B,CAAC;QACP3B,MAAM,EAAE,IAAI;QACZT,SAAS,EAAE,IAAI;QACfkB,QAAQ,EAAE,KAAK;QACfD,MAAM,EAAE,KAAK;QACbD,OAAO,EAAE,KAAK;QACdF,QAAQ,EAAE,KAAK;QACfK,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;EAEA,OAAOiC,UAAU,CAACtD,IAAI,EAAEwD,IAAI,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,aAAaA,CAAE9D,IAAI,EAAEwD,IAAI,EAAE5D,OAAO,EAAE;EAC3C,IAAImE,KAAK,GAAG,EAAE;EAEd,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,IAAI,CAACS,MAAM,EAAE6B,CAAC,EAAE,EAAE;IACpCyB,KAAK,CAAC9C,IAAI,CAAC9B,YAAY,CAACa,IAAI,CAACsC,CAAC,CAAC,EAAEkB,IAAI,EAAE5D,OAAO,CAAC,CAACgE,MAAM,CAAC;EACzD;EAEA,IAAII,MAAM,GAAG,IAAIvE,MAAM,CAAC,KAAK,GAAGsE,KAAK,CAACrE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE6C,KAAK,CAAC3C,OAAO,CAAC,CAAC;EAEtE,OAAO0D,UAAU,CAACU,MAAM,EAAER,IAAI,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,cAAcA,CAAEjE,IAAI,EAAEwD,IAAI,EAAE5D,OAAO,EAAE;EAC5C,OAAOL,cAAc,CAACH,KAAK,CAACY,IAAI,EAAEJ,OAAO,CAAC,EAAE4D,IAAI,EAAE5D,OAAO,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,cAAcA,CAAEM,MAAM,EAAE2D,IAAI,EAAE5D,OAAO,EAAE;EAC9C,IAAI,CAACb,OAAO,CAACyE,IAAI,CAAC,EAAE;IAClB5D,OAAO,GAAG,sBAAwB4D,IAAI,IAAI5D,OAAQ;IAClD4D,IAAI,GAAG,EAAE;EACX;EAEA5D,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIsE,MAAM,GAAGtE,OAAO,CAACsE,MAAM;EAC3B,IAAIC,GAAG,GAAGvE,OAAO,CAACuE,GAAG,KAAK,KAAK;EAC/B,IAAIC,KAAK,GAAG,EAAE;;EAEd;EACA,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,CAACY,MAAM,EAAE6B,CAAC,EAAE,EAAE;IACtC,IAAIQ,KAAK,GAAGjD,MAAM,CAACyC,CAAC,CAAC;IAErB,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAE;MAC7BsB,KAAK,IAAIzC,YAAY,CAACmB,KAAK,CAAC;IAC9B,CAAC,MAAM;MACL,IAAInC,MAAM,GAAGgB,YAAY,CAACmB,KAAK,CAACnC,MAAM,CAAC;MACvC,IAAIE,OAAO,GAAG,KAAK,GAAGiC,KAAK,CAACzB,OAAO,GAAG,GAAG;MAEzCmC,IAAI,CAACvC,IAAI,CAAC6B,KAAK,CAAC;MAEhB,IAAIA,KAAK,CAAC3B,MAAM,EAAE;QAChBN,OAAO,IAAI,KAAK,GAAGF,MAAM,GAAGE,OAAO,GAAG,IAAI;MAC5C;MAEA,IAAIiC,KAAK,CAAC1B,QAAQ,EAAE;QAClB,IAAI,CAAC0B,KAAK,CAAC5B,OAAO,EAAE;UAClBL,OAAO,GAAG,KAAK,GAAGF,MAAM,GAAG,GAAG,GAAGE,OAAO,GAAG,KAAK;QAClD,CAAC,MAAM;UACLA,OAAO,GAAGF,MAAM,GAAG,GAAG,GAAGE,OAAO,GAAG,IAAI;QACzC;MACF,CAAC,MAAM;QACLA,OAAO,GAAGF,MAAM,GAAG,GAAG,GAAGE,OAAO,GAAG,GAAG;MACxC;MAEAuD,KAAK,IAAIvD,OAAO;IAClB;EACF;EAEA,IAAIX,SAAS,GAAGyB,YAAY,CAAC/B,OAAO,CAACM,SAAS,IAAI,GAAG,CAAC;EACtD,IAAImE,iBAAiB,GAAGD,KAAK,CAAC5D,KAAK,CAAC,CAACN,SAAS,CAACO,MAAM,CAAC,KAAKP,SAAS;;EAEpE;EACA;EACA;EACA;EACA,IAAI,CAACgE,MAAM,EAAE;IACXE,KAAK,GAAG,CAACC,iBAAiB,GAAGD,KAAK,CAAC5D,KAAK,CAAC,CAAC,EAAE,CAACN,SAAS,CAACO,MAAM,CAAC,GAAG2D,KAAK,IAAI,KAAK,GAAGlE,SAAS,GAAG,SAAS;EACzG;EAEA,IAAIiE,GAAG,EAAE;IACPC,KAAK,IAAI,GAAG;EACd,CAAC,MAAM;IACL;IACA;IACAA,KAAK,IAAIF,MAAM,IAAIG,iBAAiB,GAAG,EAAE,GAAG,KAAK,GAAGnE,SAAS,GAAG,KAAK;EACvE;EAEA,OAAOoD,UAAU,CAAC,IAAI7D,MAAM,CAAC,GAAG,GAAG2E,KAAK,EAAE7B,KAAK,CAAC3C,OAAO,CAAC,CAAC,EAAE4D,IAAI,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrE,YAAYA,CAAEa,IAAI,EAAEwD,IAAI,EAAE5D,OAAO,EAAE;EAC1C,IAAI,CAACb,OAAO,CAACyE,IAAI,CAAC,EAAE;IAClB5D,OAAO,GAAG,sBAAwB4D,IAAI,IAAI5D,OAAQ;IAClD4D,IAAI,GAAG,EAAE;EACX;EAEA5D,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAII,IAAI,YAAYP,MAAM,EAAE;IAC1B,OAAOiE,cAAc,CAAC1D,IAAI,EAAE,qBAAuBwD,IAAK,CAAC;EAC3D;EAEA,IAAIzE,OAAO,CAACiB,IAAI,CAAC,EAAE;IACjB,OAAO8D,aAAa,CAAC,qBAAuB9D,IAAI,EAAG,qBAAuBwD,IAAI,EAAG5D,OAAO,CAAC;EAC3F;EAEA,OAAOqE,cAAc,CAAC,qBAAuBjE,IAAI,EAAG,qBAAuBwD,IAAI,EAAG5D,OAAO,CAAC;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "script"}