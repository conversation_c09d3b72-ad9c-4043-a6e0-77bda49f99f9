{"ast": null, "code": "'use strict';\n\nvar events = require('../utils/events.cjs.js');\nconst press = {\n  isActive: options => <PERSON><PERSON><PERSON>(options.press),\n  subscribe: (element, {\n    enable,\n    disable\n  }) => {\n    const onPointerUp = event => {\n      disable();\n      events.dispatchPointerEvent(element, \"pressend\", event);\n      window.removeEventListener(\"pointerup\", onPointerUp);\n    };\n    const onPointerDown = event => {\n      enable();\n      events.dispatchPointerEvent(element, \"pressstart\", event);\n      window.addEventListener(\"pointerup\", onPointerUp);\n    };\n    element.addEventListener(\"pointerdown\", onPointerDown);\n    return () => {\n      element.removeEventListener(\"pointerdown\", onPointerDown);\n      window.removeEventListener(\"pointerup\", onPointerUp);\n    };\n  }\n};\nexports.press = press;", "map": {"version": 3, "names": ["events", "require", "press", "isActive", "options", "Boolean", "subscribe", "element", "enable", "disable", "onPointerUp", "event", "dispatchPointerEvent", "window", "removeEventListener", "onPointerDown", "addEventListener", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/gestures/press.cjs.js"], "sourcesContent": ["'use strict';\n\nvar events = require('../utils/events.cjs.js');\n\nconst press = {\n    isActive: (options) => <PERSON><PERSON>an(options.press),\n    subscribe: (element, { enable, disable }) => {\n        const onPointerUp = (event) => {\n            disable();\n            events.dispatchPointerEvent(element, \"pressend\", event);\n            window.removeEventListener(\"pointerup\", onPointerUp);\n        };\n        const onPointerDown = (event) => {\n            enable();\n            events.dispatchPointerEvent(element, \"pressstart\", event);\n            window.addEventListener(\"pointerup\", onPointerUp);\n        };\n        element.addEventListener(\"pointerdown\", onPointerDown);\n        return () => {\n            element.removeEventListener(\"pointerdown\", onPointerDown);\n            window.removeEventListener(\"pointerup\", onPointerUp);\n        };\n    },\n};\n\nexports.press = press;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9C,MAAMC,KAAK,GAAG;EACVC,QAAQ,EAAGC,OAAO,IAAKC,OAAO,CAACD,OAAO,CAACF,KAAK,CAAC;EAC7CI,SAAS,EAAEA,CAACC,OAAO,EAAE;IAAEC,MAAM;IAAEC;EAAQ,CAAC,KAAK;IACzC,MAAMC,WAAW,GAAIC,KAAK,IAAK;MAC3BF,OAAO,CAAC,CAAC;MACTT,MAAM,CAACY,oBAAoB,CAACL,OAAO,EAAE,UAAU,EAAEI,KAAK,CAAC;MACvDE,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAEJ,WAAW,CAAC;IACxD,CAAC;IACD,MAAMK,aAAa,GAAIJ,KAAK,IAAK;MAC7BH,MAAM,CAAC,CAAC;MACRR,MAAM,CAACY,oBAAoB,CAACL,OAAO,EAAE,YAAY,EAAEI,KAAK,CAAC;MACzDE,MAAM,CAACG,gBAAgB,CAAC,WAAW,EAAEN,WAAW,CAAC;IACrD,CAAC;IACDH,OAAO,CAACS,gBAAgB,CAAC,aAAa,EAAED,aAAa,CAAC;IACtD,OAAO,MAAM;MACTR,OAAO,CAACO,mBAAmB,CAAC,aAAa,EAAEC,aAAa,CAAC;MACzDF,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAEJ,WAAW,CAAC;IACxD,CAAC;EACL;AACJ,CAAC;AAEDO,OAAO,CAACf,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}