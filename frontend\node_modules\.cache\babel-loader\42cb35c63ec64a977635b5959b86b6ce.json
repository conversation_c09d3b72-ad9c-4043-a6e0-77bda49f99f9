{"ast": null, "code": "import { mix } from './mix.es.js';\nimport { progress } from './progress.es.js';\nfunction fillOffset(offset, remaining) {\n  const min = offset[offset.length - 1];\n  for (let i = 1; i <= remaining; i++) {\n    const offsetProgress = progress(0, remaining, i);\n    offset.push(mix(min, 1, offsetProgress));\n  }\n}\nfunction defaultOffset(length) {\n  const offset = [0];\n  fillOffset(offset, length - 1);\n  return offset;\n}\nexport { defaultOffset, fillOffset };", "map": {"version": 3, "names": ["mix", "progress", "fillOffset", "offset", "remaining", "min", "length", "i", "offsetProgress", "push", "defaultOffset"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/offset.es.js"], "sourcesContent": ["import { mix } from './mix.es.js';\nimport { progress } from './progress.es.js';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mix(min, 1, offsetProgress));\n    }\n}\nfunction defaultOffset(length) {\n    const offset = [0];\n    fillOffset(offset, length - 1);\n    return offset;\n}\n\nexport { defaultOffset, fillOffset };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,QAAQ,QAAQ,kBAAkB;AAE3C,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACnC,MAAMC,GAAG,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,SAAS,EAAEG,CAAC,EAAE,EAAE;IACjC,MAAMC,cAAc,GAAGP,QAAQ,CAAC,CAAC,EAAEG,SAAS,EAAEG,CAAC,CAAC;IAChDJ,MAAM,CAACM,IAAI,CAACT,GAAG,CAACK,GAAG,EAAE,CAAC,EAAEG,cAAc,CAAC,CAAC;EAC5C;AACJ;AACA,SAASE,aAAaA,CAACJ,MAAM,EAAE;EAC3B,MAAMH,MAAM,GAAG,CAAC,CAAC,CAAC;EAClBD,UAAU,CAACC,MAAM,EAAEG,MAAM,GAAG,CAAC,CAAC;EAC9B,OAAOH,MAAM;AACjB;AAEA,SAASO,aAAa,EAAER,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}