{"ast": null, "code": "import { AnimationType } from '../render/utils/types.mjs';\nimport { useDomEvent } from '../events/use-dom-event.mjs';\n\n/**\n *\n * @param props\n * @param ref\n * @internal\n */\nfunction useFocusGesture({\n  whileFocus,\n  visualElement\n}) {\n  const {\n    animationState\n  } = visualElement;\n  const onFocus = () => {\n    animationState && animationState.setActive(AnimationType.Focus, true);\n  };\n  const onBlur = () => {\n    animationState && animationState.setActive(AnimationType.Focus, false);\n  };\n  useDomEvent(visualElement, \"focus\", whileFocus ? onFocus : undefined);\n  useDomEvent(visualElement, \"blur\", whileFocus ? onBlur : undefined);\n}\nexport { useFocusGesture };", "map": {"version": 3, "names": ["AnimationType", "useDomEvent", "useFocusGesture", "whileFocus", "visualElement", "animationState", "onFocus", "setActive", "Focus", "onBlur", "undefined"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/use-focus-gesture.mjs"], "sourcesContent": ["import { AnimationType } from '../render/utils/types.mjs';\nimport { useDomEvent } from '../events/use-dom-event.mjs';\n\n/**\n *\n * @param props\n * @param ref\n * @internal\n */\nfunction useFocusGesture({ whileFocus, visualElement, }) {\n    const { animationState } = visualElement;\n    const onFocus = () => {\n        animationState && animationState.setActive(AnimationType.Focus, true);\n    };\n    const onBlur = () => {\n        animationState && animationState.setActive(AnimationType.Focus, false);\n    };\n    useDomEvent(visualElement, \"focus\", whileFocus ? onFocus : undefined);\n    useDomEvent(visualElement, \"blur\", whileFocus ? onBlur : undefined);\n}\n\nexport { useFocusGesture };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,6BAA6B;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAAC;EAAEC,UAAU;EAAEC;AAAe,CAAC,EAAE;EACrD,MAAM;IAAEC;EAAe,CAAC,GAAGD,aAAa;EACxC,MAAME,OAAO,GAAGA,CAAA,KAAM;IAClBD,cAAc,IAAIA,cAAc,CAACE,SAAS,CAACP,aAAa,CAACQ,KAAK,EAAE,IAAI,CAAC;EACzE,CAAC;EACD,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACjBJ,cAAc,IAAIA,cAAc,CAACE,SAAS,CAACP,aAAa,CAACQ,KAAK,EAAE,KAAK,CAAC;EAC1E,CAAC;EACDP,WAAW,CAACG,aAAa,EAAE,OAAO,EAAED,UAAU,GAAGG,OAAO,GAAGI,SAAS,CAAC;EACrET,WAAW,CAACG,aAAa,EAAE,MAAM,EAAED,UAAU,GAAGM,MAAM,GAAGC,SAAS,CAAC;AACvE;AAEA,SAASR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}