{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar data = require('../data.cjs.js');\n\n/**\n * A list of all transformable axes. We'll use this list to generated a version\n * of each axes for each transform.\n */\nconst axes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * An ordered array of each transformable value. By default, transform values\n * will be sorted to this order.\n */\nconst order = [\"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\"\n};\nconst rotation = {\n  syntax: \"<angle>\",\n  initialValue: \"0deg\",\n  toDefaultUnit: v => v + \"deg\"\n};\nconst baseTransformProperties = {\n  translate: {\n    syntax: \"<length-percentage>\",\n    initialValue: \"0px\",\n    toDefaultUnit: v => v + \"px\"\n  },\n  rotate: rotation,\n  scale: {\n    syntax: \"<number>\",\n    initialValue: 1,\n    toDefaultUnit: utils.noopReturn\n  },\n  skew: rotation\n};\nconst transformDefinitions = new Map();\nconst asTransformCssVar = name => `--motion-${name}`;\n/**\n * Generate a list of every possible transform key\n */\nconst transforms = [\"x\", \"y\", \"z\"];\norder.forEach(name => {\n  axes.forEach(axis => {\n    transforms.push(name + axis);\n    transformDefinitions.set(asTransformCssVar(name + axis), baseTransformProperties[name]);\n  });\n});\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst compareTransformOrder = (a, b) => transforms.indexOf(a) - transforms.indexOf(b);\n/**\n * Provide a quick way to check if a string is the name of a transform\n */\nconst transformLookup = new Set(transforms);\nconst isTransform = name => transformLookup.has(name);\nconst addTransformToElement = (element, name) => {\n  // Map x to translateX etc\n  if (transformAlias[name]) name = transformAlias[name];\n  const {\n    transforms\n  } = data.getAnimationData(element);\n  utils.addUniqueItem(transforms, name);\n  /**\n   * TODO: An optimisation here could be to cache the transform in element data\n   * and only update if this has changed.\n   */\n  element.style.transform = buildTransformTemplate(transforms);\n};\nconst buildTransformTemplate = transforms => transforms.sort(compareTransformOrder).reduce(transformListToString, \"\").trim();\nconst transformListToString = (template, name) => `${template} ${name}(var(${asTransformCssVar(name)}))`;\nexports.addTransformToElement = addTransformToElement;\nexports.asTransformCssVar = asTransformCssVar;\nexports.axes = axes;\nexports.buildTransformTemplate = buildTransformTemplate;\nexports.compareTransformOrder = compareTransformOrder;\nexports.isTransform = isTransform;\nexports.transformAlias = transformAlias;\nexports.transformDefinitions = transformDefinitions;", "map": {"version": 3, "names": ["utils", "require", "data", "axes", "order", "transformAlias", "x", "y", "z", "rotation", "syntax", "initialValue", "toDefaultUnit", "v", "baseTransformProperties", "translate", "rotate", "scale", "noopReturn", "skew", "transformDefinitions", "Map", "asTransformCssVar", "name", "transforms", "for<PERSON>ach", "axis", "push", "set", "compareTransformOrder", "a", "b", "indexOf", "transformLookup", "Set", "isTransform", "has", "addTransformToElement", "element", "getAnimationData", "addUniqueItem", "style", "transform", "buildTransformTemplate", "sort", "reduce", "transformListToString", "trim", "template", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/transforms.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar data = require('../data.cjs.js');\n\n/**\n * A list of all transformable axes. We'll use this list to generated a version\n * of each axes for each transform.\n */\nconst axes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * An ordered array of each transformable value. By default, transform values\n * will be sorted to this order.\n */\nconst order = [\"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n};\nconst rotation = {\n    syntax: \"<angle>\",\n    initialValue: \"0deg\",\n    toDefaultUnit: (v) => v + \"deg\",\n};\nconst baseTransformProperties = {\n    translate: {\n        syntax: \"<length-percentage>\",\n        initialValue: \"0px\",\n        toDefaultUnit: (v) => v + \"px\",\n    },\n    rotate: rotation,\n    scale: {\n        syntax: \"<number>\",\n        initialValue: 1,\n        toDefaultUnit: utils.noopReturn,\n    },\n    skew: rotation,\n};\nconst transformDefinitions = new Map();\nconst asTransformCssVar = (name) => `--motion-${name}`;\n/**\n * Generate a list of every possible transform key\n */\nconst transforms = [\"x\", \"y\", \"z\"];\norder.forEach((name) => {\n    axes.forEach((axis) => {\n        transforms.push(name + axis);\n        transformDefinitions.set(asTransformCssVar(name + axis), baseTransformProperties[name]);\n    });\n});\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst compareTransformOrder = (a, b) => transforms.indexOf(a) - transforms.indexOf(b);\n/**\n * Provide a quick way to check if a string is the name of a transform\n */\nconst transformLookup = new Set(transforms);\nconst isTransform = (name) => transformLookup.has(name);\nconst addTransformToElement = (element, name) => {\n    // Map x to translateX etc\n    if (transformAlias[name])\n        name = transformAlias[name];\n    const { transforms } = data.getAnimationData(element);\n    utils.addUniqueItem(transforms, name);\n    /**\n     * TODO: An optimisation here could be to cache the transform in element data\n     * and only update if this has changed.\n     */\n    element.style.transform = buildTransformTemplate(transforms);\n};\nconst buildTransformTemplate = (transforms) => transforms\n    .sort(compareTransformOrder)\n    .reduce(transformListToString, \"\")\n    .trim();\nconst transformListToString = (template, name) => `${template} ${name}(var(${asTransformCssVar(name)}))`;\n\nexports.addTransformToElement = addTransformToElement;\nexports.asTransformCssVar = asTransformCssVar;\nexports.axes = axes;\nexports.buildTransformTemplate = buildTransformTemplate;\nexports.compareTransformOrder = compareTransformOrder;\nexports.isTransform = isTransform;\nexports.transformAlias = transformAlias;\nexports.transformDefinitions = transformDefinitions;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,IAAI,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAEpC;AACA;AACA;AACA;AACA,MAAME,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChC;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AACtD,MAAMC,cAAc,GAAG;EACnBC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE;AACP,CAAC;AACD,MAAMC,QAAQ,GAAG;EACbC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,MAAM;EACpBC,aAAa,EAAGC,CAAC,IAAKA,CAAC,GAAG;AAC9B,CAAC;AACD,MAAMC,uBAAuB,GAAG;EAC5BC,SAAS,EAAE;IACPL,MAAM,EAAE,qBAAqB;IAC7BC,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAGC,CAAC,IAAKA,CAAC,GAAG;EAC9B,CAAC;EACDG,MAAM,EAAEP,QAAQ;EAChBQ,KAAK,EAAE;IACHP,MAAM,EAAE,UAAU;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAEZ,KAAK,CAACkB;EACzB,CAAC;EACDC,IAAI,EAAEV;AACV,CAAC;AACD,MAAMW,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACtC,MAAMC,iBAAiB,GAAIC,IAAI,IAAK,YAAYA,IAAI,EAAE;AACtD;AACA;AACA;AACA,MAAMC,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClCpB,KAAK,CAACqB,OAAO,CAAEF,IAAI,IAAK;EACpBpB,IAAI,CAACsB,OAAO,CAAEC,IAAI,IAAK;IACnBF,UAAU,CAACG,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAAC;IAC5BN,oBAAoB,CAACQ,GAAG,CAACN,iBAAiB,CAACC,IAAI,GAAGG,IAAI,CAAC,EAAEZ,uBAAuB,CAACS,IAAI,CAAC,CAAC;EAC3F,CAAC,CAAC;AACN,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMM,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKP,UAAU,CAACQ,OAAO,CAACF,CAAC,CAAC,GAAGN,UAAU,CAACQ,OAAO,CAACD,CAAC,CAAC;AACrF;AACA;AACA;AACA,MAAME,eAAe,GAAG,IAAIC,GAAG,CAACV,UAAU,CAAC;AAC3C,MAAMW,WAAW,GAAIZ,IAAI,IAAKU,eAAe,CAACG,GAAG,CAACb,IAAI,CAAC;AACvD,MAAMc,qBAAqB,GAAGA,CAACC,OAAO,EAAEf,IAAI,KAAK;EAC7C;EACA,IAAIlB,cAAc,CAACkB,IAAI,CAAC,EACpBA,IAAI,GAAGlB,cAAc,CAACkB,IAAI,CAAC;EAC/B,MAAM;IAAEC;EAAW,CAAC,GAAGtB,IAAI,CAACqC,gBAAgB,CAACD,OAAO,CAAC;EACrDtC,KAAK,CAACwC,aAAa,CAAChB,UAAU,EAAED,IAAI,CAAC;EACrC;AACJ;AACA;AACA;EACIe,OAAO,CAACG,KAAK,CAACC,SAAS,GAAGC,sBAAsB,CAACnB,UAAU,CAAC;AAChE,CAAC;AACD,MAAMmB,sBAAsB,GAAInB,UAAU,IAAKA,UAAU,CACpDoB,IAAI,CAACf,qBAAqB,CAAC,CAC3BgB,MAAM,CAACC,qBAAqB,EAAE,EAAE,CAAC,CACjCC,IAAI,CAAC,CAAC;AACX,MAAMD,qBAAqB,GAAGA,CAACE,QAAQ,EAAEzB,IAAI,KAAK,GAAGyB,QAAQ,IAAIzB,IAAI,QAAQD,iBAAiB,CAACC,IAAI,CAAC,IAAI;AAExG0B,OAAO,CAACZ,qBAAqB,GAAGA,qBAAqB;AACrDY,OAAO,CAAC3B,iBAAiB,GAAGA,iBAAiB;AAC7C2B,OAAO,CAAC9C,IAAI,GAAGA,IAAI;AACnB8C,OAAO,CAACN,sBAAsB,GAAGA,sBAAsB;AACvDM,OAAO,CAACpB,qBAAqB,GAAGA,qBAAqB;AACrDoB,OAAO,CAACd,WAAW,GAAGA,WAAW;AACjCc,OAAO,CAAC5C,cAAc,GAAGA,cAAc;AACvC4C,OAAO,CAAC7B,oBAAoB,GAAGA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}