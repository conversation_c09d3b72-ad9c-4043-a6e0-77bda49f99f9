{"ast": null, "code": "import { getAnimatableNone } from '../../render/dom/value-types/animatable-none.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\nimport { isZero, getZeroUnit } from './transitions.mjs';\nfunction getKeyframes(value, valueName, target, transition) {\n  const isTargetAnimatable = isAnimatable(valueName, target);\n  let origin = transition.from !== undefined ? transition.from : value.get();\n  if (origin === \"none\" && isTargetAnimatable && typeof target === \"string\") {\n    /**\n     * If we're trying to animate from \"none\", try and get an animatable version\n     * of the target. This could be improved to work both ways.\n     */\n    origin = getAnimatableNone(valueName, target);\n  } else if (isZero(origin) && typeof target === \"string\") {\n    origin = getZeroUnit(target);\n  } else if (!Array.isArray(target) && isZero(target) && typeof origin === \"string\") {\n    target = getZeroUnit(origin);\n  }\n  /**\n   * If the target has been defined as a series of keyframes\n   */\n  if (Array.isArray(target)) {\n    /**\n     * Ensure an initial wildcard keyframe is hydrated by the origin.\n     * TODO: Support extra wildcard keyframes i.e [1, null, 0]\n     */\n    if (target[0] === null) {\n      target[0] = origin;\n    }\n    return target;\n  } else {\n    return [origin, target];\n  }\n}\nexport { getKeyframes };", "map": {"version": 3, "names": ["getAnimatableNone", "isAnimatable", "isZero", "getZeroUnit", "getKeyframes", "value", "valueName", "target", "transition", "isTargetAnimatable", "origin", "from", "undefined", "get", "Array", "isArray"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/utils/keyframes.mjs"], "sourcesContent": ["import { getAnimatableNone } from '../../render/dom/value-types/animatable-none.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\nimport { isZero, getZeroUnit } from './transitions.mjs';\n\nfunction getKeyframes(value, valueName, target, transition) {\n    const isTargetAnimatable = isAnimatable(valueName, target);\n    let origin = transition.from !== undefined ? transition.from : value.get();\n    if (origin === \"none\" && isTargetAnimatable && typeof target === \"string\") {\n        /**\n         * If we're trying to animate from \"none\", try and get an animatable version\n         * of the target. This could be improved to work both ways.\n         */\n        origin = getAnimatableNone(valueName, target);\n    }\n    else if (isZero(origin) && typeof target === \"string\") {\n        origin = getZeroUnit(target);\n    }\n    else if (!Array.isArray(target) &&\n        isZero(target) &&\n        typeof origin === \"string\") {\n        target = getZeroUnit(origin);\n    }\n    /**\n     * If the target has been defined as a series of keyframes\n     */\n    if (Array.isArray(target)) {\n        /**\n         * Ensure an initial wildcard keyframe is hydrated by the origin.\n         * TODO: Support extra wildcard keyframes i.e [1, null, 0]\n         */\n        if (target[0] === null) {\n            target[0] = origin;\n        }\n        return target;\n    }\n    else {\n        return [origin, target];\n    }\n}\n\nexport { getKeyframes };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,MAAM,EAAEC,WAAW,QAAQ,mBAAmB;AAEvD,SAASC,YAAYA,CAACC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACxD,MAAMC,kBAAkB,GAAGR,YAAY,CAACK,SAAS,EAAEC,MAAM,CAAC;EAC1D,IAAIG,MAAM,GAAGF,UAAU,CAACG,IAAI,KAAKC,SAAS,GAAGJ,UAAU,CAACG,IAAI,GAAGN,KAAK,CAACQ,GAAG,CAAC,CAAC;EAC1E,IAAIH,MAAM,KAAK,MAAM,IAAID,kBAAkB,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;IACvE;AACR;AACA;AACA;IACQG,MAAM,GAAGV,iBAAiB,CAACM,SAAS,EAAEC,MAAM,CAAC;EACjD,CAAC,MACI,IAAIL,MAAM,CAACQ,MAAM,CAAC,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;IACnDG,MAAM,GAAGP,WAAW,CAACI,MAAM,CAAC;EAChC,CAAC,MACI,IAAI,CAACO,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,IAC3BL,MAAM,CAACK,MAAM,CAAC,IACd,OAAOG,MAAM,KAAK,QAAQ,EAAE;IAC5BH,MAAM,GAAGJ,WAAW,CAACO,MAAM,CAAC;EAChC;EACA;AACJ;AACA;EACI,IAAII,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;IACvB;AACR;AACA;AACA;IACQ,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACpBA,MAAM,CAAC,CAAC,CAAC,GAAGG,MAAM;IACtB;IACA,OAAOH,MAAM;EACjB,CAAC,MACI;IACD,OAAO,CAACG,MAAM,EAAEH,MAAM,CAAC;EAC3B;AACJ;AAEA,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}