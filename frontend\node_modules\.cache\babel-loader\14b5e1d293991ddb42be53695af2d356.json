{"ast": null, "code": "'use strict';\n\n/**\n * Determines whether the payload is an error thrown by A<PERSON>os\n *\n * @param {*} payload The value to test\n * @returns {boolean} True if the payload is an error thrown by <PERSON><PERSON>os, otherwise false\n */\nmodule.exports = function isAxiosError(payload) {\n  return typeof payload === 'object' && payload.isAxiosError === true;\n};", "map": {"version": 3, "names": ["module", "exports", "isAxiosError", "payload"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/axios/lib/helpers/isAxiosError.js"], "sourcesContent": ["'use strict';\n\n/**\n * Determines whether the payload is an error thrown by <PERSON><PERSON>os\n *\n * @param {*} payload The value to test\n * @returns {boolean} True if the payload is an error thrown by <PERSON><PERSON>os, otherwise false\n */\nmodule.exports = function isAxiosError(payload) {\n  return (typeof payload === 'object') && (payload.isAxiosError === true);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC9C,OAAQ,OAAOA,OAAO,KAAK,QAAQ,IAAMA,OAAO,CAACD,YAAY,KAAK,IAAK;AACzE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}