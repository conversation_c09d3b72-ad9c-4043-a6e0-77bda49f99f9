{"version": 3, "file": "estree-walker.umd.js", "sources": ["../src/estree-walker.js"], "sourcesContent": ["export function walk(ast, { enter, leave }) {\n\tvisit(ast, null, enter, leave);\n}\n\nlet shouldSkip = false;\nconst context = { skip: () => shouldSkip = true };\n\nexport const childKeys = {};\n\nconst toString = Object.prototype.toString;\n\nfunction isArray(thing) {\n\treturn toString.call(thing) === '[object Array]';\n}\n\nfunction visit(node, parent, enter, leave, prop, index) {\n\tif (!node) return;\n\n\tif (enter) {\n\t\tconst _shouldSkip = shouldSkip;\n\t\tshouldSkip = false;\n\t\tenter.call(context, node, parent, prop, index);\n\t\tconst skipped = shouldSkip;\n\t\tshouldSkip = _shouldSkip;\n\n\t\tif (skipped) return;\n\t}\n\n\tconst keys = childKeys[node.type] || (\n\t\tchildKeys[node.type] = Object.keys(node).filter(key => typeof node[key] === 'object')\n\t);\n\n\tfor (let i = 0; i < keys.length; i += 1) {\n\t\tconst key = keys[i];\n\t\tconst value = node[key];\n\n\t\tif (isArray(value)) {\n\t\t\tfor (let j = 0; j < value.length; j += 1) {\n\t\t\t\tvisit(value[j], node, enter, leave, key, j);\n\t\t\t}\n\t\t}\n\n\t\telse if (value && value.type) {\n\t\t\tvisit(value, node, enter, leave, key, null);\n\t\t}\n\t}\n\n\tif (leave) {\n\t\tleave(node, parent, prop, index);\n\t}\n}\n"], "names": [], "mappings": ";;;;;;CAAO,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;CAC5C,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;CAChC,CAAC;;CAED,IAAI,UAAU,GAAG,KAAK,CAAC;CACvB,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,MAAM,UAAU,GAAG,IAAI,EAAE,CAAC;;AAElD,AAAY,OAAC,SAAS,GAAG,EAAE,CAAC;;CAE5B,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;;CAE3C,SAAS,OAAO,CAAC,KAAK,EAAE;CACxB,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,gBAAgB,CAAC;CAClD,CAAC;;CAED,SAAS,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;CACxD,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;;CAEnB,CAAC,IAAI,KAAK,EAAE;CACZ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC;CACjC,EAAE,UAAU,GAAG,KAAK,CAAC;CACrB,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;CACjD,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC;CAC7B,EAAE,UAAU,GAAG,WAAW,CAAC;;CAE3B,EAAE,IAAI,OAAO,EAAE,OAAO;CACtB,EAAE;;CAEF,CAAC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;CAClC,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC;CACvF,EAAE,CAAC;;CAEH,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;CAC1C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;;CAE1B,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;CACtB,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;CAC7C,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;CAChD,IAAI;CACJ,GAAG;;CAEH,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;CAChC,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;CAC/C,GAAG;CACH,EAAE;;CAEF,CAAC,IAAI,KAAK,EAAE;CACZ,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;CACnC,EAAE;CACF,CAAC;;;;;;;;;;;;;"}