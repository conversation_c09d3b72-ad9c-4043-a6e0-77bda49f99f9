{"ast": null, "code": "'use strict';\n\nvar tslib = require('tslib');\nvar heyListen = require('hey-listen');\nvar utils = require('@motionone/utils');\nvar stagger = require('../utils/stagger.cjs.js');\nvar animateStyle = require('../animate/animate-style.cjs.js');\nvar controls = require('../animate/utils/controls.cjs.js');\nvar keyframes = require('../animate/utils/keyframes.cjs.js');\nvar options = require('../animate/utils/options.cjs.js');\nvar resolveElements = require('../utils/resolve-elements.cjs.js');\nvar calcTime = require('./utils/calc-time.cjs.js');\nvar edit = require('./utils/edit.cjs.js');\nvar sort = require('./utils/sort.cjs.js');\nvar animation = require('@motionone/animation');\nfunction timeline(definition, options = {}) {\n  var _a;\n  const animationDefinitions = createAnimationsFromTimeline(definition, options);\n  /**\n   * Create and start animations\n   */\n  const animationFactories = animationDefinitions.map(definition => animateStyle.animateStyle(...definition, animation.Animation)).filter(Boolean);\n  return controls.withControls(animationFactories, options,\n  // Get the duration from the first animation definition\n  (_a = animationDefinitions[0]) === null || _a === void 0 ? void 0 : _a[3].duration);\n}\nfunction createAnimationsFromTimeline(definition, _a = {}) {\n  var {\n      defaultOptions = {}\n    } = _a,\n    timelineOptions = tslib.__rest(_a, [\"defaultOptions\"]);\n  const animationDefinitions = [];\n  const elementSequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the definition array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < definition.length; i++) {\n    const segment = definition[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (utils.isString(segment)) {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcTime.calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    const [elementDefinition, keyframes$1, options$1 = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (options$1.at !== undefined) {\n      currentTime = calcTime.calcNextTime(currentTime, options$1.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    /**\n     * Find all the elements specified in the definition and parse value\n     * keyframes from their timeline definitions.\n     */\n    const elements = resolveElements.resolveElements(elementDefinition, elementCache);\n    const numElements = elements.length;\n    for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n      const element = elements[elementIndex];\n      const elementSequence = getElementSequence(element, elementSequences);\n      for (const key in keyframes$1) {\n        const valueSequence = getValueSequence(key, elementSequence);\n        let valueKeyframes = keyframes.keyframesList(keyframes$1[key]);\n        const valueOptions = options.getOptions(options$1, key);\n        let {\n          duration = defaultOptions.duration || utils.defaults.duration,\n          easing = defaultOptions.easing || utils.defaults.easing\n        } = valueOptions;\n        if (utils.isEasingGenerator(easing)) {\n          heyListen.invariant(key === \"opacity\" || valueKeyframes.length > 1, \"spring must be provided 2 keyframes within timeline()\");\n          const custom = easing.createAnimation(valueKeyframes, key !== \"opacity\", () => 0, key);\n          easing = custom.easing;\n          valueKeyframes = custom.keyframes || valueKeyframes;\n          duration = custom.duration || duration;\n        }\n        const delay = stagger.resolveOption(options$1.delay, elementIndex, numElements) || 0;\n        const startTime = currentTime + delay;\n        const targetTime = startTime + duration;\n        /**\n         *\n         */\n        let {\n          offset = utils.defaultOffset(valueKeyframes.length)\n        } = valueOptions;\n        /**\n         * If there's only one offset of 0, fill in a second with length 1\n         *\n         * TODO: Ensure there's a test that covers this removal\n         */\n        if (offset.length === 1 && offset[0] === 0) {\n          offset[1] = 1;\n        }\n        /**\n         * Fill out if offset if fewer offsets than keyframes\n         */\n        const remainder = offset.length - valueKeyframes.length;\n        remainder > 0 && utils.fillOffset(offset, remainder);\n        /**\n         * If only one value has been set, ie [1], push a null to the start of\n         * the keyframe array. This will let us mark a keyframe at this point\n         * that will later be hydrated with the previous value.\n         */\n        valueKeyframes.length === 1 && valueKeyframes.unshift(null);\n        /**\n         * Add keyframes, mapping offsets to absolute time.\n         */\n        edit.addKeyframes(valueSequence, valueKeyframes, easing, offset, startTime, targetTime);\n        maxDuration = Math.max(delay + duration, maxDuration);\n        totalDuration = Math.max(targetTime, totalDuration);\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  elementSequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(sort.compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(utils.progress(0, totalDuration, at));\n        valueEasing.push(easing || utils.defaults.easing);\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(\"linear\");\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      animationDefinitions.push([element, key, keyframes, Object.assign(Object.assign(Object.assign({}, defaultOptions), {\n        duration: totalDuration,\n        easing: valueEasing,\n        offset: valueOffset\n      }), timelineOptions)]);\n    }\n  });\n  return animationDefinitions;\n}\nfunction getElementSequence(element, sequences) {\n  !sequences.has(element) && sequences.set(element, {});\n  return sequences.get(element);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nexports.createAnimationsFromTimeline = createAnimationsFromTimeline;\nexports.timeline = timeline;", "map": {"version": 3, "names": ["tslib", "require", "heyListen", "utils", "stagger", "animateStyle", "controls", "keyframes", "options", "resolveElements", "calcTime", "edit", "sort", "animation", "timeline", "definition", "_a", "animationDefinitions", "createAnimationsFromTimeline", "animationFactories", "map", "Animation", "filter", "Boolean", "withControls", "duration", "defaultOptions", "timelineOptions", "__rest", "elementSequences", "Map", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "length", "segment", "isString", "set", "Array", "isArray", "name", "calcNextTime", "at", "elementDefinition", "keyframes$1", "options$1", "undefined", "maxDuration", "elements", "numElements", "elementIndex", "element", "elementSequence", "getElementSequence", "key", "valueSequence", "getValueSequence", "valueKeyframes", "keyframesList", "valueOptions", "getOptions", "defaults", "easing", "isEasingGenerator", "invariant", "custom", "createAnimation", "delay", "resolveOption", "startTime", "targetTime", "offset", "defaultOffset", "remainder", "fillOffset", "unshift", "addKeyframes", "Math", "max", "for<PERSON>ach", "valueSequences", "compareByTime", "valueOffset", "valueEasing", "value", "push", "progress", "Object", "assign", "sequences", "has", "get", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/timeline/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar tslib = require('tslib');\nvar heyListen = require('hey-listen');\nvar utils = require('@motionone/utils');\nvar stagger = require('../utils/stagger.cjs.js');\nvar animateStyle = require('../animate/animate-style.cjs.js');\nvar controls = require('../animate/utils/controls.cjs.js');\nvar keyframes = require('../animate/utils/keyframes.cjs.js');\nvar options = require('../animate/utils/options.cjs.js');\nvar resolveElements = require('../utils/resolve-elements.cjs.js');\nvar calcTime = require('./utils/calc-time.cjs.js');\nvar edit = require('./utils/edit.cjs.js');\nvar sort = require('./utils/sort.cjs.js');\nvar animation = require('@motionone/animation');\n\nfunction timeline(definition, options = {}) {\n    var _a;\n    const animationDefinitions = createAnimationsFromTimeline(definition, options);\n    /**\n     * Create and start animations\n     */\n    const animationFactories = animationDefinitions\n        .map((definition) => animateStyle.animateStyle(...definition, animation.Animation))\n        .filter(Boolean);\n    return controls.withControls(animationFactories, options, \n    // Get the duration from the first animation definition\n    (_a = animationDefinitions[0]) === null || _a === void 0 ? void 0 : _a[3].duration);\n}\nfunction createAnimationsFromTimeline(definition, _a = {}) {\n    var { defaultOptions = {} } = _a, timelineOptions = tslib.__rest(_a, [\"defaultOptions\"]);\n    const animationDefinitions = [];\n    const elementSequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the definition array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < definition.length; i++) {\n        const segment = definition[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (utils.isString(segment)) {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcTime.calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        const [elementDefinition, keyframes$1, options$1 = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (options$1.at !== undefined) {\n            currentTime = calcTime.calcNextTime(currentTime, options$1.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        /**\n         * Find all the elements specified in the definition and parse value\n         * keyframes from their timeline definitions.\n         */\n        const elements = resolveElements.resolveElements(elementDefinition, elementCache);\n        const numElements = elements.length;\n        for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n            const element = elements[elementIndex];\n            const elementSequence = getElementSequence(element, elementSequences);\n            for (const key in keyframes$1) {\n                const valueSequence = getValueSequence(key, elementSequence);\n                let valueKeyframes = keyframes.keyframesList(keyframes$1[key]);\n                const valueOptions = options.getOptions(options$1, key);\n                let { duration = defaultOptions.duration || utils.defaults.duration, easing = defaultOptions.easing || utils.defaults.easing, } = valueOptions;\n                if (utils.isEasingGenerator(easing)) {\n                    heyListen.invariant(key === \"opacity\" || valueKeyframes.length > 1, \"spring must be provided 2 keyframes within timeline()\");\n                    const custom = easing.createAnimation(valueKeyframes, key !== \"opacity\", () => 0, key);\n                    easing = custom.easing;\n                    valueKeyframes = custom.keyframes || valueKeyframes;\n                    duration = custom.duration || duration;\n                }\n                const delay = stagger.resolveOption(options$1.delay, elementIndex, numElements) || 0;\n                const startTime = currentTime + delay;\n                const targetTime = startTime + duration;\n                /**\n                 *\n                 */\n                let { offset = utils.defaultOffset(valueKeyframes.length) } = valueOptions;\n                /**\n                 * If there's only one offset of 0, fill in a second with length 1\n                 *\n                 * TODO: Ensure there's a test that covers this removal\n                 */\n                if (offset.length === 1 && offset[0] === 0) {\n                    offset[1] = 1;\n                }\n                /**\n                 * Fill out if offset if fewer offsets than keyframes\n                 */\n                const remainder = offset.length - valueKeyframes.length;\n                remainder > 0 && utils.fillOffset(offset, remainder);\n                /**\n                 * If only one value has been set, ie [1], push a null to the start of\n                 * the keyframe array. This will let us mark a keyframe at this point\n                 * that will later be hydrated with the previous value.\n                 */\n                valueKeyframes.length === 1 && valueKeyframes.unshift(null);\n                /**\n                 * Add keyframes, mapping offsets to absolute time.\n                 */\n                edit.addKeyframes(valueSequence, valueKeyframes, easing, offset, startTime, targetTime);\n                maxDuration = Math.max(delay + duration, maxDuration);\n                totalDuration = Math.max(targetTime, totalDuration);\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    elementSequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(sort.compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(utils.progress(0, totalDuration, at));\n                valueEasing.push(easing || utils.defaults.easing);\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(\"linear\");\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            animationDefinitions.push([\n                element,\n                key,\n                keyframes,\n                Object.assign(Object.assign(Object.assign({}, defaultOptions), { duration: totalDuration, easing: valueEasing, offset: valueOffset }), timelineOptions),\n            ]);\n        }\n    });\n    return animationDefinitions;\n}\nfunction getElementSequence(element, sequences) {\n    !sequences.has(element) && sequences.set(element, {});\n    return sequences.get(element);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\n\nexports.createAnimationsFromTimeline = createAnimationsFromTimeline;\nexports.timeline = timeline;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIE,KAAK,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIG,OAAO,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AAChD,IAAII,YAAY,GAAGJ,OAAO,CAAC,iCAAiC,CAAC;AAC7D,IAAIK,QAAQ,GAAGL,OAAO,CAAC,kCAAkC,CAAC;AAC1D,IAAIM,SAAS,GAAGN,OAAO,CAAC,mCAAmC,CAAC;AAC5D,IAAIO,OAAO,GAAGP,OAAO,CAAC,iCAAiC,CAAC;AACxD,IAAIQ,eAAe,GAAGR,OAAO,CAAC,kCAAkC,CAAC;AACjE,IAAIS,QAAQ,GAAGT,OAAO,CAAC,0BAA0B,CAAC;AAClD,IAAIU,IAAI,GAAGV,OAAO,CAAC,qBAAqB,CAAC;AACzC,IAAIW,IAAI,GAAGX,OAAO,CAAC,qBAAqB,CAAC;AACzC,IAAIY,SAAS,GAAGZ,OAAO,CAAC,sBAAsB,CAAC;AAE/C,SAASa,QAAQA,CAACC,UAAU,EAAEP,OAAO,GAAG,CAAC,CAAC,EAAE;EACxC,IAAIQ,EAAE;EACN,MAAMC,oBAAoB,GAAGC,4BAA4B,CAACH,UAAU,EAAEP,OAAO,CAAC;EAC9E;AACJ;AACA;EACI,MAAMW,kBAAkB,GAAGF,oBAAoB,CAC1CG,GAAG,CAAEL,UAAU,IAAKV,YAAY,CAACA,YAAY,CAAC,GAAGU,UAAU,EAAEF,SAAS,CAACQ,SAAS,CAAC,CAAC,CAClFC,MAAM,CAACC,OAAO,CAAC;EACpB,OAAOjB,QAAQ,CAACkB,YAAY,CAACL,kBAAkB,EAAEX,OAAO;EACxD;EACA,CAACQ,EAAE,GAAGC,oBAAoB,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACS,QAAQ,CAAC;AACvF;AACA,SAASP,4BAA4BA,CAACH,UAAU,EAAEC,EAAE,GAAG,CAAC,CAAC,EAAE;EACvD,IAAI;MAAEU,cAAc,GAAG,CAAC;IAAE,CAAC,GAAGV,EAAE;IAAEW,eAAe,GAAG3B,KAAK,CAAC4B,MAAM,CAACZ,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACxF,MAAMC,oBAAoB,GAAG,EAAE;EAC/B,MAAMY,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClC,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIF,GAAG,CAAC,CAAC;EAC5B,IAAIG,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,UAAU,CAACsB,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAME,OAAO,GAAGvB,UAAU,CAACqB,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAIjC,KAAK,CAACoC,QAAQ,CAACD,OAAO,CAAC,EAAE;MACzBN,UAAU,CAACQ,GAAG,CAACF,OAAO,EAAEJ,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACO,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAE;MAC9BN,UAAU,CAACQ,GAAG,CAACF,OAAO,CAACK,IAAI,EAAEjC,QAAQ,CAACkC,YAAY,CAACV,WAAW,EAAEI,OAAO,CAACO,EAAE,EAAEZ,QAAQ,EAAED,UAAU,CAAC,CAAC;MAClG;IACJ;IACA,MAAM,CAACc,iBAAiB,EAAEC,WAAW,EAAEC,SAAS,GAAG,CAAC,CAAC,CAAC,GAAGV,OAAO;IAChE;AACR;AACA;AACA;IACQ,IAAIU,SAAS,CAACH,EAAE,KAAKI,SAAS,EAAE;MAC5Bf,WAAW,GAAGxB,QAAQ,CAACkC,YAAY,CAACV,WAAW,EAAEc,SAAS,CAACH,EAAE,EAAEZ,QAAQ,EAAED,UAAU,CAAC;IACxF;IACA;AACR;AACA;AACA;IACQ,IAAIkB,WAAW,GAAG,CAAC;IACnB;AACR;AACA;AACA;IACQ,MAAMC,QAAQ,GAAG1C,eAAe,CAACA,eAAe,CAACqC,iBAAiB,EAAEf,YAAY,CAAC;IACjF,MAAMqB,WAAW,GAAGD,QAAQ,CAACd,MAAM;IACnC,KAAK,IAAIgB,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGD,WAAW,EAAEC,YAAY,EAAE,EAAE;MACnE,MAAMC,OAAO,GAAGH,QAAQ,CAACE,YAAY,CAAC;MACtC,MAAME,eAAe,GAAGC,kBAAkB,CAACF,OAAO,EAAEzB,gBAAgB,CAAC;MACrE,KAAK,MAAM4B,GAAG,IAAIV,WAAW,EAAE;QAC3B,MAAMW,aAAa,GAAGC,gBAAgB,CAACF,GAAG,EAAEF,eAAe,CAAC;QAC5D,IAAIK,cAAc,GAAGrD,SAAS,CAACsD,aAAa,CAACd,WAAW,CAACU,GAAG,CAAC,CAAC;QAC9D,MAAMK,YAAY,GAAGtD,OAAO,CAACuD,UAAU,CAACf,SAAS,EAAES,GAAG,CAAC;QACvD,IAAI;UAAEhC,QAAQ,GAAGC,cAAc,CAACD,QAAQ,IAAItB,KAAK,CAAC6D,QAAQ,CAACvC,QAAQ;UAAEwC,MAAM,GAAGvC,cAAc,CAACuC,MAAM,IAAI9D,KAAK,CAAC6D,QAAQ,CAACC;QAAQ,CAAC,GAAGH,YAAY;QAC9I,IAAI3D,KAAK,CAAC+D,iBAAiB,CAACD,MAAM,CAAC,EAAE;UACjC/D,SAAS,CAACiE,SAAS,CAACV,GAAG,KAAK,SAAS,IAAIG,cAAc,CAACvB,MAAM,GAAG,CAAC,EAAE,uDAAuD,CAAC;UAC5H,MAAM+B,MAAM,GAAGH,MAAM,CAACI,eAAe,CAACT,cAAc,EAAEH,GAAG,KAAK,SAAS,EAAE,MAAM,CAAC,EAAEA,GAAG,CAAC;UACtFQ,MAAM,GAAGG,MAAM,CAACH,MAAM;UACtBL,cAAc,GAAGQ,MAAM,CAAC7D,SAAS,IAAIqD,cAAc;UACnDnC,QAAQ,GAAG2C,MAAM,CAAC3C,QAAQ,IAAIA,QAAQ;QAC1C;QACA,MAAM6C,KAAK,GAAGlE,OAAO,CAACmE,aAAa,CAACvB,SAAS,CAACsB,KAAK,EAAEjB,YAAY,EAAED,WAAW,CAAC,IAAI,CAAC;QACpF,MAAMoB,SAAS,GAAGtC,WAAW,GAAGoC,KAAK;QACrC,MAAMG,UAAU,GAAGD,SAAS,GAAG/C,QAAQ;QACvC;AAChB;AACA;QACgB,IAAI;UAAEiD,MAAM,GAAGvE,KAAK,CAACwE,aAAa,CAACf,cAAc,CAACvB,MAAM;QAAE,CAAC,GAAGyB,YAAY;QAC1E;AAChB;AACA;AACA;AACA;QACgB,IAAIY,MAAM,CAACrC,MAAM,KAAK,CAAC,IAAIqC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UACxCA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;QACjB;QACA;AAChB;AACA;QACgB,MAAME,SAAS,GAAGF,MAAM,CAACrC,MAAM,GAAGuB,cAAc,CAACvB,MAAM;QACvDuC,SAAS,GAAG,CAAC,IAAIzE,KAAK,CAAC0E,UAAU,CAACH,MAAM,EAAEE,SAAS,CAAC;QACpD;AAChB;AACA;AACA;AACA;QACgBhB,cAAc,CAACvB,MAAM,KAAK,CAAC,IAAIuB,cAAc,CAACkB,OAAO,CAAC,IAAI,CAAC;QAC3D;AAChB;AACA;QACgBnE,IAAI,CAACoE,YAAY,CAACrB,aAAa,EAAEE,cAAc,EAAEK,MAAM,EAAES,MAAM,EAAEF,SAAS,EAAEC,UAAU,CAAC;QACvFvB,WAAW,GAAG8B,IAAI,CAACC,GAAG,CAACX,KAAK,GAAG7C,QAAQ,EAAEyB,WAAW,CAAC;QACrDf,aAAa,GAAG6C,IAAI,CAACC,GAAG,CAACR,UAAU,EAAEtC,aAAa,CAAC;MACvD;IACJ;IACAF,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIgB,WAAW;EAC9B;EACA;AACJ;AACA;EACIrB,gBAAgB,CAACqD,OAAO,CAAC,CAACC,cAAc,EAAE7B,OAAO,KAAK;IAClD,KAAK,MAAMG,GAAG,IAAI0B,cAAc,EAAE;MAC9B,MAAMzB,aAAa,GAAGyB,cAAc,CAAC1B,GAAG,CAAC;MACzC;AACZ;AACA;MACYC,aAAa,CAAC9C,IAAI,CAACA,IAAI,CAACwE,aAAa,CAAC;MACtC,MAAM7E,SAAS,GAAG,EAAE;MACpB,MAAM8E,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,aAAa,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAES,EAAE;UAAE0C,KAAK;UAAEtB;QAAO,CAAC,GAAGP,aAAa,CAACtB,CAAC,CAAC;QAC9C7B,SAAS,CAACiF,IAAI,CAACD,KAAK,CAAC;QACrBF,WAAW,CAACG,IAAI,CAACrF,KAAK,CAACsF,QAAQ,CAAC,CAAC,EAAEtD,aAAa,EAAEU,EAAE,CAAC,CAAC;QACtDyC,WAAW,CAACE,IAAI,CAACvB,MAAM,IAAI9D,KAAK,CAAC6D,QAAQ,CAACC,MAAM,CAAC;MACrD;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIoB,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACP,OAAO,CAAC,CAAC,CAAC;QACtBvE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/B+E,WAAW,CAACR,OAAO,CAAC,QAAQ,CAAC;MACjC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIO,WAAW,CAACA,WAAW,CAAChD,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3CgD,WAAW,CAACG,IAAI,CAAC,CAAC,CAAC;QACnBjF,SAAS,CAACiF,IAAI,CAAC,IAAI,CAAC;MACxB;MACAvE,oBAAoB,CAACuE,IAAI,CAAC,CACtBlC,OAAO,EACPG,GAAG,EACHlD,SAAS,EACTmF,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjE,cAAc,CAAC,EAAE;QAAED,QAAQ,EAAEU,aAAa;QAAE8B,MAAM,EAAEqB,WAAW;QAAEZ,MAAM,EAAEW;MAAY,CAAC,CAAC,EAAE1D,eAAe,CAAC,CAC1J,CAAC;IACN;EACJ,CAAC,CAAC;EACF,OAAOV,oBAAoB;AAC/B;AACA,SAASuC,kBAAkBA,CAACF,OAAO,EAAEsC,SAAS,EAAE;EAC5C,CAACA,SAAS,CAACC,GAAG,CAACvC,OAAO,CAAC,IAAIsC,SAAS,CAACpD,GAAG,CAACc,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOsC,SAAS,CAACE,GAAG,CAACxC,OAAO,CAAC;AACjC;AACA,SAASK,gBAAgBA,CAAChB,IAAI,EAAEiD,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACjD,IAAI,CAAC,EAChBiD,SAAS,CAACjD,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOiD,SAAS,CAACjD,IAAI,CAAC;AAC1B;AAEAoD,OAAO,CAAC7E,4BAA4B,GAAGA,4BAA4B;AACnE6E,OAAO,CAACjF,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}