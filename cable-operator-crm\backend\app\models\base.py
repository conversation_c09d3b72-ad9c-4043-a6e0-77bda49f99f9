"""
Base model classes with common functionality
"""

from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, DateTime, Boolean
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func

from app.core.database import Base


class TimestampMixin:
    """Mixin for created_at and updated_at timestamps"""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True
    )


class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    def soft_delete(self):
        """Mark record as deleted"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """Restore soft deleted record"""
        self.is_deleted = False
        self.deleted_at = None


class BaseModel(Base, TimestampMixin, SoftDeleteMixin):
    """Base model with common functionality"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    
    @declared_attr
    def __tablename__(cls):
        """Generate table name from class name"""
        return cls.__name__.lower() + 's'
    
    def to_dict(self, exclude_fields=None):
        """Convert model to dictionary"""
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: dict, exclude_fields=None):
        """Update model from dictionary"""
        exclude_fields = exclude_fields or ['id', 'created_at', 'updated_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    @classmethod
    def get_column_names(cls):
        """Get all column names"""
        return [column.name for column in cls.__table__.columns]
    
    @classmethod
    def get_searchable_columns(cls):
        """Get columns that can be searched (override in subclasses)"""
        return []
    
    @classmethod
    def get_filterable_columns(cls):
        """Get columns that can be filtered (override in subclasses)"""
        return []
    
    def __repr__(self):
        """String representation"""
        return f"<{self.__class__.__name__}(id={self.id})>"


# Utility functions for models
def get_model_by_name(model_name: str):
    """Get model class by name"""
    from app.models import (
        Company, User, Customer, Plan, Payment, 
        Invoice, Notification, AuditLog
    )
    
    models = {
        'company': Company,
        'user': User,
        'customer': Customer,
        'plan': Plan,
        'payment': Payment,
        'invoice': Invoice,
        'notification': Notification,
        'audit_log': AuditLog,
    }
    
    return models.get(model_name.lower())


def serialize_model(model_instance, include_relations=False):
    """Serialize model instance to dictionary"""
    if model_instance is None:
        return None
    
    result = model_instance.to_dict()
    
    if include_relations:
        # Add related data (implement as needed)
        pass
    
    return result


def deserialize_model(model_class, data: dict):
    """Create model instance from dictionary"""
    instance = model_class()
    instance.update_from_dict(data)
    return instance


# Export base components
__all__ = [
    "BaseModel",
    "TimestampMixin",
    "SoftDeleteMixin",
    "get_model_by_name",
    "serialize_model",
    "deserialize_model",
]
