{"ast": null, "code": "import _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { createMemoryHistory, createLocation, locationsAreEqual, createPath } from 'history';\nimport warning from 'tiny-warning';\nimport invariant from 'tiny-invariant';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport pathToRegexp from 'path-to-regexp';\nimport { isValidElementType } from 'react-is';\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport hoistStatics from 'hoist-non-react-statics';\nvar MAX_SIGNED_31_BIT_INT = **********;\nvar commonjsGlobal = typeof globalThis !== \"undefined\" // 'global proper'\n?\n// eslint-disable-next-line no-undef\nglobalThis : typeof window !== \"undefined\" ? window // Browser\n: typeof global !== \"undefined\" ? global // node.js\n: {};\nfunction getUniqueId() {\n  var key = \"__global_unique_id__\";\n  return commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1;\n} // Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    // eslint-disable-next-line no-self-compare\n    return x !== x && y !== y;\n  }\n}\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n  var contextProp = \"__create-react-context-\" + getUniqueId() + \"__\";\n  var Provider = /*#__PURE__*/function (_React$Component) {\n    _inheritsLoose(Provider, _React$Component);\n    function Provider() {\n      var _this;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n      _this.emitter = createEventEmitter(_this.props.value);\n      return _this;\n    }\n    var _proto = Provider.prototype;\n    _proto.getChildContext = function getChildContext() {\n      var _ref;\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n    _proto.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits;\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits = typeof calculateChangedBits === \"function\" ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== \"production\") {\n            process.env.NODE_ENV !== \"production\" ? warning((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, \"calculateChangedBits: Expected the return value to be a \" + \"31-bit integer. Instead received: \" + changedBits) : void 0;\n          }\n          changedBits |= 0;\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n    _proto.render = function render() {\n      return this.props.children;\n    };\n    return Provider;\n  }(React.Component);\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = PropTypes.object.isRequired, _Provider$childContex);\n  var Consumer = /*#__PURE__*/function (_React$Component2) {\n    _inheritsLoose(Consumer, _React$Component2);\n    function Consumer() {\n      var _this2;\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      _this2 = _React$Component2.call.apply(_React$Component2, [this].concat(args)) || this;\n      _this2.observedBits = void 0;\n      _this2.state = {\n        value: _this2.getValue()\n      };\n      _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({\n            value: _this2.getValue()\n          });\n        }\n      };\n      return _this2;\n    }\n    var _proto2 = Consumer.prototype;\n    _proto2.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n    _proto2.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      var observedBits = this.props.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n    _proto2.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n    _proto2.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n    _proto2.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n    return Consumer;\n  }(React.Component);\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = PropTypes.object, _Consumer$contextType);\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\n\n// MIT License\nvar createContext = React.createContext || createReactContext;\n\n// TODO: Replace with React.createContext once we can assume React 16+\n\nvar createNamedContext = function createNamedContext(name) {\n  var context = createContext();\n  context.displayName = name;\n  return context;\n};\nvar historyContext = /*#__PURE__*/createNamedContext(\"Router-History\");\nvar context = /*#__PURE__*/createNamedContext(\"Router\");\n\n/**\n * The public API for putting history on context.\n */\n\nvar Router = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Router, _React$Component);\n  Router.computeRootMatch = function computeRootMatch(pathname) {\n    return {\n      path: \"/\",\n      url: \"/\",\n      params: {},\n      isExact: pathname === \"/\"\n    };\n  };\n  function Router(props) {\n    var _this;\n    _this = _React$Component.call(this, props) || this;\n    _this.state = {\n      location: props.history.location\n    }; // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n\n    _this._isMounted = false;\n    _this._pendingLocation = null;\n    if (!props.staticContext) {\n      _this.unlisten = props.history.listen(function (location) {\n        _this._pendingLocation = location;\n      });\n    }\n    return _this;\n  }\n  var _proto = Router.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    var _this2 = this;\n    this._isMounted = true;\n    if (this.unlisten) {\n      // Any pre-mount location changes have been captured at\n      // this point, so unregister the listener.\n      this.unlisten();\n    }\n    if (!this.props.staticContext) {\n      this.unlisten = this.props.history.listen(function (location) {\n        if (_this2._isMounted) {\n          _this2.setState({\n            location: location\n          });\n        }\n      });\n    }\n    if (this._pendingLocation) {\n      this.setState({\n        location: this._pendingLocation\n      });\n    }\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  };\n  _proto.render = function render() {\n    return /*#__PURE__*/React.createElement(context.Provider, {\n      value: {\n        history: this.props.history,\n        location: this.state.location,\n        match: Router.computeRootMatch(this.state.location.pathname),\n        staticContext: this.props.staticContext\n      }\n    }, /*#__PURE__*/React.createElement(historyContext.Provider, {\n      children: this.props.children || null,\n      value: this.props.history\n    }));\n  };\n  return Router;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n  Router.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(prevProps.history === this.props.history, \"You cannot change <Router history>\") : void 0;\n  };\n}\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\n\nvar MemoryRouter = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(MemoryRouter, _React$Component);\n  function MemoryRouter() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.history = createMemoryHistory(_this.props);\n    return _this;\n  }\n  var _proto = MemoryRouter.prototype;\n  _proto.render = function render() {\n    return /*#__PURE__*/React.createElement(Router, {\n      history: this.history,\n      children: this.props.children\n    });\n  };\n  return MemoryRouter;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n  MemoryRouter.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!this.props.history, \"<MemoryRouter> ignores the history prop. To use a custom history, \" + \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\") : void 0;\n  };\n}\nvar Lifecycle = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Lifecycle, _React$Component);\n  function Lifecycle() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Lifecycle.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  };\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  };\n  _proto.render = function render() {\n    return null;\n  };\n  return Lifecycle;\n}(React.Component);\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\n\nfunction Prompt(_ref) {\n  var message = _ref.message,\n    _ref$when = _ref.when,\n    when = _ref$when === void 0 ? true : _ref$when;\n  return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n    !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Prompt> outside a <Router>\") : invariant(false) : void 0;\n    if (!when || context.staticContext) return null;\n    var method = context.history.block;\n    return /*#__PURE__*/React.createElement(Lifecycle, {\n      onMount: function onMount(self) {\n        self.release = method(message);\n      },\n      onUpdate: function onUpdate(self, prevProps) {\n        if (prevProps.message !== message) {\n          self.release();\n          self.release = method(message);\n        }\n      },\n      onUnmount: function onUnmount(self) {\n        self.release();\n      },\n      message: message\n    });\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  var messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\nvar cache = {};\nvar cacheLimit = 10000;\nvar cacheCount = 0;\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n  var generator = pathToRegexp.compile(path);\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n  return generator;\n}\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\n\nfunction generatePath(path, params) {\n  if (path === void 0) {\n    path = \"/\";\n  }\n  if (params === void 0) {\n    params = {};\n  }\n  return path === \"/\" ? path : compilePath(path)(params, {\n    pretty: true\n  });\n}\n\n/**\n * The public API for navigating programmatically with a component.\n */\n\nfunction Redirect(_ref) {\n  var computedMatch = _ref.computedMatch,\n    to = _ref.to,\n    _ref$push = _ref.push,\n    push = _ref$push === void 0 ? false : _ref$push;\n  return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n    !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Redirect> outside a <Router>\") : invariant(false) : void 0;\n    var history = context.history,\n      staticContext = context.staticContext;\n    var method = push ? history.push : history.replace;\n    var location = createLocation(computedMatch ? typeof to === \"string\" ? generatePath(to, computedMatch.params) : _extends({}, to, {\n      pathname: generatePath(to.pathname, computedMatch.params)\n    }) : to); // When rendering in a static context,\n    // set the new location immediately.\n\n    if (staticContext) {\n      method(location);\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Lifecycle, {\n      onMount: function onMount() {\n        method(location);\n      },\n      onUpdate: function onUpdate(self, prevProps) {\n        var prevLocation = createLocation(prevProps.to);\n        if (!locationsAreEqual(prevLocation, _extends({}, location, {\n          key: prevLocation.key\n        }))) {\n          method(location);\n        }\n      },\n      to: to\n    });\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\nvar cache$1 = {};\nvar cacheLimit$1 = 10000;\nvar cacheCount$1 = 0;\nfunction compilePath$1(path, options) {\n  var cacheKey = \"\" + options.end + options.strict + options.sensitive;\n  var pathCache = cache$1[cacheKey] || (cache$1[cacheKey] = {});\n  if (pathCache[path]) return pathCache[path];\n  var keys = [];\n  var regexp = pathToRegexp(path, keys, options);\n  var result = {\n    regexp: regexp,\n    keys: keys\n  };\n  if (cacheCount$1 < cacheLimit$1) {\n    pathCache[path] = result;\n    cacheCount$1++;\n  }\n  return result;\n}\n/**\n * Public API for matching a URL pathname to a path.\n */\n\nfunction matchPath(pathname, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = {\n      path: options\n    };\n  }\n  var _options = options,\n    path = _options.path,\n    _options$exact = _options.exact,\n    exact = _options$exact === void 0 ? false : _options$exact,\n    _options$strict = _options.strict,\n    strict = _options$strict === void 0 ? false : _options$strict,\n    _options$sensitive = _options.sensitive,\n    sensitive = _options$sensitive === void 0 ? false : _options$sensitive;\n  var paths = [].concat(path);\n  return paths.reduce(function (matched, path) {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n    var _compilePath = compilePath$1(path, {\n        end: exact,\n        strict: strict,\n        sensitive: sensitive\n      }),\n      regexp = _compilePath.regexp,\n      keys = _compilePath.keys;\n    var match = regexp.exec(pathname);\n    if (!match) return null;\n    var url = match[0],\n      values = match.slice(1);\n    var isExact = pathname === url;\n    if (exact && !isExact) return null;\n    return {\n      path: path,\n      // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url,\n      // the matched portion of the URL\n      isExact: isExact,\n      // whether or not we matched exactly\n      params: keys.reduce(function (memo, key, index) {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\nfunction evalChildrenDev(children, props, path) {\n  var value = children(props);\n  process.env.NODE_ENV !== \"production\" ? warning(value !== undefined, \"You returned `undefined` from the `children` function of \" + (\"<Route\" + (path ? \" path=\\\"\" + path + \"\\\"\" : \"\") + \">, but you \") + \"should have returned a React element or `null`\") : void 0;\n  return value || null;\n}\n/**\n * The public API for matching a single path and rendering.\n */\n\nvar Route = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Route, _React$Component);\n  function Route() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Route.prototype;\n  _proto.render = function render() {\n    var _this = this;\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context$1) {\n      !context$1 ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Route> outside a <Router>\") : invariant(false) : void 0;\n      var location = _this.props.location || context$1.location;\n      var match = _this.props.computedMatch ? _this.props.computedMatch // <Switch> already computed the match for us\n      : _this.props.path ? matchPath(location.pathname, _this.props) : context$1.match;\n      var props = _extends({}, context$1, {\n        location: location,\n        match: match\n      });\n      var _this$props = _this.props,\n        children = _this$props.children,\n        component = _this$props.component,\n        render = _this$props.render; // Preact uses an empty array as children by\n      // default, so use null if that's the case.\n\n      if (Array.isArray(children) && isEmptyChildren(children)) {\n        children = null;\n      }\n      return /*#__PURE__*/React.createElement(context.Provider, {\n        value: props\n      }, props.match ? children ? typeof children === \"function\" ? process.env.NODE_ENV !== \"production\" ? evalChildrenDev(children, props, _this.props.path) : children(props) : children : component ? /*#__PURE__*/React.createElement(component, props) : render ? render(props) : null : typeof children === \"function\" ? process.env.NODE_ENV !== \"production\" ? evalChildrenDev(children, props, _this.props.path) : children(props) : null);\n    });\n  };\n  return Route;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: function component(props, propName) {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\"Invalid prop 'component' supplied to 'Route': the prop is not a valid React component\");\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n  Route.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.children && !isEmptyChildren(this.props.children) && this.props.component), \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.children && !isEmptyChildren(this.props.children) && this.props.render), \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.component && this.props.render), \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\") : void 0;\n  };\n  Route.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.location && !prevProps.location), '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(!this.props.location && prevProps.location), '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.') : void 0;\n  };\n}\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n  return _extends({}, location, {\n    pathname: addLeadingSlash(basename) + location.pathname\n  });\n}\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n  var base = addLeadingSlash(basename);\n  if (location.pathname.indexOf(base) !== 0) return location;\n  return _extends({}, location, {\n    pathname: location.pathname.substr(base.length)\n  });\n}\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\nfunction staticHandler(methodName) {\n  return function () {\n    process.env.NODE_ENV !== \"production\" ? invariant(false, \"You cannot %s with <StaticRouter>\", methodName) : invariant(false);\n  };\n}\nfunction noop() {}\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\n\nvar StaticRouter = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(StaticRouter, _React$Component);\n  function StaticRouter() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.handlePush = function (location) {\n      return _this.navigateTo(location, \"PUSH\");\n    };\n    _this.handleReplace = function (location) {\n      return _this.navigateTo(location, \"REPLACE\");\n    };\n    _this.handleListen = function () {\n      return noop;\n    };\n    _this.handleBlock = function () {\n      return noop;\n    };\n    return _this;\n  }\n  var _proto = StaticRouter.prototype;\n  _proto.navigateTo = function navigateTo(location, action) {\n    var _this$props = this.props,\n      _this$props$basename = _this$props.basename,\n      basename = _this$props$basename === void 0 ? \"\" : _this$props$basename,\n      _this$props$context = _this$props.context,\n      context = _this$props$context === void 0 ? {} : _this$props$context;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  };\n  _proto.render = function render() {\n    var _this$props2 = this.props,\n      _this$props2$basename = _this$props2.basename,\n      basename = _this$props2$basename === void 0 ? \"\" : _this$props2$basename,\n      _this$props2$context = _this$props2.context,\n      context = _this$props2$context === void 0 ? {} : _this$props2$context,\n      _this$props2$location = _this$props2.location,\n      location = _this$props2$location === void 0 ? \"/\" : _this$props2$location,\n      rest = _objectWithoutPropertiesLoose(_this$props2, [\"basename\", \"context\", \"location\"]);\n    var history = {\n      createHref: function createHref(path) {\n        return addLeadingSlash(basename + createURL(path));\n      },\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n    return /*#__PURE__*/React.createElement(Router, _extends({}, rest, {\n      history: history,\n      staticContext: context\n    }));\n  };\n  return StaticRouter;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n  StaticRouter.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!this.props.history, \"<StaticRouter> ignores the history prop. To use a custom history, \" + \"use `import { Router }` instead of `import { StaticRouter as Router }`.\") : void 0;\n  };\n}\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\n\nvar Switch = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Switch, _React$Component);\n  function Switch() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Switch.prototype;\n  _proto.render = function render() {\n    var _this = this;\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n      !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Switch> outside a <Router>\") : invariant(false) : void 0;\n      var location = _this.props.location || context.location;\n      var element, match; // We use React.Children.forEach instead of React.Children.toArray().find()\n      // here because toArray adds keys to all child elements and we do not want\n      // to trigger an unmount/remount for two <Route>s that render the same\n      // component at different URLs.\n\n      React.Children.forEach(_this.props.children, function (child) {\n        if (match == null && /*#__PURE__*/React.isValidElement(child)) {\n          element = child;\n          var path = child.props.path || child.props.from;\n          match = path ? matchPath(location.pathname, _extends({}, child.props, {\n            path: path\n          })) : context.match;\n        }\n      });\n      return match ? /*#__PURE__*/React.cloneElement(element, {\n        location: location,\n        computedMatch: match\n      }) : null;\n    });\n  };\n  return Switch;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n  Switch.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.location && !prevProps.location), '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(!this.props.location && prevProps.location), '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.') : void 0;\n  };\n}\n\n/**\n * A public higher-order component to access the imperative API\n */\n\nfunction withRouter(Component) {\n  var displayName = \"withRouter(\" + (Component.displayName || Component.name) + \")\";\n  var C = function C(props) {\n    var wrappedComponentRef = props.wrappedComponentRef,\n      remainingProps = _objectWithoutPropertiesLoose(props, [\"wrappedComponentRef\"]);\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n      !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <\" + displayName + \" /> outside a <Router>\") : invariant(false) : void 0;\n      return /*#__PURE__*/React.createElement(Component, _extends({}, remainingProps, context, {\n        ref: wrappedComponentRef\n      }));\n    });\n  };\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n  if (process.env.NODE_ENV !== \"production\") {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([PropTypes.string, PropTypes.func, PropTypes.object])\n    };\n  }\n  return hoistStatics(C, Component);\n}\nvar useContext = React.useContext;\nfunction useHistory() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useHistory()\") : invariant(false) : void 0;\n  }\n  return useContext(historyContext);\n}\nfunction useLocation() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useLocation()\") : invariant(false) : void 0;\n  }\n  return useContext(context).location;\n}\nfunction useParams() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useParams()\") : invariant(false) : void 0;\n  }\n  var match = useContext(context).match;\n  return match ? match.params : {};\n}\nfunction useRouteMatch(path) {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useRouteMatch()\") : invariant(false) : void 0;\n  }\n  var location = useLocation();\n  var match = useContext(context).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  if (typeof window !== \"undefined\") {\n    var global$1 = window;\n    var key = \"__react_router_build__\";\n    var buildNames = {\n      cjs: \"CommonJS\",\n      esm: \"ES modules\",\n      umd: \"UMD\"\n    };\n    if (global$1[key] && global$1[key] !== \"esm\") {\n      var initialBuildName = buildNames[global$1[key]];\n      var secondaryBuildName = buildNames[\"esm\"]; // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n\n      throw new Error(\"You are loading the \" + secondaryBuildName + \" build of React Router \" + (\"on a page that is already running the \" + initialBuildName + \" \") + \"build, so things won't work right.\");\n    }\n    global$1[key] = \"esm\";\n  }\n}\nexport { MemoryRouter, Prompt, Redirect, Route, Router, StaticRouter, Switch, historyContext as __HistoryContext, context as __RouterContext, generatePath, matchPath, useHistory, useLocation, useParams, useRouteMatch, withRouter };", "map": {"version": 3, "sources": ["../modules/miniCreateReactContext.js", "../modules/createContext.js", "../modules/createNamedContext.js", "../modules/HistoryContext.js", "../modules/RouterContext.js", "../modules/Router.js", "../modules/MemoryRouter.js", "../modules/Lifecycle.js", "../modules/Prompt.js", "../modules/generatePath.js", "../modules/Redirect.js", "../modules/matchPath.js", "../modules/Route.js", "../modules/StaticRouter.js", "../modules/Switch.js", "../modules/withRouter.js", "../modules/hooks.js", "../modules/index.js"], "names": ["MAX_SIGNED_31_BIT_INT", "commonjsGlobal", "globalThis", "window", "global", "getUniqueId", "key", "objectIs", "x", "y", "createEventEmitter", "value", "handlers", "on", "handler", "push", "off", "filter", "h", "get", "set", "newValue", "changedBits", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "children", "Array", "isArray", "createReactContext", "defaultValue", "calculateChangedBits", "contextProp", "Provider", "React", "Component", "emitter", "props", "childContextTypes", "PropTypes", "object", "isRequired", "getChildContext", "componentWillReceiveProps", "nextProps", "oldValue", "process", "env", "NODE_ENV", "warning", "render", "Consumer", "contextTypes", "observedBits", "state", "getValue", "undefined", "componentDidMount", "context", "onUpdate", "componentWillUnmount", "setState", "createContext", "createNamedContext", "displayName", "name", "historyContext", "Router", "computeRootMatch", "pathname", "path", "url", "params", "isExact", "location", "history", "_isMounted", "_pendingLocation", "staticContext", "unlisten", "listen", "RouterContext", "match", "HistoryContext", "propTypes", "node", "prototype", "componentDidUpdate", "prevProps", "MemoryRouter", "createHistory", "initialEntries", "array", "initialIndex", "number", "getUserConfirmation", "func", "<PERSON><PERSON><PERSON><PERSON>", "Lifecycle", "onMount", "call", "onUnmount", "Prompt", "message", "when", "method", "block", "self", "release", "messageType", "oneOfType", "string", "bool", "cache", "cacheLimit", "cacheCount", "compilePath", "generator", "pathToRegexp", "compile", "generatePath", "pretty", "Redirect", "computedMatch", "to", "replace", "createLocation", "prevLocation", "locationsAreEqual", "from", "options", "cache<PERSON>ey", "end", "strict", "sensitive", "pathCache", "keys", "regexp", "result", "matchPath", "exact", "paths", "concat", "reduce", "matched", "exec", "values", "memo", "index", "isEmptyChildren", "Children", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "component", "createElement", "propName", "isValidElementType", "Error", "arrayOf", "addLeadingSlash", "char<PERSON>t", "addBasename", "basename", "stripBasename", "base", "indexOf", "substr", "length", "createURL", "createPath", "static<PERSON><PERSON><PERSON>", "methodName", "invariant", "noop", "StaticRouter", "navigateTo", "action", "handlePush", "handleReplace", "handleListen", "handleBlock", "rest", "createHref", "go", "goBack", "goForward", "Switch", "element", "isValidElement", "child", "cloneElement", "with<PERSON><PERSON><PERSON>", "C", "wrappedComponentRef", "remainingProps", "WrappedComponent", "hoistStatics", "useContext", "useHistory", "useLocation", "useParams", "useRouteMatch", "buildNames", "cjs", "esm", "umd", "initialBuildName", "secondaryBuildName"], "mappings": ";;;;;;;;;;;AAQA,IAAMA,qBAAqB,GAAG,UAA9B;AAEA,IAAMC,cAAc,GAClB,OAAOC,UAAP,KAAsB,WAAtB,CAAA;AAAA;;AAEIA,UAFJ,GAGI,OAAOC,MAAP,KAAkB,WAAlB,GACAA,MADA,CAAA;AAAA,EAEA,OAAOC,MAAP,KAAkB,WAAlB,GACAA,MADA,CAAA;AAAA,EAEA,CAAA,CARN;AAUA,SAASC,WAAT,CAAA,EAAuB;MACjBC,GAAG,GAAG,sBAAV;SACQL,cAAc,CAACK,GAAD,CAAdL,GAAsB,CAACA,cAAc,CAACK,GAAD,CAAdL,IAAuB,CAAxB,IAA6B,CAA3D;;;;AAKF,SAASM,QAAT,CAAkBC,CAAlB,EAAqBC,CAArB,EAAwB;MAClBD,CAAC,KAAKC,CAAV,EAAa;WACJD,CAAC,KAAK,CAANA,IAAW,CAAA,GAAIA,CAAJ,KAAU,CAAA,GAAIC,CAAhC;GADF,MAEO;;WAEED,CAAC,KAAKA,CAANA,IAAWC,CAAC,KAAKA,CAAxB;;;AAIJ,SAASC,kBAAT,CAA4BC,KAA5B,EAAmC;MAC7BC,QAAQ,GAAG,EAAf;SACO;IACLC,EADK,EAAA,SAAA,EAAA,CACFC,OADE,EACO;MACVF,QAAQ,CAACG,IAATH,CAAcE,OAAdF,CAAAA;KAFG;IAKLI,GALK,EAAA,SAAA,GAAA,CAKDF,OALC,EAKQ;MACXF,QAAQ,GAAG,QAAQ,CAACK,MAAT,CAAgB,UAAA,CAAC,EAAA;eAAIC,CAAC,KAAKJ,OAAV;OAAjB,CAAXF;KANG;IASLO,GATK,EAAA,SAAA,GAAA,CAAA,EASC;aACGR,KAAP;KAVG;IAaLS,GAbK,EAAA,SAAA,GAAA,CAaDC,QAbC,EAaSC,WAbT,EAasB;MACzBX,KAAK,GAAGU,QAARV;MACAC,QAAQ,CAACW,OAATX,CAAiB,UAAA,OAAO,EAAA;eAAIE,OAAO,CAACH,KAAD,EAAQW,WAAR,CAAX;OAAxBV,CAAAA;;GAfJ;;AAoBF,SAASY,SAAT,CAAmBC,QAAnB,EAA6B;SACpBC,KAAK,CAACC,OAAND,CAAcD,QAAdC,CAAAA,GAA0BD,QAAQ,CAAC,CAAD,CAAlCC,GAAwCD,QAA/C;;AAGa,SAASG,kBAAT,CAA4BC,YAA5B,EAA0CC,oBAA1C,EAAgE;;MACvEC,WAAW,GAAG,yBAAA,GAA4B1B,WAAW,CAAA,CAAvC,GAA4C,IAAhE;MAEM2B,QAHuE,GAAA,aAAA,UAAA,gBAAA,EAAA;;;;;;;;YAI3EG,OAJ2E,GAIjEzB,kBAAkB,CAAC,KAAA,CAAK0B,KAAL,CAAWzB,KAAZ,CAJ+C;;;;WAU3E8B,eAV2E,GAU3EA,SAAAA,eAAAA,CAAAA,EAAkB;;6BAEbV,WADH,CAAA,GACiB,IAAA,CAAKI,OADtB,EAAA,IAAA;KAXyE;WAgB3EO,yBAhB2E,GAgB3EA,SAAAA,yBAAAA,CAA0BC,SAA1BD,EAAqC;UAC/B,IAAA,CAAKN,KAAL,CAAWzB,KAAX,KAAqBgC,SAAS,CAAChC,KAAnC,EAA0C;YACpCiC,QAAQ,GAAG,IAAA,CAAKR,KAAL,CAAWzB,KAA1B;YACIU,QAAQ,GAAGsB,SAAS,CAAChC,KAAzB;YACIW,WAAJ;YAEIf,QAAQ,CAACqC,QAAD,EAAWvB,QAAX,CAAZ,EAAkC;UAChCC,WAAW,GAAG,CAAdA,CADgC,CAAA;SAAlC,MAEO;UACLA,WAAW,GACT,OAAOQ,oBAAP,KAAgC,UAAhC,GACIA,oBAAoB,CAACc,QAAD,EAAWvB,QAAX,CADxB,GAEIrB,qBAHNsB;cAIIuB,OAAO,CAACC,GAARD,CAAYE,QAAZF,KAAyB,YAA7B,EAA2C;oDACzCG,OAAO,CACL,CAAC1B,WAAW,GAAGtB,qBAAf,MAA0CsB,WADrC,EAEL,0DAAA,GACE,oCADF,GAEEA,WAJG,CAAP0B,GAAAA,KAAAA,CAAAA;;UAQF1B,WAAW,IAAI,CAAfA;cAEIA,WAAW,KAAK,CAApB,EAAuB;iBAChBa,OAAL,CAAaf,GAAb,CAAiBuB,SAAS,CAAChC,KAA3B,EAAkCW,WAAlC,CAAA;;;;KAzCmE;WA+C3E2B,MA/C2E,GA+C3EA,SAAAA,MAAAA,CAAAA,EAAS;aACA,IAAA,CAAKb,KAAL,CAAWX,QAAlB;KAhDyE;;IAGtDQ,KAAK,CAACC,SAHgD,CAAA;EAGvEF,QAHuE,CAMpEK,iBANoE,IAAA,qBAAA,GAAA,CAAA,CAAA,EAAA,qBAAA,CAOxEN,WAPwE,CAAA,GAO1DO,SAAS,CAACC,MAAVD,CAAiBE,UAPyC,EAAA,qBAAA,CAAA;MAoDvEU,QApDuE,GAAA,aAAA,UAAA,iBAAA,EAAA;;;;;;;;aAyD3EE,YAzD2E,GAAA,KAAA,CAAA;aA2D3EC,KA3D2E,GA2DnE;QACN1C,KAAK,EAAE,MAAA,CAAK2C,QAAL,CAAA;OA5DkE;aAgG3EI,QAhG2E,GAgGhE,UAACrC,QAAD,EAAWC,WAAX,EAA2B;YAC9B8B,YAAY,GAAG,MAAA,CAAKA,YAAL,GAAoB,CAAzC;YACI,CAACA,YAAY,GAAG9B,WAAhB,MAAiC,CAArC,EAAwC;iBACjCsC,QAAL,CAAc;YAAEjD,KAAK,EAAE,MAAA,CAAK2C,QAAL,CAAA;WAAvB,CAAA;;OAnGuE;;;;YA+D3EZ,yBA/D2E,GA+D3EA,SAAAA,yBAAAA,CAA0BC,SAA1BD,EAAqC;UAC7BU,YAD6B,GACZT,SADY,CAC7BS,YAD6B;WAE9BA,YAAL,GACEA,YAAY,KAAKG,SAAjBH,IAA8BA,YAAY,KAAK,IAA/CA,GACIpD,qBADJoD,CAAAA;MAAAA,EAEIA,YAHN;KAjEyE;YAuE3EI,iBAvE2E,GAuE3EA,SAAAA,iBAAAA,CAAAA,EAAoB;UACd,IAAA,CAAKC,OAAL,CAAa1B,WAAb,CAAJ,EAA+B;aACxB0B,OAAL,CAAa1B,WAAb,CAAA,CAA0BlB,EAA1B,CAA6B,IAAA,CAAK6C,QAAlC,CAAA;;UAEIN,YAJY,GAIK,IAAA,CAAKhB,KAJV,CAIZgB,YAJY;WAKbA,YAAL,GACEA,YAAY,KAAKG,SAAjBH,IAA8BA,YAAY,KAAK,IAA/CA,GACIpD,qBADJoD,CAAAA;MAAAA,EAEIA,YAHN;KA5EyE;YAkF3EO,oBAlF2E,GAkF3EA,SAAAA,oBAAAA,CAAAA,EAAuB;UACjB,IAAA,CAAKF,OAAL,CAAa1B,WAAb,CAAJ,EAA+B;aACxB0B,OAAL,CAAa1B,WAAb,CAAA,CAA0Bf,GAA1B,CAA8B,IAAA,CAAK0C,QAAnC,CAAA;;KApFuE;YAwF3EJ,QAxF2E,GAwF3EA,SAAAA,QAAAA,CAAAA,EAAW;UACL,IAAA,CAAKG,OAAL,CAAa1B,WAAb,CAAJ,EAA+B;eACtB,IAAA,CAAK0B,OAAL,CAAa1B,WAAb,CAAA,CAA0BZ,GAA1B,CAAA,CAAP;OADF,MAEO;eACEU,YAAP;;KA5FuE;YAuG3EoB,MAvG2E,GAuG3EA,SAAAA,MAAAA,CAAAA,EAAS;aACAzB,SAAS,CAAC,IAAA,CAAKY,KAAL,CAAWX,QAAZ,CAATD,CAA+B,IAAA,CAAK6B,KAAL,CAAW1C,KAA1Ca,CAAP;KAxGyE;;IAoDtDS,KAAK,CAACC,SApDgD,CAAA;EAoDvEgB,QApDuE,CAqDpEC,YArDoE,IAAA,qBAAA,GAAA,CAAA,CAAA,EAAA,qBAAA,CAsDxEpB,WAtDwE,CAAA,GAsD1DO,SAAS,CAACC,MAtDgD,EAAA,qBAAA,CAAA;SA4GtE;IACLP,QAAQ,EAARA,QADK;IAELkB,QAAQ,EAARA;GAFF;;;AC1KF;AAOA,IAAA,aAAA,GAAejB,KAAK,CAAC4B,aAAN5B,IAAuBL,kBAAtC;;ACPA;;AAGA,IAAMkC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAA,IAAI,EAAI;MAC3BL,OAAO,GAAGI,aAAa,CAAA,CAA7B;EACAJ,OAAO,CAACM,WAARN,GAAsBO,IAAtBP;SAEOA,OAAP;CAJF;ACDA,IAAMQ,cAAc,GAAA,aAAiBH,kBAAkB,CAAC,gBAAD,CAAvD;ACAA,IAAML,OAAO,GAAA,aAAiBK,kBAAkB,CAAC,QAAD,CAAhD;;ACKA;;;;IAGMI,MAAAA,GAAAA,aAAAA,UAAAA,gBAAAA,EAAAA;;SACGC,gBAAAA,GAAP,SAAA,gBAAA,CAAwBC,QAAxB,EAAkC;WACzB;MAAEC,IAAI,EAAE,GAAR;MAAaC,GAAG,EAAE,GAAlB;MAAuBC,MAAM,EAAE,CAAA,CAA/B;MAAmCC,OAAO,EAAEJ,QAAQ,KAAK;KAAhE;;kBAGUhC,KAAZ,EAAmB;;wCACXA,KAAN,CAAA,IAAA,IAAA;UAEKiB,KAAL,GAAa;MACXoB,QAAQ,EAAErC,KAAK,CAACsC,OAANtC,CAAcqC;KAD1B,CAHiB,CAAA;;;;;;UAYZE,UAAL,GAAkB,KAAlB;UACKC,gBAAL,GAAwB,IAAxB;QAEI,CAACxC,KAAK,CAACyC,aAAX,EAA0B;YACnBC,QAAL,GAAgB,KAAK,CAACJ,OAAN,CAAcK,MAAd,CAAqB,UAAA,QAAQ,EAAI;cAC1CH,gBAAL,GAAwBH,QAAxB;OADc,CAAhB;;;;;SAMJjB,iBAAAA,GAAAA,SAAAA,iBAAAA,CAAAA,EAAoB;;SACbmB,UAAL,GAAkB,IAAlB;QAEI,IAAA,CAAKG,QAAT,EAAmB;;;WAGZA,QAAL,CAAA,CAAA;;QAEE,CAAC,IAAA,CAAK1C,KAAL,CAAWyC,aAAhB,EAA+B;WACxBC,QAAL,GAAgB,IAAA,CAAK1C,KAAL,CAAWsC,OAAX,CAAmBK,MAAnB,CAA0B,UAAA,QAAQ,EAAI;YAChD,MAAI,CAACJ,UAAT,EAAqB;UACnB,MAAI,CAACf,QAAL,CAAc;YAAEa,QAAQ,EAARA;WAAhB,CAAA;;OAFY,CAAhB;;QAME,IAAA,CAAKG,gBAAT,EAA2B;WACpBhB,QAAL,CAAc;QAAEa,QAAQ,EAAE,IAAA,CAAKG;OAA/B,CAAA;;;SAIJjB,oBAAAA,GAAAA,SAAAA,oBAAAA,CAAAA,EAAuB;QACjB,IAAA,CAAKmB,QAAT,EAAmB;WACZA,QAAL,CAAA,CAAA;WACKH,UAAL,GAAkB,KAAlB;WACKC,gBAAL,GAAwB,IAAxB;;;SAIJ3B,MAAAA,GAAAA,SAAAA,MAAAA,CAAAA,EAAS;wBAEL,KAAA,CAAA,aAAA,CAAC+B,OAAD,CAAe,QAAf,EAAA;MACE,KAAK,EAAE;QACLN,OAAO,EAAE,IAAA,CAAKtC,KAAL,CAAWsC,OADf;QAELD,QAAQ,EAAE,IAAA,CAAKpB,KAAL,CAAWoB,QAFhB;QAGLQ,KAAK,EAAEf,MAAM,CAACC,gBAAPD,CAAwB,IAAA,CAAKb,KAAL,CAAWoB,QAAX,CAAoBL,QAA5CF,CAHF;QAILW,aAAa,EAAE,IAAA,CAAKzC,KAAL,CAAWyC;;oBAG5B,KAAA,CAAA,aAAA,CAACK,cAAD,CAAgB,QAAhB,EAAA;MACE,QAAQ,EAAE,IAAA,CAAK9C,KAAL,CAAWX,QAAX,IAAuB,IADnC;MAEE,KAAK,EAAE,IAAA,CAAKW,KAAL,CAAWsC;MAVtB,CADF;;;EAxDiBzC,KAAK,CAACC,SAAAA,CAAAA;AA0E3B,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;EACXgC,MAAM,CAACiB,SAAPjB,GAAmB;IACjBzC,QAAQ,EAAEa,SAAS,CAAC8C,IADH;IAEjBV,OAAO,EAAEpC,SAAS,CAACC,MAAVD,CAAiBE,UAFT;IAGjBqC,aAAa,EAAEvC,SAAS,CAACC;GAH3B2B;EAMAA,MAAM,CAACmB,SAAPnB,CAAiBoB,kBAAjBpB,GAAsC,UAASqB,SAAT,EAAoB;4CACxDvC,OAAO,CACLuC,SAAS,CAACb,OAAVa,KAAsB,IAAA,CAAKnD,KAAL,CAAWsC,OAD5B,EAEL,oCAFK,CAAP1B,GAAAA,KAAAA,CAAAA;GADFkB;;;ACpFF;;;;IAGMsB,YAAAA,GAAAA,aAAAA,UAAAA,gBAAAA,EAAAA;;;;;;;;UACJd,OAAAA,GAAUe,mBAAa,CAAC,KAAA,CAAKrD,KAAN,CAAA;;;;SAEvBa,MAAAA,GAAAA,SAAAA,MAAAA,CAAAA,EAAS;wBACA,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA;MAAQ,OAAO,EAAE,IAAA,CAAKyB,OAAtB;MAA+B,QAAQ,EAAE,IAAA,CAAKtC,KAAL,CAAWX;MAA3D;;;EAJuBQ,KAAK,CAACC,SAAAA,CAAAA;AAQjC,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;EACXsD,YAAY,CAACL,SAAbK,GAAyB;IACvBE,cAAc,EAAEpD,SAAS,CAACqD,KADH;IAEvBC,YAAY,EAAEtD,SAAS,CAACuD,MAFD;IAGvBC,mBAAmB,EAAExD,SAAS,CAACyD,IAHR;IAIvBC,SAAS,EAAE1D,SAAS,CAACuD,MAJE;IAKvBpE,QAAQ,EAAEa,SAAS,CAAC8C;GALtBI;EAQAA,YAAY,CAACH,SAAbG,CAAuBhC,iBAAvBgC,GAA2C,YAAW;4CACpDxC,OAAO,CACL,CAAC,IAAA,CAAKZ,KAAL,CAAWsC,OADP,EAEL,oEAAA,GACE,yEAHG,CAAP1B,GAAAA,KAAAA,CAAAA;GADFwC;;ICzBIS,SAAAA,GAAAA,aAAAA,UAAAA,gBAAAA,EAAAA;;;;;;SACJzC,iBAAAA,GAAAA,SAAAA,iBAAAA,CAAAA,EAAoB;QACd,IAAA,CAAKpB,KAAL,CAAW8D,OAAf,EAAwB,IAAA,CAAK9D,KAAL,CAAW8D,OAAX,CAAmBC,IAAnB,CAAwB,IAAxB,EAA8B,IAA9B,CAAA;;SAG1Bb,kBAAAA,GAAAA,SAAAA,kBAAAA,CAAmBC,SAAnBD,EAA8B;QACxB,IAAA,CAAKlD,KAAL,CAAWsB,QAAf,EAAyB,IAAA,CAAKtB,KAAL,CAAWsB,QAAX,CAAoByC,IAApB,CAAyB,IAAzB,EAA+B,IAA/B,EAAqCZ,SAArC,CAAA;;SAG3B5B,oBAAAA,GAAAA,SAAAA,oBAAAA,CAAAA,EAAuB;QACjB,IAAA,CAAKvB,KAAL,CAAWgE,SAAf,EAA0B,IAAA,CAAKhE,KAAL,CAAWgE,SAAX,CAAqBD,IAArB,CAA0B,IAA1B,EAAgC,IAAhC,CAAA;;SAG5BlD,MAAAA,GAAAA,SAAAA,MAAAA,CAAAA,EAAS;WACA,IAAP;;;EAdoBhB,KAAK,CAACC,SAAAA,CAAAA;;ACK9B;;;;AAGA,SAASmE,MAAT,CAAA,IAAA,EAA0C;MAAxBC,OAAwB,GAAA,IAAA,CAAxBA,OAAwB;qBAAfC,IAAe;IAAfA,IAAe,GAAA,SAAA,KAAA,KAAA,CAAA,GAAR,IAAQ,GAAA,SAAA;sBAEtC,KAAA,CAAA,aAAA,CAACvB,OAAD,CAAe,QAAf,EAAA,IAAA,EACG,UAAA,OAAO,EAAI;KACAvB,OAAV,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAAU,gDAAV,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;QAEI,CAAC8C,IAAD,IAAS9C,OAAO,CAACoB,aAArB,EAAoC,OAAO,IAAP;QAE9B2B,MAAM,GAAG/C,OAAO,CAACiB,OAARjB,CAAgBgD,KAA/B;wBAGE,KAAA,CAAA,aAAA,CAAC,SAAD,EAAA;MACE,OAAO,EAAE,SAAA,OAAA,CAAA,IAAI,EAAI;QACfC,IAAI,CAACC,OAALD,GAAeF,MAAM,CAACF,OAAD,CAArBI;OAFJ;MAIE,QAAQ,EAAE,SAAA,QAAA,CAACA,IAAD,EAAOnB,SAAP,EAAqB;YACzBA,SAAS,CAACe,OAAVf,KAAsBe,OAA1B,EAAmC;UACjCI,IAAI,CAACC,OAALD,CAAAA,CAAAA;UACAA,IAAI,CAACC,OAALD,GAAeF,MAAM,CAACF,OAAD,CAArBI;;OAPN;MAUE,SAAS,EAAE,SAAA,SAAA,CAAA,IAAI,EAAI;QACjBA,IAAI,CAACC,OAALD,CAAAA,CAAAA;OAXJ;MAaE,OAAO,EAAEJ;MAdb;GARJ,CADF;;AA+BF,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;MACLM,WAAW,GAAGtE,SAAS,CAACuE,SAAVvE,CAAoB,CAACA,SAAS,CAACyD,IAAX,EAAiBzD,SAAS,CAACwE,MAA3B,CAApBxE,CAApB;EAEA+D,MAAM,CAAClB,SAAPkB,GAAmB;IACjBE,IAAI,EAAEjE,SAAS,CAACyE,IADC;IAEjBT,OAAO,EAAEM,WAAW,CAACpE;GAFvB6D;;AC3CF,IAAMW,KAAK,GAAG,CAAA,CAAd;AACA,IAAMC,UAAU,GAAG,KAAnB;AACA,IAAIC,UAAU,GAAG,CAAjB;AAEA,SAASC,WAAT,CAAqB9C,IAArB,EAA2B;MACrB2C,KAAK,CAAC3C,IAAD,CAAT,EAAiB,OAAO2C,KAAK,CAAC3C,IAAD,CAAZ;MAEX+C,SAAS,GAAGC,YAAY,CAACC,OAAbD,CAAqBhD,IAArBgD,CAAlB;MAEIH,UAAU,GAAGD,UAAjB,EAA6B;IAC3BD,KAAK,CAAC3C,IAAD,CAAL2C,GAAcI,SAAdJ;IACAE,UAAU,EAAA;;SAGLE,SAAP;;;;;;AAMF,SAASG,YAAT,CAAsBlD,IAAtB,EAAkCE,MAAlC,EAA+C;MAAzBF,IAAyB,KAAA,KAAA,CAAA,EAAA;IAAzBA,IAAyB,GAAlB,GAAkB;;MAAbE,MAAa,KAAA,KAAA,CAAA,EAAA;IAAbA,MAAa,GAAJ,CAAA,CAAI;;SACtCF,IAAI,KAAK,GAATA,GAAeA,IAAfA,GAAsB,WAAW,CAACA,IAAD,CAAX,CAAkBE,MAAlB,EAA0B;IAAEiD,MAAM,EAAE;GAApC,CAA7B;;;ACdF;;;;AAGA,SAASC,QAAT,CAAA,IAAA,EAAuD;MAAnCC,aAAmC,GAAA,IAAA,CAAnCA,aAAmC;IAApBC,EAAoB,GAAA,IAAA,CAApBA,EAAoB;qBAAhB5G,IAAgB;IAAhBA,IAAgB,GAAA,SAAA,KAAA,KAAA,CAAA,GAAT,KAAS,GAAA,SAAA;sBAEnD,KAAA,CAAA,aAAA,CAACiE,OAAD,CAAe,QAAf,EAAA,IAAA,EACG,UAAA,OAAO,EAAI;KACAvB,OAAV,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAAU,kDAAV,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;QAEQiB,OAHE,GAGyBjB,OAHzB,CAGFiB,OAHE;MAGOG,aAHP,GAGyBpB,OAHzB,CAGOoB,aAHP;QAKJ2B,MAAM,GAAGzF,IAAI,GAAG2D,OAAO,CAAC3D,IAAX,GAAkB2D,OAAO,CAACkD,OAA7C;QACMnD,QAAQ,GAAGoD,cAAc,CAC7BH,aAAa,GACT,OAAOC,EAAP,KAAc,QAAd,GACEJ,YAAY,CAACI,EAAD,EAAKD,aAAa,CAACnD,MAAnB,CADd,GAAA,QAAA,CAAA,CAAA,CAAA,EAGOoD,EAHP,EAAA;MAIIvD,QAAQ,EAAEmD,YAAY,CAACI,EAAE,CAACvD,QAAJ,EAAcsD,aAAa,CAACnD,MAA5B;MALjB,GAOToD,EARyB,CAA/B,CANU,CAAA;;;QAmBN9C,aAAJ,EAAmB;MACjB2B,MAAM,CAAC/B,QAAD,CAAN+B;aACO,IAAP;;wBAIA,KAAA,CAAA,aAAA,CAAC,SAAD,EAAA;MACE,OAAO,EAAE,SAAA,OAAA,CAAA,EAAM;QACbA,MAAM,CAAC/B,QAAD,CAAN+B;OAFJ;MAIE,QAAQ,EAAE,SAAA,QAAA,CAACE,IAAD,EAAOnB,SAAP,EAAqB;YACvBuC,YAAY,GAAGD,cAAc,CAACtC,SAAS,CAACoC,EAAX,CAAnC;YAEE,CAACI,iBAAiB,CAACD,YAAD,EAAA,QAAA,CAAA,CAAA,CAAA,EACbrD,QADa,EAAA;UAEhBnE,GAAG,EAAEwH,YAAY,CAACxH;WAHtB,EAKE;UACAkG,MAAM,CAAC/B,QAAD,CAAN+B;;OAZN;MAeE,EAAE,EAAEmB;MAhBR;GAzBJ,CADF;;AAkDF,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;EACXF,QAAQ,CAACtC,SAATsC,GAAqB;IACnB1G,IAAI,EAAEuB,SAAS,CAACyE,IADG;IAEnBiB,IAAI,EAAE1F,SAAS,CAACwE,MAFG;IAGnBa,EAAE,EAAErF,SAAS,CAACuE,SAAVvE,CAAoB,CAACA,SAAS,CAACwE,MAAX,EAAmBxE,SAAS,CAACC,MAA7B,CAApBD,CAAAA,CAA0DE;GAHhEiF;;AC9DF,IAAMT,OAAK,GAAG,CAAA,CAAd;AACA,IAAMC,YAAU,GAAG,KAAnB;AACA,IAAIC,YAAU,GAAG,CAAjB;AAEA,SAASC,aAAT,CAAqB9C,IAArB,EAA2B4D,OAA3B,EAAoC;MAC5BC,QAAQ,GAAA,EAAA,GAAMD,OAAO,CAACE,GAAd,GAAoBF,OAAO,CAACG,MAA5B,GAAqCH,OAAO,CAACI,SAA3D;MACMC,SAAS,GAAGtB,OAAK,CAACkB,QAAD,CAALlB,KAAoBA,OAAK,CAACkB,QAAD,CAALlB,GAAkB,CAAA,CAAtCA,CAAlB;MAEIsB,SAAS,CAACjE,IAAD,CAAb,EAAqB,OAAOiE,SAAS,CAACjE,IAAD,CAAhB;MAEfkE,IAAI,GAAG,EAAb;MACMC,MAAM,GAAGnB,YAAY,CAAChD,IAAD,EAAOkE,IAAP,EAAaN,OAAb,CAA3B;MACMQ,MAAM,GAAG;IAAED,MAAM,EAANA,MAAF;IAAUD,IAAI,EAAJA;GAAzB;MAEIrB,YAAU,GAAGD,YAAjB,EAA6B;IAC3BqB,SAAS,CAACjE,IAAD,CAATiE,GAAkBG,MAAlBH;IACApB,YAAU,EAAA;;SAGLuB,MAAP;;;;;;AAMF,SAASC,SAAT,CAAmBtE,QAAnB,EAA6B6D,OAA7B,EAA2C;MAAdA,OAAc,KAAA,KAAA,CAAA,EAAA;IAAdA,OAAc,GAAJ,CAAA,CAAI;;MACrC,OAAOA,OAAP,KAAmB,QAAnB,IAA+BvG,KAAK,CAACC,OAAND,CAAcuG,OAAdvG,CAAnC,EAA2D;IACzDuG,OAAO,GAAG;MAAE5D,IAAI,EAAE4D;KAAlBA;;iBAGiEA,OAL1B;IAKjC5D,IALiC,GAAA,QAAA,CAKjCA,IALiC;8BAK3BsE,KAL2B;IAK3BA,KAL2B,GAAA,cAAA,KAAA,KAAA,CAAA,GAKnB,KALmB,GAAA,cAAA;+BAKZP,MALY;IAKZA,MALY,GAAA,eAAA,KAAA,KAAA,CAAA,GAKH,KALG,GAAA,eAAA;kCAKIC,SALJ;IAKIA,SALJ,GAAA,kBAAA,KAAA,KAAA,CAAA,GAKgB,KALhB,GAAA,kBAAA;MAOnCO,KAAK,GAAG,EAAA,CAAGC,MAAH,CAAUxE,IAAV,CAAd;SAEO,KAAK,CAACyE,MAAN,CAAa,UAACC,OAAD,EAAU1E,IAAV,EAAmB;QACjC,CAACA,IAAD,IAASA,IAAI,KAAK,EAAtB,EAA0B,OAAO,IAAP;QACtB0E,OAAJ,EAAa,OAAOA,OAAP;uBAEY5B,aAAW,CAAC9C,IAAD,EAAO;QACzC8D,GAAG,EAAEQ,KADoC;QAEzCP,MAAM,EAANA,MAFyC;QAGzCC,SAAS,EAATA;OAHkC,CAJC;MAI7BG,MAJ6B,GAAA,YAAA,CAI7BA,MAJ6B;MAIrBD,IAJqB,GAAA,YAAA,CAIrBA,IAJqB;QAS/BtD,KAAK,GAAGuD,MAAM,CAACQ,IAAPR,CAAYpE,QAAZoE,CAAd;QAEI,CAACvD,KAAL,EAAY,OAAO,IAAP;QAELX,GAb8B,GAaZW,KAbY,CAAA,CAAA,CAAA;MAatBgE,MAbsB,GAaZhE,KAbY,CAAA,KAAA,CAAA,CAAA,CAAA;QAc/BT,OAAO,GAAGJ,QAAQ,KAAKE,GAA7B;QAEIqE,KAAK,IAAI,CAACnE,OAAd,EAAuB,OAAO,IAAP;WAEhB;MACLH,IAAI,EAAJA,IADK;;MAELC,GAAG,EAAED,IAAI,KAAK,GAATA,IAAgBC,GAAG,KAAK,EAAxBD,GAA6B,GAA7BA,GAAmCC,GAFnC;;MAGLE,OAAO,EAAPA,OAHK;;MAILD,MAAM,EAAE,IAAI,CAACuE,MAAL,CAAY,UAACI,IAAD,EAAO5I,GAAP,EAAY6I,KAAZ,EAAsB;QACxCD,IAAI,CAAC5I,GAAG,CAAC0D,IAAL,CAAJkF,GAAiBD,MAAM,CAACE,KAAD,CAAvBD;eACOA,IAAP;OAFM,EAGL,CAAA,CAHK;KAJV;GAlBK,EA2BJ,IA3BI,CAAP;;AC3BF,SAASE,eAAT,CAAyB3H,QAAzB,EAAmC;SAC1BQ,KAAK,CAACoH,QAANpH,CAAeqH,KAAfrH,CAAqBR,QAArBQ,CAAAA,KAAmC,CAA1C;;AAGF,SAASsH,eAAT,CAAyB9H,QAAzB,EAAmCW,KAAnC,EAA0CiC,IAA1C,EAAgD;MACxC1D,KAAK,GAAGc,QAAQ,CAACW,KAAD,CAAtB;0CAEAY,OAAO,CACLrC,KAAK,KAAK4C,SADL,EAEL,2DAAA,IAAA,QAAA,IACWc,IAAI,GAAA,UAAA,GAAaA,IAAb,GAAA,IAAA,GAAuB,EADtC,CAAA,GAAA,aAAA,CAAA,GAEE,gDAJG,CAAPrB,GAAAA,KAAAA,CAAAA;SAOOrC,KAAK,IAAI,IAAhB;;;;;;IAMI6I,KAAAA,GAAAA,aAAAA,UAAAA,gBAAAA,EAAAA;;;;;;SACJvG,MAAAA,GAAAA,SAAAA,MAAAA,CAAAA,EAAS;;wBAEL,KAAA,CAAA,aAAA,CAAC+B,OAAD,CAAe,QAAf,EAAA,IAAA,EACG,UAAA,SAAO,EAAI;OACAvB,SAAV,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAAU,+CAAV,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;UAEMgB,QAAQ,GAAG,KAAI,CAACrC,KAAL,CAAWqC,QAAX,IAAuBhB,SAAO,CAACgB,QAAhD;UACMQ,KAAK,GAAG,KAAI,CAAC7C,KAAL,CAAWsF,aAAX,GACV,KAAI,CAACtF,KAAL,CAAWsF,aADD,CAAA;MAAA,EAEV,KAAI,CAACtF,KAAL,CAAWiC,IAAX,GACAqE,SAAS,CAACjE,QAAQ,CAACL,QAAV,EAAoB,KAAI,CAAChC,KAAzB,CADT,GAEAqB,SAAO,CAACwB,KAJZ;UAMM7C,KAAK,GAAA,QAAA,CAAA,CAAA,CAAA,EAAQqB,SAAR,EAAA;QAAiBgB,QAAQ,EAARA,QAAjB;QAA2BQ,KAAK,EAALA;QAAtC;wBAEsC,KAAI,CAAC7C,KAZjC;QAYJX,QAZI,GAAA,WAAA,CAYJA,QAZI;QAYMgI,SAZN,GAAA,WAAA,CAYMA,SAZN;QAYiBxG,MAZjB,GAAA,WAAA,CAYiBA,MAZjB,CAAA,CAAA;;;UAgBNvB,KAAK,CAACC,OAAND,CAAcD,QAAdC,CAAAA,IAA2B0H,eAAe,CAAC3H,QAAD,CAA9C,EAA0D;QACxDA,QAAQ,GAAG,IAAXA;;0BAIA,KAAA,CAAA,aAAA,CAACuD,OAAD,CAAe,QAAf,EAAA;QAAwB,KAAK,EAAE5C;SAC5BA,KAAK,CAAC6C,KAAN7C,GACGX,QAAQ,GACN,OAAOA,QAAP,KAAoB,UAApB,GACE,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GACE8H,eAAe,CAAC9H,QAAD,EAAWW,KAAX,EAAkB,KAAI,CAACA,KAAL,CAAWiC,IAA7B,CADjB,GAEE5C,QAAQ,CAACW,KAAD,CAHZ,GAIEX,QALI,GAMNgI,SAAS,GAAA,aACTxH,KAAK,CAACyH,aAANzH,CAAoBwH,SAApBxH,EAA+BG,KAA/BH,CADS,GAETgB,MAAM,GACNA,MAAM,CAACb,KAAD,CADA,GAEN,IAXLA,GAYG,OAAOX,QAAP,KAAoB,UAApB,GACA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GACE8H,eAAe,CAAC9H,QAAD,EAAWW,KAAX,EAAkB,KAAI,CAACA,KAAL,CAAWiC,IAA7B,CADjB,GAEE5C,QAAQ,CAACW,KAAD,CAHV,GAIA,IAjBN,CADF;KArBJ,CADF;;;EAFgBH,KAAK,CAACC,SAAAA,CAAAA;AAmD1B,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;EACXsH,KAAK,CAACrE,SAANqE,GAAkB;IAChB/H,QAAQ,EAAEa,SAAS,CAACuE,SAAVvE,CAAoB,CAACA,SAAS,CAACyD,IAAX,EAAiBzD,SAAS,CAAC8C,IAA3B,CAApB9C,CADM;IAEhBmH,SAAS,EAAE,SAAA,SAAA,CAACrH,KAAD,EAAQuH,QAAR,EAAqB;UAC1BvH,KAAK,CAACuH,QAAD,CAALvH,IAAmB,CAACwH,kBAAkB,CAACxH,KAAK,CAACuH,QAAD,CAAN,CAA1C,EAA6D;eACpD,IAAIE,KAAJ,CAAA,uFAAA,CAAP;;KAJY;IAShBlB,KAAK,EAAErG,SAAS,CAACyE,IATD;IAUhBtC,QAAQ,EAAEnC,SAAS,CAACC,MAVJ;IAWhB8B,IAAI,EAAE/B,SAAS,CAACuE,SAAVvE,CAAoB,CACxBA,SAAS,CAACwE,MADc,EAExBxE,SAAS,CAACwH,OAAVxH,CAAkBA,SAAS,CAACwE,MAA5BxE,CAFwB,CAApBA,CAXU;IAehBW,MAAM,EAAEX,SAAS,CAACyD,IAfF;IAgBhBsC,SAAS,EAAE/F,SAAS,CAACyE,IAhBL;IAiBhBqB,MAAM,EAAE9F,SAAS,CAACyE;GAjBpByC;EAoBAA,KAAK,CAACnE,SAANmE,CAAgBhG,iBAAhBgG,GAAoC,YAAW;4CAC7CxG,OAAO,CACL,EACE,IAAA,CAAKZ,KAAL,CAAWX,QAAX,IACA,CAAC2H,eAAe,CAAC,IAAA,CAAKhH,KAAL,CAAWX,QAAZ,CADhB,IAEA,IAAA,CAAKW,KAAL,CAAWqH,SAHb,CADK,EAML,gHANK,CAAPzG,GAAAA,KAAAA,CAAAA;4CASAA,OAAO,CACL,EACE,IAAA,CAAKZ,KAAL,CAAWX,QAAX,IACA,CAAC2H,eAAe,CAAC,IAAA,CAAKhH,KAAL,CAAWX,QAAZ,CADhB,IAEA,IAAA,CAAKW,KAAL,CAAWa,MAHb,CADK,EAML,0GANK,CAAPD,GAAAA,KAAAA,CAAAA;4CASAA,OAAO,CACL,EAAE,IAAA,CAAKZ,KAAL,CAAWqH,SAAX,IAAwB,IAAA,CAAKrH,KAAL,CAAWa,MAArC,CADK,EAEL,2GAFK,CAAPD,GAAAA,KAAAA,CAAAA;GAnBFwG;EAyBAA,KAAK,CAACnE,SAANmE,CAAgBlE,kBAAhBkE,GAAqC,UAASjE,SAAT,EAAoB;4CACvDvC,OAAO,CACL,EAAE,IAAA,CAAKZ,KAAL,CAAWqC,QAAX,IAAuB,CAACc,SAAS,CAACd,QAApC,CADK,EAEL,yKAFK,CAAPzB,GAAAA,KAAAA,CAAAA;4CAKAA,OAAO,CACL,EAAE,CAAC,IAAA,CAAKZ,KAAL,CAAWqC,QAAZ,IAAwBc,SAAS,CAACd,QAApC,CADK,EAEL,qKAFK,CAAPzB,GAAAA,KAAAA,CAAAA;GANFwG;;ACtHF,SAASO,eAAT,CAAyB1F,IAAzB,EAA+B;SACtBA,IAAI,CAAC2F,MAAL3F,CAAY,CAAZA,CAAAA,KAAmB,GAAnBA,GAAyBA,IAAzBA,GAAgC,GAAA,GAAMA,IAA7C;;AAGF,SAAS4F,WAAT,CAAqBC,QAArB,EAA+BzF,QAA/B,EAAyC;MACnC,CAACyF,QAAL,EAAe,OAAOzF,QAAP;sBAGVA,QADL,EAAA;IAEEL,QAAQ,EAAE2F,eAAe,CAACG,QAAD,CAAfH,GAA4BtF,QAAQ,CAACL;;;AAInD,SAAS+F,aAAT,CAAuBD,QAAvB,EAAiCzF,QAAjC,EAA2C;MACrC,CAACyF,QAAL,EAAe,OAAOzF,QAAP;MAET2F,IAAI,GAAGL,eAAe,CAACG,QAAD,CAA5B;MAEIzF,QAAQ,CAACL,QAATK,CAAkB4F,OAAlB5F,CAA0B2F,IAA1B3F,CAAAA,KAAoC,CAAxC,EAA2C,OAAOA,QAAP;sBAGtCA,QADL,EAAA;IAEEL,QAAQ,EAAEK,QAAQ,CAACL,QAATK,CAAkB6F,MAAlB7F,CAAyB2F,IAAI,CAACG,MAA9B9F;;;AAId,SAAS+F,SAAT,CAAmB/F,QAAnB,EAA6B;SACpB,OAAOA,QAAP,KAAoB,QAApB,GAA+BA,QAA/B,GAA0CgG,UAAU,CAAChG,QAAD,CAA3D;;AAGF,SAASiG,aAAT,CAAuBC,UAAvB,EAAmC;SAC1B,YAAM;4CACXC,SAAS,CAAA,KAAA,EAAQ,mCAAR,EAA6CD,UAA7C,CAATC,GAAAA,SAAS,CAAA,KAAA,CAATA;GADF;;AAKF,SAASC,IAAT,CAAA,EAAgB,CAAA;;;;;;;;IAQVC,YAAAA,GAAAA,aAAAA,UAAAA,gBAAAA,EAAAA;;;;;;;;UAQJG,UAAAA,GAAa,UAAA,QAAQ,EAAA;aAAI,KAAA,CAAKF,UAAL,CAAgBtG,QAAhB,EAA0B,MAA1B,CAAJ;;UACrByG,aAAAA,GAAgB,UAAA,QAAQ,EAAA;aAAI,KAAA,CAAKH,UAAL,CAAgBtG,QAAhB,EAA0B,SAA1B,CAAJ;;UACxB0G,YAAAA,GAAe,YAAA;aAAMN,IAAN;;UACfO,WAAAA,GAAc,YAAA;aAAMP,IAAN;;;;;SAVdE,UAAAA,GAAAA,SAAAA,UAAAA,CAAWtG,QAAXsG,EAAqBC,MAArBD,EAA6B;sBACa,IAAA,CAAK3I,KADlB;yCACnB8H,QADmB;MACnBA,QADmB,GAAA,oBAAA,KAAA,KAAA,CAAA,GACR,EADQ,GAAA,oBAAA;wCACJzG,OADI;MACJA,OADI,GAAA,mBAAA,KAAA,KAAA,CAAA,GACM,CAAA,CADN,GAAA,mBAAA;IAE3BA,OAAO,CAACuH,MAARvH,GAAiBuH,MAAjBvH;IACAA,OAAO,CAACgB,QAARhB,GAAmBwG,WAAW,CAACC,QAAD,EAAWrC,cAAc,CAACpD,QAAD,CAAzB,CAA9BhB;IACAA,OAAO,CAACa,GAARb,GAAc+G,SAAS,CAAC/G,OAAO,CAACgB,QAAT,CAAvBhB;;SAQFR,MAAAA,GAAAA,SAAAA,MAAAA,CAAAA,EAAS;uBAC0D,IAAA,CAAKb,KAD/D;2CACC8H,QADD;MACCA,QADD,GAAA,qBAAA,KAAA,KAAA,CAAA,GACY,EADZ,GAAA,qBAAA;0CACgBzG,OADhB;MACgBA,OADhB,GAAA,oBAAA,KAAA,KAAA,CAAA,GAC0B,CAAA,CAD1B,GAAA,oBAAA;2CAC8BgB,QAD9B;MAC8BA,QAD9B,GAAA,qBAAA,KAAA,KAAA,CAAA,GACyC,GADzC,GAAA,qBAAA;MACiD4G,IADjD,GAAA,6BAAA,CAAA,YAAA,EAAA,CAAA,UAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA;QAGD3G,OAAO,GAAG;MACd4G,UAAU,EAAE,SAAA,UAAA,CAAA,IAAI,EAAA;eAAIvB,eAAe,CAACG,QAAQ,GAAGM,SAAS,CAACnG,IAAD,CAArB,CAAnB;OADF;MAEd2G,MAAM,EAAE,KAFM;MAGdvG,QAAQ,EAAE0F,aAAa,CAACD,QAAD,EAAWrC,cAAc,CAACpD,QAAD,CAAzB,CAHT;MAId1D,IAAI,EAAE,IAAA,CAAKkK,UAJG;MAKdrD,OAAO,EAAE,IAAA,CAAKsD,aALA;MAMdK,EAAE,EAAEb,aAAa,CAAC,IAAD,CANH;MAOdc,MAAM,EAAEd,aAAa,CAAC,QAAD,CAPP;MAQde,SAAS,EAAEf,aAAa,CAAC,WAAD,CARV;MASd3F,MAAM,EAAE,IAAA,CAAKoG,YATC;MAUd1E,KAAK,EAAE,IAAA,CAAK2E;KAVd;wBAaO,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA,QAAA,CAAA,CAAA,CAAA,EAAYC,IAAZ,EAAA;MAAkB,OAAO,EAAE3G,OAA3B;MAAoC,aAAa,EAAEjB;OAA1D;;;EA7BuBxB,KAAK,CAACC,SAAAA,CAAAA;AAiCjC,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;EACX4I,YAAY,CAAC3F,SAAb2F,GAAyB;IACvBZ,QAAQ,EAAE5H,SAAS,CAACwE,MADG;IAEvBrD,OAAO,EAAEnB,SAAS,CAACC,MAFI;IAGvBkC,QAAQ,EAAEnC,SAAS,CAACuE,SAAVvE,CAAoB,CAACA,SAAS,CAACwE,MAAX,EAAmBxE,SAAS,CAACC,MAA7B,CAApBD;GAHZwI;EAMAA,YAAY,CAACzF,SAAbyF,CAAuBtH,iBAAvBsH,GAA2C,YAAW;4CACpD9H,OAAO,CACL,CAAC,IAAA,CAAKZ,KAAL,CAAWsC,OADP,EAEL,oEAAA,GACE,yEAHG,CAAP1B,GAAAA,KAAAA,CAAAA;GADF8H;;;ACpFF;;;;IAGMY,MAAAA,GAAAA,aAAAA,UAAAA,gBAAAA,EAAAA;;;;;;SACJzI,MAAAA,GAAAA,SAAAA,MAAAA,CAAAA,EAAS;;wBAEL,KAAA,CAAA,aAAA,CAAC+B,OAAD,CAAe,QAAf,EAAA,IAAA,EACG,UAAA,OAAO,EAAI;OACAvB,OAAV,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAAU,gDAAV,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;UAEMgB,QAAQ,GAAG,KAAI,CAACrC,KAAL,CAAWqC,QAAX,IAAuBhB,OAAO,CAACgB,QAAhD;UAEIkH,OAAJ,EAAa1G,KAAb,CALU,CAAA;;;;;MAWVhD,KAAK,CAACoH,QAANpH,CAAeV,OAAfU,CAAuB,KAAI,CAACG,KAAL,CAAWX,QAAlCQ,EAA4C,UAAA,KAAK,EAAI;YAC/CgD,KAAK,IAAI,IAATA,IAAAA,aAAiBhD,KAAK,CAAC2J,cAAN3J,CAAqB4J,KAArB5J,CAArB,EAAkD;UAChD0J,OAAO,GAAGE,KAAVF;cAEMtH,IAAI,GAAGwH,KAAK,CAACzJ,KAANyJ,CAAYxH,IAAZwH,IAAoBA,KAAK,CAACzJ,KAANyJ,CAAY7D,IAA7C;UAEA/C,KAAK,GAAGZ,IAAI,GACRqE,SAAS,CAACjE,QAAQ,CAACL,QAAV,EAAA,QAAA,CAAA,CAAA,CAAA,EAAyByH,KAAK,CAACzJ,KAA/B,EAAA;YAAsCiC,IAAI,EAAJA;aADvC,GAERZ,OAAO,CAACwB,KAFZA;;OANJhD,CAAAA;aAYOgD,KAAK,GAAA,aACR,KAAK,CAAC6G,YAAN,CAAmBH,OAAnB,EAA4B;QAAElH,QAAQ,EAARA,QAAF;QAAYiD,aAAa,EAAEzC;OAAvD,CADQ,GAER,IAFJ;KAxBJ,CADF;;;EAFiBhD,KAAK,CAACC,SAAAA,CAAAA;AAoC3B,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;EACXwJ,MAAM,CAACvG,SAAPuG,GAAmB;IACjBjK,QAAQ,EAAEa,SAAS,CAAC8C,IADH;IAEjBX,QAAQ,EAAEnC,SAAS,CAACC;GAFtBmJ;EAKAA,MAAM,CAACrG,SAAPqG,CAAiBpG,kBAAjBoG,GAAsC,UAASnG,SAAT,EAAoB;4CACxDvC,OAAO,CACL,EAAE,IAAA,CAAKZ,KAAL,CAAWqC,QAAX,IAAuB,CAACc,SAAS,CAACd,QAApC,CADK,EAEL,0KAFK,CAAPzB,GAAAA,KAAAA,CAAAA;4CAKAA,OAAO,CACL,EAAE,CAAC,IAAA,CAAKZ,KAAL,CAAWqC,QAAZ,IAAwBc,SAAS,CAACd,QAApC,CADK,EAEL,sKAFK,CAAPzB,GAAAA,KAAAA,CAAAA;GANF0I;;;AC9CF;;;;AAGA,SAASK,UAAT,CAAoB7J,SAApB,EAA+B;MACvB6B,WAAW,GAAA,aAAA,IAAiB7B,SAAS,CAAC6B,WAAV7B,IAAyBA,SAAS,CAAC8B,IAApD,CAAA,GAAA,GAAjB;MACMgI,CAAC,GAAG,SAAJA,CAAI,CAAA,KAAK,EAAI;QACTC,mBADS,GACkC7J,KADlC,CACT6J,mBADS;MACeC,cADf,GAAA,6BAAA,CACkC9J,KADlC,EAAA,CAAA,qBAAA,CAAA,CAAA;wBAIf,KAAA,CAAA,aAAA,CAAC4C,OAAD,CAAe,QAAf,EAAA,IAAA,EACG,UAAA,OAAO,EAAI;OAERvB,OADF,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAAA,sBAAA,GAEgBM,WAFhB,GAAA,wBAAA,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;0BAKE,KAAA,CAAA,aAAA,CAAC,SAAD,EAAA,QAAA,CAAA,CAAA,CAAA,EACMmI,cADN,EAEMzI,OAFN,EAAA;QAGE,GAAG,EAAEwI;SAJT;KANJ,CADF;GAHF;EAsBAD,CAAC,CAACjI,WAAFiI,GAAgBjI,WAAhBiI;EACAA,CAAC,CAACG,gBAAFH,GAAqB9J,SAArB8J;6CAEa;IACXA,CAAC,CAAC7G,SAAF6G,GAAc;MACZC,mBAAmB,EAAE3J,SAAS,CAACuE,SAAVvE,CAAoB,CACvCA,SAAS,CAACwE,MAD6B,EAEvCxE,SAAS,CAACyD,IAF6B,EAGvCzD,SAAS,CAACC,MAH6B,CAApBD;KADvB0J;;SASKI,YAAY,CAACJ,CAAD,EAAI9J,SAAJ,CAAnB;;ACxCF,IAAMmK,UAAU,GAAGpK,KAAK,CAACoK,UAAzB;AAEO,SAASC,UAAT,CAAA,EAAsB;6CACd;MAET,OAAOD,UAAP,KAAsB,UADxB,CAAA,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAEP,yDAFO,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;;SAMKA,UAAU,CAACnH,cAAD,CAAjB;;AAGK,SAASqH,WAAT,CAAA,EAAuB;6CACf;MAET,OAAOF,UAAP,KAAsB,UADxB,CAAA,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAEP,0DAFO,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;;SAMKA,UAAU,CAACrH,OAAD,CAAVqH,CAA0B5H,QAAjC;;AAGK,SAAS+H,SAAT,CAAA,EAAqB;6CACb;MAET,OAAOH,UAAP,KAAsB,UADxB,CAAA,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAEP,wDAFO,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;;MAMIpH,KAAK,GAAGoH,UAAU,CAACrH,OAAD,CAAVqH,CAA0BpH,KAAxC;SACOA,KAAK,GAAGA,KAAK,CAACV,MAAT,GAAkB,CAAA,CAA9B;;AAGK,SAASkI,aAAT,CAAuBpI,IAAvB,EAA6B;6CACrB;MAET,OAAOgI,UAAP,KAAsB,UADxB,CAAA,GAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,SAAS,CAAA,KAAA,EAEP,4DAFO,CAAT,GAAA,SAAS,CAAA,KAAA,CAAT,GAAA,KAAA,CAAA;;MAMI5H,QAAQ,GAAG8H,WAAW,CAAA,CAA5B;MACMtH,KAAK,GAAGoH,UAAU,CAACrH,OAAD,CAAVqH,CAA0BpH,KAAxC;SACOZ,IAAI,GAAGqE,SAAS,CAACjE,QAAQ,CAACL,QAAV,EAAoBC,IAApB,CAAZ,GAAwCY,KAAnD;;ACrDF,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,EAAa;MACP,OAAO9E,MAAP,KAAkB,WAAtB,EAAmC;QAC3BC,QAAM,GAAGD,MAAf;QACMG,GAAG,GAAG,wBAAZ;QACMoM,UAAU,GAAG;MAAEC,GAAG,EAAE,UAAP;MAAmBC,GAAG,EAAE,YAAxB;MAAsCC,GAAG,EAAE;KAA9D;QAEIzM,QAAM,CAACE,GAAD,CAANF,IAAeA,QAAM,CAACE,GAAD,CAANF,KAAgByC,KAAnC,EAA6D;UACrDiK,gBAAgB,GAAGJ,UAAU,CAACtM,QAAM,CAACE,GAAD,CAAP,CAAnC;UACMyM,kBAAkB,GAAGL,UAAU,CAAC7J,KAAD,CAArC,CAF2D,CAAA;;;YAMrD,IAAIgH,KAAJ,CACJ,sBAAA,GAAuBkD,kBAAvB,GAAA,yBAAA,IAAA,wCAAA,GAC2CD,gBAD3C,GAAA,GAAA,CAAA,GAAA,oCADI,CAAN;;IAOF1M,QAAM,CAACE,GAAD,CAANF,GAAcyC,KAAdzC", "sourcesContent": ["// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nconst MAX_SIGNED_31_BIT_INT = **********;\n\nconst commonjsGlobal =\n  typeof globalThis !== \"undefined\" // 'global proper'\n    ? // eslint-disable-next-line no-undef\n      globalThis\n    : typeof window !== \"undefined\"\n    ? window // Browser\n    : typeof global !== \"undefined\"\n    ? global // node.js\n    : {};\n\nfunction getUniqueId() {\n  let key = \"__global_unique_id__\";\n  return (commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1);\n}\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    // eslint-disable-next-line no-self-compare\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  let handlers = [];\n  return {\n    on(handler) {\n      handlers.push(handler);\n    },\n\n    off(handler) {\n      handlers = handlers.filter(h => h !== handler);\n    },\n\n    get() {\n      return value;\n    },\n\n    set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(handler => handler(value, changedBits));\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nexport default function createReactContext(defaultValue, calculateChangedBits) {\n  const contextProp = \"__create-react-context-\" + getUniqueId() + \"__\";\n\n  class Provider extends React.Component {\n    emitter = createEventEmitter(this.props.value);\n\n    static childContextTypes = {\n      [contextProp]: PropTypes.object.isRequired\n    };\n\n    getChildContext() {\n      return {\n        [contextProp]: this.emitter\n      };\n    }\n\n    componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        let oldValue = this.props.value;\n        let newValue = nextProps.value;\n        let changedBits;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits =\n            typeof calculateChangedBits === \"function\"\n              ? calculateChangedBits(oldValue, newValue)\n              : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== \"production\") {\n            warning(\n              (changedBits & MAX_SIGNED_31_BIT_INT) === changedBits,\n              \"calculateChangedBits: Expected the return value to be a \" +\n                \"31-bit integer. Instead received: \" +\n                changedBits\n            );\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    }\n\n    render() {\n      return this.props.children;\n    }\n  }\n\n  class Consumer extends React.Component {\n    static contextTypes = {\n      [contextProp]: PropTypes.object\n    };\n\n    observedBits;\n\n    state = {\n      value: this.getValue()\n    };\n\n    componentWillReceiveProps(nextProps) {\n      let { observedBits } = nextProps;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      let { observedBits } = this.props;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    }\n\n    getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    }\n\n    onUpdate = (newValue, changedBits) => {\n      const observedBits = this.observedBits | 0;\n      if ((observedBits & changedBits) !== 0) {\n        this.setState({ value: this.getValue() });\n      }\n    };\n\n    render() {\n      return onlyChild(this.props.children)(this.state.value);\n    }\n  }\n\n  return {\n    Provider,\n    Consumer\n  };\n}\n", "// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport createReactContext from \"./miniCreateReactContext\";\n\nexport default React.createContext || createReactContext;\n", "// TODO: Replace with React.createContext once we can assume React 16+\nimport createContext from \"./createContext\";\n\nconst createNamedContext = name => {\n  const context = createContext();\n  context.displayName = name;\n\n  return context;\n};\n\nexport default createNamedContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst historyContext = /*#__PURE__*/ createNamedContext(\"Router-History\");\nexport default historyContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst context = /*#__PURE__*/ createNamedContext(\"Router\");\nexport default context;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nimport HistoryContext from \"./HistoryContext.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for putting history on context.\n */\nclass Router extends React.Component {\n  static computeRootMatch(pathname) {\n    return { path: \"/\", url: \"/\", params: {}, isExact: pathname === \"/\" };\n  }\n\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      location: props.history.location\n    };\n\n    // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n    this._isMounted = false;\n    this._pendingLocation = null;\n\n    if (!props.staticContext) {\n      this.unlisten = props.history.listen(location => {\n        this._pendingLocation = location;\n      });\n    }\n  }\n\n  componentDidMount() {\n    this._isMounted = true;\n\n    if (this.unlisten) {\n      // Any pre-mount location changes have been captured at\n      // this point, so unregister the listener.\n      this.unlisten();\n    }\n    if (!this.props.staticContext) {\n      this.unlisten = this.props.history.listen(location => {\n        if (this._isMounted) {\n          this.setState({ location });\n        }\n      });\n    }\n    if (this._pendingLocation) {\n      this.setState({ location: this._pendingLocation });\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  }\n\n  render() {\n    return (\n      <RouterContext.Provider\n        value={{\n          history: this.props.history,\n          location: this.state.location,\n          match: Router.computeRootMatch(this.state.location.pathname),\n          staticContext: this.props.staticContext\n        }}\n      >\n        <HistoryContext.Provider\n          children={this.props.children || null}\n          value={this.props.history}\n        />\n      </RouterContext.Provider>\n    );\n  }\n}\n\nif (__DEV__) {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n\n  Router.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      prevProps.history === this.props.history,\n      \"You cannot change <Router history>\"\n    );\n  };\n}\n\nexport default Router;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createMemoryHistory as createHistory } from \"history\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\nclass MemoryRouter extends React.Component {\n  history = createHistory(this.props);\n\n  render() {\n    return <Router history={this.history} children={this.props.children} />;\n  }\n}\n\nif (__DEV__) {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n\n  MemoryRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<MemoryRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\"\n    );\n  };\n}\n\nexport default MemoryRouter;\n", "import React from \"react\";\n\nclass Lifecycle extends React.Component {\n  componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  }\n\n  componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  }\n\n  render() {\n    return null;\n  }\n}\n\nexport default Lifecycle;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\nfunction Prompt({ message, when = true }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Prompt> outside a <Router>\");\n\n        if (!when || context.staticContext) return null;\n\n        const method = context.history.block;\n\n        return (\n          <Lifecycle\n            onMount={self => {\n              self.release = method(message);\n            }}\n            onUpdate={(self, prevProps) => {\n              if (prevProps.message !== message) {\n                self.release();\n                self.release = method(message);\n              }\n            }}\n            onUnmount={self => {\n              self.release();\n            }}\n            message={message}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  const messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\n\nexport default Prompt;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n\n  const generator = pathToRegexp.compile(path);\n\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n\n  return generator;\n}\n\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\nfunction generatePath(path = \"/\", params = {}) {\n  return path === \"/\" ? path : compilePath(path)(params, { pretty: true });\n}\n\nexport default generatePath;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, locationsAreEqual } from \"history\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\nimport generatePath from \"./generatePath.js\";\n\n/**\n * The public API for navigating programmatically with a component.\n */\nfunction Redirect({ computedMatch, to, push = false }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Redirect> outside a <Router>\");\n\n        const { history, staticContext } = context;\n\n        const method = push ? history.push : history.replace;\n        const location = createLocation(\n          computedMatch\n            ? typeof to === \"string\"\n              ? generatePath(to, computedMatch.params)\n              : {\n                  ...to,\n                  pathname: generatePath(to.pathname, computedMatch.params)\n                }\n            : to\n        );\n\n        // When rendering in a static context,\n        // set the new location immediately.\n        if (staticContext) {\n          method(location);\n          return null;\n        }\n\n        return (\n          <Lifecycle\n            onMount={() => {\n              method(location);\n            }}\n            onUpdate={(self, prevProps) => {\n              const prevLocation = createLocation(prevProps.to);\n              if (\n                !locationsAreEqual(prevLocation, {\n                  ...location,\n                  key: prevLocation.key\n                })\n              ) {\n                method(location);\n              }\n            }}\n            to={to}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\n\nexport default Redirect;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path, options) {\n  const cacheKey = `${options.end}${options.strict}${options.sensitive}`;\n  const pathCache = cache[cacheKey] || (cache[cacheKey] = {});\n\n  if (pathCache[path]) return pathCache[path];\n\n  const keys = [];\n  const regexp = pathToRegexp(path, keys, options);\n  const result = { regexp, keys };\n\n  if (cacheCount < cacheLimit) {\n    pathCache[path] = result;\n    cacheCount++;\n  }\n\n  return result;\n}\n\n/**\n * Public API for matching a URL pathname to a path.\n */\nfunction matchPath(pathname, options = {}) {\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = { path: options };\n  }\n\n  const { path, exact = false, strict = false, sensitive = false } = options;\n\n  const paths = [].concat(path);\n\n  return paths.reduce((matched, path) => {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n\n    const { regexp, keys } = compilePath(path, {\n      end: exact,\n      strict,\n      sensitive\n    });\n    const match = regexp.exec(pathname);\n\n    if (!match) return null;\n\n    const [url, ...values] = match;\n    const isExact = pathname === url;\n\n    if (exact && !isExact) return null;\n\n    return {\n      path, // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url, // the matched portion of the URL\n      isExact, // whether or not we matched exactly\n      params: keys.reduce((memo, key, index) => {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\n\nexport default matchPath;\n", "import React from \"react\";\nimport { isValidElementType } from \"react-is\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\n\nfunction evalChildrenDev(children, props, path) {\n  const value = children(props);\n\n  warning(\n    value !== undefined,\n    \"You returned `undefined` from the `children` function of \" +\n      `<Route${path ? ` path=\"${path}\"` : \"\"}>, but you ` +\n      \"should have returned a React element or `null`\"\n  );\n\n  return value || null;\n}\n\n/**\n * The public API for matching a single path and rendering.\n */\nclass Route extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Route> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n          const match = this.props.computedMatch\n            ? this.props.computedMatch // <Switch> already computed the match for us\n            : this.props.path\n            ? matchPath(location.pathname, this.props)\n            : context.match;\n\n          const props = { ...context, location, match };\n\n          let { children, component, render } = this.props;\n\n          // Preact uses an empty array as children by\n          // default, so use null if that's the case.\n          if (Array.isArray(children) && isEmptyChildren(children)) {\n            children = null;\n          }\n\n          return (\n            <RouterContext.Provider value={props}>\n              {props.match\n                ? children\n                  ? typeof children === \"function\"\n                    ? __DEV__\n                      ? evalChildrenDev(children, props, this.props.path)\n                      : children(props)\n                    : children\n                  : component\n                  ? React.createElement(component, props)\n                  : render\n                  ? render(props)\n                  : null\n                : typeof children === \"function\"\n                ? __DEV__\n                  ? evalChildrenDev(children, props, this.props.path)\n                  : children(props)\n                : null}\n            </RouterContext.Provider>\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: (props, propName) => {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\n          `Invalid prop 'component' supplied to 'Route': the prop is not a valid React component`\n        );\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.arrayOf(PropTypes.string)\n    ]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n\n  Route.prototype.componentDidMount = function() {\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.component\n      ),\n      \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\"\n    );\n\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.render\n      ),\n      \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\"\n    );\n\n    warning(\n      !(this.props.component && this.props.render),\n      \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\"\n    );\n  };\n\n  Route.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Route;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, createPath } from \"history\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\n\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n\n  return {\n    ...location,\n    pathname: addLeadingSlash(basename) + location.pathname\n  };\n}\n\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n\n  const base = addLeadingSlash(basename);\n\n  if (location.pathname.indexOf(base) !== 0) return location;\n\n  return {\n    ...location,\n    pathname: location.pathname.substr(base.length)\n  };\n}\n\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\n\nfunction staticHandler(methodName) {\n  return () => {\n    invariant(false, \"You cannot %s with <StaticRouter>\", methodName);\n  };\n}\n\nfunction noop() {}\n\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\nclass StaticRouter extends React.Component {\n  navigateTo(location, action) {\n    const { basename = \"\", context = {} } = this.props;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  }\n\n  handlePush = location => this.navigateTo(location, \"PUSH\");\n  handleReplace = location => this.navigateTo(location, \"REPLACE\");\n  handleListen = () => noop;\n  handleBlock = () => noop;\n\n  render() {\n    const { basename = \"\", context = {}, location = \"/\", ...rest } = this.props;\n\n    const history = {\n      createHref: path => addLeadingSlash(basename + createURL(path)),\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n\n    return <Router {...rest} history={history} staticContext={context} />;\n  }\n}\n\nif (__DEV__) {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n\n  StaticRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<StaticRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { StaticRouter as Router }`.\"\n    );\n  };\n}\n\nexport default StaticRouter;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\nclass Switch extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Switch> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n\n          let element, match;\n\n          // We use React.Children.forEach instead of React.Children.toArray().find()\n          // here because toArray adds keys to all child elements and we do not want\n          // to trigger an unmount/remount for two <Route>s that render the same\n          // component at different URLs.\n          React.Children.forEach(this.props.children, child => {\n            if (match == null && React.isValidElement(child)) {\n              element = child;\n\n              const path = child.props.path || child.props.from;\n\n              match = path\n                ? matchPath(location.pathname, { ...child.props, path })\n                : context.match;\n            }\n          });\n\n          return match\n            ? React.cloneElement(element, { location, computedMatch: match })\n            : null;\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n\n  Switch.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Switch;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport hoistStatics from \"hoist-non-react-statics\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * A public higher-order component to access the imperative API\n */\nfunction withRouter(Component) {\n  const displayName = `withRouter(${Component.displayName || Component.name})`;\n  const C = props => {\n    const { wrappedComponentRef, ...remainingProps } = props;\n\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(\n            context,\n            `You should not use <${displayName} /> outside a <Router>`\n          );\n          return (\n            <Component\n              {...remainingProps}\n              {...context}\n              ref={wrappedComponentRef}\n            />\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  };\n\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n\n  if (__DEV__) {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([\n        PropTypes.string,\n        PropTypes.func,\n        PropTypes.object\n      ])\n    };\n  }\n\n  return hoistStatics(C, Component);\n}\n\nexport default withRouter;\n", "import React from \"react\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport HistoryContext from \"./HistoryContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nconst useContext = React.useContext;\n\nexport function useHistory() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useHistory()\"\n    );\n  }\n\n  return useContext(HistoryContext);\n}\n\nexport function useLocation() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useLocation()\"\n    );\n  }\n\n  return useContext(RouterContext).location;\n}\n\nexport function useParams() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useParams()\"\n    );\n  }\n\n  const match = useContext(RouterContext).match;\n  return match ? match.params : {};\n}\n\nexport function useRouteMatch(path) {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useRouteMatch()\"\n    );\n  }\n\n  const location = useLocation();\n  const match = useContext(RouterContext).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\n", "if (__DEV__) {\n  if (typeof window !== \"undefined\") {\n    const global = window;\n    const key = \"__react_router_build__\";\n    const buildNames = { cjs: \"CommonJS\", esm: \"ES modules\", umd: \"UMD\" };\n\n    if (global[key] && global[key] !== process.env.BUILD_FORMAT) {\n      const initialBuildName = buildNames[global[key]];\n      const secondaryBuildName = buildNames[process.env.BUILD_FORMAT];\n\n      // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n      throw new Error(\n        `You are loading the ${secondaryBuildName} build of React Router ` +\n          `on a page that is already running the ${initialBuildName} ` +\n          `build, so things won't work right.`\n      );\n    }\n\n    global[key] = process.env.BUILD_FORMAT;\n  }\n}\n\nexport { default as MemoryRouter } from \"./MemoryRouter.js\";\nexport { default as Prompt } from \"./Prompt.js\";\nexport { default as Redirect } from \"./Redirect.js\";\nexport { default as Route } from \"./Route.js\";\nexport { default as Router } from \"./Router.js\";\nexport { default as StaticRouter } from \"./StaticRouter.js\";\nexport { default as Switch } from \"./Switch.js\";\nexport { default as generatePath } from \"./generatePath.js\";\nexport { default as matchPath } from \"./matchPath.js\";\nexport { default as withRouter } from \"./withRouter.js\";\n\nexport { default as __HistoryContext } from \"./HistoryContext.js\";\nexport { default as __RouterContext } from \"./RouterContext.js\";\n\nexport { useHistory, useLocation, useParams, useRouteMatch } from \"./hooks.js\";\n"]}, "metadata": {}, "sourceType": "module"}