{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar edge = require('./edge.cjs.js');\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n  let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n  let targetPoint = 0;\n  let containerPoint = 0;\n  if (utils.isNumber(offset)) {\n    /**\n     * If we're provided offset: [0, 0.5, 1] then each number x should become\n     * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n     * and container etc.\n     */\n    offsetDefinition = [offset, offset];\n  } else if (utils.isString(offset)) {\n    offset = offset.trim();\n    if (offset.includes(\" \")) {\n      offsetDefinition = offset.split(\" \");\n    } else {\n      /**\n       * If we're provided a definition like \"100px\" then we want to apply\n       * that only to the top of the target point, leaving the container at 0.\n       * Whereas a named offset like \"end\" should be applied to both.\n       */\n      offsetDefinition = [offset, edge.namedEdges[offset] ? offset : `0`];\n    }\n  }\n  targetPoint = edge.resolveEdge(offsetDefinition[0], targetLength, targetInset);\n  containerPoint = edge.resolveEdge(offsetDefinition[1], containerLength);\n  return targetPoint - containerPoint;\n}\nexports.resolveOffset = resolveOffset;", "map": {"version": 3, "names": ["utils", "require", "edge", "defaultOffset", "resolveOffset", "offset", "containerLength", "targetLength", "targetInset", "offsetDefinition", "Array", "isArray", "targetPoint", "containerPoint", "isNumber", "isString", "trim", "includes", "split", "<PERSON><PERSON><PERSON>", "resolveEdge", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/offsets/offset.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar edge = require('./edge.cjs.js');\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (utils.isNumber(offset)) {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (utils.isString(offset)) {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [\n                offset,\n                edge.namedEdges[offset] ? offset : `0`,\n            ];\n        }\n    }\n    targetPoint = edge.resolveEdge(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = edge.resolveEdge(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\nexports.resolveOffset = resolveOffset;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,IAAI,GAAGD,OAAO,CAAC,eAAe,CAAC;AAEnC,MAAME,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5B,SAASC,aAAaA,CAACC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,WAAW,EAAE;EACvE,IAAIC,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,GAAGA,MAAM,GAAGF,aAAa;EACrE,IAAIS,WAAW,GAAG,CAAC;EACnB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIb,KAAK,CAACc,QAAQ,CAACT,MAAM,CAAC,EAAE;IACxB;AACR;AACA;AACA;AACA;IACQI,gBAAgB,GAAG,CAACJ,MAAM,EAAEA,MAAM,CAAC;EACvC,CAAC,MACI,IAAIL,KAAK,CAACe,QAAQ,CAACV,MAAM,CAAC,EAAE;IAC7BA,MAAM,GAAGA,MAAM,CAACW,IAAI,CAAC,CAAC;IACtB,IAAIX,MAAM,CAACY,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtBR,gBAAgB,GAAGJ,MAAM,CAACa,KAAK,CAAC,GAAG,CAAC;IACxC,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;MACYT,gBAAgB,GAAG,CACfJ,MAAM,EACNH,IAAI,CAACiB,UAAU,CAACd,MAAM,CAAC,GAAGA,MAAM,GAAG,GAAG,CACzC;IACL;EACJ;EACAO,WAAW,GAAGV,IAAI,CAACkB,WAAW,CAACX,gBAAgB,CAAC,CAAC,CAAC,EAAEF,YAAY,EAAEC,WAAW,CAAC;EAC9EK,cAAc,GAAGX,IAAI,CAACkB,WAAW,CAACX,gBAAgB,CAAC,CAAC,CAAC,EAAEH,eAAe,CAAC;EACvE,OAAOM,WAAW,GAAGC,cAAc;AACvC;AAEAQ,OAAO,CAACjB,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}