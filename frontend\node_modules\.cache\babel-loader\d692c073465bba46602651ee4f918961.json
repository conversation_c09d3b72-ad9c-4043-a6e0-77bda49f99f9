{"ast": null, "code": "import { useMotionValue } from './use-motion-value.mjs';\nimport { useMultiOnChange } from './use-on-change.mjs';\nimport { cancelSync, sync } from '../frameloop/index.mjs';\nfunction useCombineMotionValues(values, combineValues) {\n  /**\n   * Initialise the returned motion value. This remains the same between renders.\n   */\n  const value = useMotionValue(combineValues());\n  /**\n   * Create a function that will update the template motion value with the latest values.\n   * This is pre-bound so whenever a motion value updates it can schedule its\n   * execution in Framesync. If it's already been scheduled it won't be fired twice\n   * in a single frame.\n   */\n  const updateValue = () => value.set(combineValues());\n  /**\n   * Synchronously update the motion value with the latest values during the render.\n   * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n   */\n  updateValue();\n  /**\n   * Subscribe to all motion values found within the template. Whenever any of them change,\n   * schedule an update.\n   */\n  useMultiOnChange(values, () => sync.update(updateValue, false, true), () => cancelSync.update(updateValue));\n  return value;\n}\nexport { useCombineMotionValues };", "map": {"version": 3, "names": ["useMotionValue", "useMultiOnChange", "cancelSync", "sync", "useCombineMotionValues", "values", "combineValues", "value", "updateValue", "set", "update"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/value/use-combine-values.mjs"], "sourcesContent": ["import { useMotionValue } from './use-motion-value.mjs';\nimport { useMultiOnChange } from './use-on-change.mjs';\nimport { cancelSync, sync } from '../frameloop/index.mjs';\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = useMotionValue(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    useMultiOnChange(values, () => sync.update(updateValue, false, true), () => cancelSync.update(updateValue));\n    return value;\n}\n\nexport { useCombineMotionValues };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,UAAU,EAAEC,IAAI,QAAQ,wBAAwB;AAEzD,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EACnD;AACJ;AACA;EACI,MAAMC,KAAK,GAAGP,cAAc,CAACM,aAAa,CAAC,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,WAAW,GAAGA,CAAA,KAAMD,KAAK,CAACE,GAAG,CAACH,aAAa,CAAC,CAAC,CAAC;EACpD;AACJ;AACA;AACA;EACIE,WAAW,CAAC,CAAC;EACb;AACJ;AACA;AACA;EACIP,gBAAgB,CAACI,MAAM,EAAE,MAAMF,IAAI,CAACO,MAAM,CAACF,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,MAAMN,UAAU,CAACQ,MAAM,CAACF,WAAW,CAAC,CAAC;EAC3G,OAAOD,KAAK;AAChB;AAEA,SAASH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}