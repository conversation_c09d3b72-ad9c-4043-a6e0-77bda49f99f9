{"ast": null, "code": "'use strict';\n\nvar handleElement = require('./handle-element.cjs.js');\nvar handleWindow = require('./handle-window.cjs.js');\nvar utils = require('@motionone/utils');\nfunction resize(a, b) {\n  return utils.isFunction(a) ? handleWindow.resizeWindow(a) : handleElement.resizeElement(a, b);\n}\nexports.resize = resize;", "map": {"version": 3, "names": ["handleElement", "require", "handleWindow", "utils", "resize", "a", "b", "isFunction", "resizeWindow", "resizeElement", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/resize/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar handleElement = require('./handle-element.cjs.js');\nvar handleWindow = require('./handle-window.cjs.js');\nvar utils = require('@motionone/utils');\n\nfunction resize(a, b) {\n    return utils.isFunction(a) ? handleWindow.resizeWindow(a) : handleElement.resizeElement(a, b);\n}\n\nexports.resize = resize;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACtD,IAAIC,YAAY,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACpD,IAAIE,KAAK,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAEvC,SAASG,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,OAAOH,KAAK,CAACI,UAAU,CAACF,CAAC,CAAC,GAAGH,YAAY,CAACM,YAAY,CAACH,CAAC,CAAC,GAAGL,aAAa,CAACS,aAAa,CAACJ,CAAC,EAAEC,CAAC,CAAC;AACjG;AAEAI,OAAO,CAACN,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}