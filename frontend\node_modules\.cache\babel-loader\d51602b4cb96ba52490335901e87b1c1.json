{"ast": null, "code": "'use strict';\n\nvar resolveElements = require('../utils/resolve-elements.cjs.js');\nvar utils = require('@motionone/utils');\nconst thresholds = {\n  any: 0,\n  all: 1\n};\nfunction inView(elementOrSelector, onStart, {\n  root,\n  margin: rootMargin,\n  amount = \"any\"\n} = {}) {\n  /**\n   * If this browser doesn't support IntersectionObserver, return a dummy stop function.\n   * Default triggering of onStart is tricky - it could be used for starting/stopping\n   * videos, lazy loading content etc. We could provide an option to enable a fallback, or\n   * provide a fallback callback option.\n   */\n  if (typeof IntersectionObserver === \"undefined\") {\n    return () => {};\n  }\n  const elements = resolveElements.resolveElements(elementOrSelector);\n  const activeIntersections = new WeakMap();\n  const onIntersectionChange = entries => {\n    entries.forEach(entry => {\n      const onEnd = activeIntersections.get(entry.target);\n      /**\n       * If there's no change to the intersection, we don't need to\n       * do anything here.\n       */\n      if (entry.isIntersecting === Boolean(onEnd)) return;\n      if (entry.isIntersecting) {\n        const newOnEnd = onStart(entry);\n        if (utils.isFunction(newOnEnd)) {\n          activeIntersections.set(entry.target, newOnEnd);\n        } else {\n          observer.unobserve(entry.target);\n        }\n      } else if (onEnd) {\n        onEnd(entry);\n        activeIntersections.delete(entry.target);\n      }\n    });\n  };\n  const observer = new IntersectionObserver(onIntersectionChange, {\n    root,\n    rootMargin,\n    threshold: typeof amount === \"number\" ? amount : thresholds[amount]\n  });\n  elements.forEach(element => observer.observe(element));\n  return () => observer.disconnect();\n}\nexports.inView = inView;", "map": {"version": 3, "names": ["resolveElements", "require", "utils", "thresholds", "any", "all", "inView", "elementOrSelector", "onStart", "root", "margin", "rootMargin", "amount", "IntersectionObserver", "elements", "activeIntersections", "WeakMap", "onIntersectionChange", "entries", "for<PERSON>ach", "entry", "onEnd", "get", "target", "isIntersecting", "Boolean", "newOnEnd", "isFunction", "set", "observer", "unobserve", "delete", "threshold", "element", "observe", "disconnect", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/in-view.cjs.js"], "sourcesContent": ["'use strict';\n\nvar resolveElements = require('../utils/resolve-elements.cjs.js');\nvar utils = require('@motionone/utils');\n\nconst thresholds = {\n    any: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"any\" } = {}) {\n    /**\n     * If this browser doesn't support IntersectionObserver, return a dummy stop function.\n     * Default triggering of onStart is tricky - it could be used for starting/stopping\n     * videos, lazy loading content etc. We could provide an option to enable a fallback, or\n     * provide a fallback callback option.\n     */\n    if (typeof IntersectionObserver === \"undefined\") {\n        return () => { };\n    }\n    const elements = resolveElements.resolveElements(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (utils.isFunction(newOnEnd)) {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (onEnd) {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\nexports.inView = inView;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,kCAAkC,CAAC;AACjE,IAAIC,KAAK,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAEvC,MAAME,UAAU,GAAG;EACfC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE;AACT,CAAC;AACD,SAASC,MAAMA,CAACC,iBAAiB,EAAEC,OAAO,EAAE;EAAEC,IAAI;EAAEC,MAAM,EAAEC,UAAU;EAAEC,MAAM,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC3F;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,OAAOC,oBAAoB,KAAK,WAAW,EAAE;IAC7C,OAAO,MAAM,CAAE,CAAC;EACpB;EACA,MAAMC,QAAQ,GAAGd,eAAe,CAACA,eAAe,CAACO,iBAAiB,CAAC;EACnE,MAAMQ,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;EACzC,MAAMC,oBAAoB,GAAIC,OAAO,IAAK;IACtCA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;MACvB,MAAMC,KAAK,GAAGN,mBAAmB,CAACO,GAAG,CAACF,KAAK,CAACG,MAAM,CAAC;MACnD;AACZ;AACA;AACA;MACY,IAAIH,KAAK,CAACI,cAAc,KAAKC,OAAO,CAACJ,KAAK,CAAC,EACvC;MACJ,IAAID,KAAK,CAACI,cAAc,EAAE;QACtB,MAAME,QAAQ,GAAGlB,OAAO,CAACY,KAAK,CAAC;QAC/B,IAAIlB,KAAK,CAACyB,UAAU,CAACD,QAAQ,CAAC,EAAE;UAC5BX,mBAAmB,CAACa,GAAG,CAACR,KAAK,CAACG,MAAM,EAAEG,QAAQ,CAAC;QACnD,CAAC,MACI;UACDG,QAAQ,CAACC,SAAS,CAACV,KAAK,CAACG,MAAM,CAAC;QACpC;MACJ,CAAC,MACI,IAAIF,KAAK,EAAE;QACZA,KAAK,CAACD,KAAK,CAAC;QACZL,mBAAmB,CAACgB,MAAM,CAACX,KAAK,CAACG,MAAM,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN,CAAC;EACD,MAAMM,QAAQ,GAAG,IAAIhB,oBAAoB,CAACI,oBAAoB,EAAE;IAC5DR,IAAI;IACJE,UAAU;IACVqB,SAAS,EAAE,OAAOpB,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGT,UAAU,CAACS,MAAM;EACtE,CAAC,CAAC;EACFE,QAAQ,CAACK,OAAO,CAAEc,OAAO,IAAKJ,QAAQ,CAACK,OAAO,CAACD,OAAO,CAAC,CAAC;EACxD,OAAO,MAAMJ,QAAQ,CAACM,UAAU,CAAC,CAAC;AACtC;AAEAC,OAAO,CAAC9B,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}