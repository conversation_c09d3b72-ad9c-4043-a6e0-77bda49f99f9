{"ast": null, "code": "const isFunction = value => typeof value === \"function\";\nexport { isFunction };", "map": {"version": 3, "names": ["isFunction", "value"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/is-function.es.js"], "sourcesContent": ["const isFunction = (value) => typeof value === \"function\";\n\nexport { isFunction };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAIC,KAAK,IAAK,OAAOA,KAAK,KAAK,UAAU;AAEzD,SAASD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}