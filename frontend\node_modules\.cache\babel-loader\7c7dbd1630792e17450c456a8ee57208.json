{"ast": null, "code": "const noop = () => {};\nconst noopReturn = v => v;\nexport { noop, noopReturn };", "map": {"version": 3, "names": ["noop", "noopReturn", "v"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/noop.es.js"], "sourcesContent": ["const noop = () => { };\nconst noopReturn = (v) => v;\n\nexport { noop, noopReturn };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;AACtB,MAAMC,UAAU,GAAIC,CAAC,IAAKA,CAAC;AAE3B,SAASF,IAAI,EAAEC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}