from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

db = SQLAlchemy()

def create_app():
    app = Flask(__name__)

    # Enable CORS for all routes
    CORS(app, origins=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:8000", "null"], supports_credentials=True)

    # Configuration settings
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv(
        'DATABASE_URL',
        'sqlite:///cable_management.db'  # Default to SQLite for easier setup
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'your_jwt_secret_key_change_in_production')
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key_change_in_production')

    db.init_app(app)

    # Register blueprints
    from .routes import api
    app.register_blueprint(api, url_prefix='/api')

    with app.app_context():
        db.create_all()  # Create database tables

        # Create default admin user if not exists
        from .models import Admin
        from .utils import hash_password
        if not Admin.query.filter_by(username='admin').first():
            admin = Admin(
                username='admin',
                password_hash=hash_password('admin123')
            )
            db.session.add(admin)
            db.session.commit()
            print("Default admin user created: username='admin', password='admin123'")

    return app