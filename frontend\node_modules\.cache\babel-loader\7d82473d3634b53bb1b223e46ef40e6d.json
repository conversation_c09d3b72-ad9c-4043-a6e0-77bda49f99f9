{"ast": null, "code": "function isMouseEvent(event) {\n  // PointerEvent inherits from MouseEvent so we can't use a straight instanceof check.\n  if (typeof PointerEvent !== \"undefined\" && event instanceof PointerEvent) {\n    return !!(event.pointerType === \"mouse\");\n  }\n  return event instanceof MouseEvent;\n}\nfunction isTouchEvent(event) {\n  const hasTouches = !!event.touches;\n  return hasTouches;\n}\nexport { isMouseEvent, isTouchEvent };", "map": {"version": 3, "names": ["isMouseEvent", "event", "PointerEvent", "pointerType", "MouseEvent", "isTouchEvent", "hasTouches", "touches"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/utils/event-type.mjs"], "sourcesContent": ["function isMouseEvent(event) {\n    // PointerEvent inherits from MouseEvent so we can't use a straight instanceof check.\n    if (typeof PointerEvent !== \"undefined\" && event instanceof PointerEvent) {\n        return !!(event.pointerType === \"mouse\");\n    }\n    return event instanceof MouseEvent;\n}\nfunction isTouchEvent(event) {\n    const hasTouches = !!event.touches;\n    return hasTouches;\n}\n\nexport { isMouseEvent, isTouchEvent };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,KAAK,EAAE;EACzB;EACA,IAAI,OAAOC,YAAY,KAAK,WAAW,IAAID,KAAK,YAAYC,YAAY,EAAE;IACtE,OAAO,CAAC,EAAED,KAAK,CAACE,WAAW,KAAK,OAAO,CAAC;EAC5C;EACA,OAAOF,KAAK,YAAYG,UAAU;AACtC;AACA,SAASC,YAAYA,CAACJ,KAAK,EAAE;EACzB,MAAMK,UAAU,GAAG,CAAC,CAACL,KAAK,CAACM,OAAO;EAClC,OAAOD,UAAU;AACrB;AAEA,SAASN,YAAY,EAAEK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}