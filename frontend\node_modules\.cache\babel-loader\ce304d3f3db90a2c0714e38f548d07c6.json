{"ast": null, "code": "import { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nconst svgMotionConfig = {\n  useVisualState: makeUseVisualState({\n    scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n    createRenderState: createSvgRenderState,\n    onMount: (props, instance, {\n      renderState,\n      latestValues\n    }) => {\n      try {\n        renderState.dimensions = typeof instance.getBBox === \"function\" ? instance.getBBox() : instance.getBoundingClientRect();\n      } catch (e) {\n        // Most likely trying to measure an unrendered element under Firefox\n        renderState.dimensions = {\n          x: 0,\n          y: 0,\n          width: 0,\n          height: 0\n        };\n      }\n      buildSVGAttrs(renderState, latestValues, {\n        enableHardwareAcceleration: false\n      }, isSVGTag(instance.tagName), props.transformTemplate);\n      renderSVG(instance, renderState);\n    }\n  })\n};\nexport { svgMotionConfig };", "map": {"version": 3, "names": ["renderSVG", "scrapeMotionValuesFromProps", "makeUseVisualState", "createSvgRenderState", "buildSVGAttrs", "isSVGTag", "svgMotionConfig", "useVisualState", "createRenderState", "onMount", "props", "instance", "renderState", "latestValues", "dimensions", "getBBox", "getBoundingClientRect", "e", "x", "y", "width", "height", "enableHardwareAcceleration", "tagName", "transformTemplate"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs"], "sourcesContent": ["import { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\n\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n        onMount: (props, instance, { renderState, latestValues }) => {\n            try {\n                renderState.dimensions =\n                    typeof instance.getBBox ===\n                        \"function\"\n                        ? instance.getBBox()\n                        : instance.getBoundingClientRect();\n            }\n            catch (e) {\n                // Most likely trying to measure an unrendered element under Firefox\n                renderState.dimensions = {\n                    x: 0,\n                    y: 0,\n                    width: 0,\n                    height: 0,\n                };\n            }\n            buildSVGAttrs(renderState, latestValues, { enableHardwareAcceleration: false }, isSVGTag(instance.tagName), props.transformTemplate);\n            renderSVG(instance, renderState);\n        },\n    }),\n};\n\nexport { svgMotionConfig };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,eAAe,GAAG;EACpBC,cAAc,EAAEL,kBAAkB,CAAC;IAC/BD,2BAA2B,EAAEA,2BAA2B;IACxDO,iBAAiB,EAAEL,oBAAoB;IACvCM,OAAO,EAAEA,CAACC,KAAK,EAAEC,QAAQ,EAAE;MAAEC,WAAW;MAAEC;IAAa,CAAC,KAAK;MACzD,IAAI;QACAD,WAAW,CAACE,UAAU,GAClB,OAAOH,QAAQ,CAACI,OAAO,KACnB,UAAU,GACRJ,QAAQ,CAACI,OAAO,CAAC,CAAC,GAClBJ,QAAQ,CAACK,qBAAqB,CAAC,CAAC;MAC9C,CAAC,CACD,OAAOC,CAAC,EAAE;QACN;QACAL,WAAW,CAACE,UAAU,GAAG;UACrBI,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;QACZ,CAAC;MACL;MACAjB,aAAa,CAACQ,WAAW,EAAEC,YAAY,EAAE;QAAES,0BAA0B,EAAE;MAAM,CAAC,EAAEjB,QAAQ,CAACM,QAAQ,CAACY,OAAO,CAAC,EAAEb,KAAK,CAACc,iBAAiB,CAAC;MACpIxB,SAAS,CAACW,QAAQ,EAAEC,WAAW,CAAC;IACpC;EACJ,CAAC;AACL,CAAC;AAED,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}