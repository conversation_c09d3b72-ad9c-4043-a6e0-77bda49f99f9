{"ast": null, "code": "'use strict';\n\nfunction isVariant(definition) {\n  return typeof definition === \"object\";\n}\nexports.isVariant = isVariant;", "map": {"version": 3, "names": ["isVariant", "definition", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/utils/is-variant.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction isVariant(definition) {\n    return typeof definition === \"object\";\n}\n\nexports.isVariant = isVariant;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAASA,CAACC,UAAU,EAAE;EAC3B,OAAO,OAAOA,UAAU,KAAK,QAAQ;AACzC;AAEAC,OAAO,CAACF,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}