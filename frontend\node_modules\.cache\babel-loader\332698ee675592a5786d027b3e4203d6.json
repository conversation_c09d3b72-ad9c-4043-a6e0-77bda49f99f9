{"ast": null, "code": "import { cssVariableRegex } from '../../render/dom/utils/css-variables-conversion.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nconst varToken = \"_$css\";\nconst correctBoxShadow = {\n  correct: (latest, {\n    treeScale,\n    projectionDelta\n  }) => {\n    const original = latest;\n    /**\n     * We need to first strip and store CSS variables from the string.\n     */\n    const containsCSSVariables = latest.includes(\"var(\");\n    const cssVariables = [];\n    if (containsCSSVariables) {\n      latest = latest.replace(cssVariableRegex, match => {\n        cssVariables.push(match);\n        return varToken;\n      });\n    }\n    const shadow = complex.parse(latest);\n    // TODO: Doesn't support multiple shadows\n    if (shadow.length > 5) return original;\n    const template = complex.createTransformer(latest);\n    const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n    // Calculate the overall context scale\n    const xScale = projectionDelta.x.scale * treeScale.x;\n    const yScale = projectionDelta.y.scale * treeScale.y;\n    shadow[0 + offset] /= xScale;\n    shadow[1 + offset] /= yScale;\n    /**\n     * Ideally we'd correct x and y scales individually, but because blur and\n     * spread apply to both we have to take a scale average and apply that instead.\n     * We could potentially improve the outcome of this by incorporating the ratio between\n     * the two scales.\n     */\n    const averageScale = mix(xScale, yScale, 0.5);\n    // Blur\n    if (typeof shadow[2 + offset] === \"number\") shadow[2 + offset] /= averageScale;\n    // Spread\n    if (typeof shadow[3 + offset] === \"number\") shadow[3 + offset] /= averageScale;\n    let output = template(shadow);\n    if (containsCSSVariables) {\n      let i = 0;\n      output = output.replace(varToken, () => {\n        const cssVariable = cssVariables[i];\n        i++;\n        return cssVariable;\n      });\n    }\n    return output;\n  }\n};\nexport { correctBoxShadow };", "map": {"version": 3, "names": ["cssVariableRegex", "mix", "complex", "varToken", "correctBoxShadow", "correct", "latest", "treeScale", "projectionDel<PERSON>", "original", "containsCSSVariables", "includes", "cssVariables", "replace", "match", "push", "shadow", "parse", "length", "template", "createTransformer", "offset", "xScale", "x", "scale", "yScale", "y", "averageScale", "output", "i", "cssVariable"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs"], "sourcesContent": ["import { cssVariableRegex } from '../../render/dom/utils/css-variables-conversion.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\n\nconst varToken = \"_$css\";\nconst correctBoxShadow = {\n    correct: (latest, { treeScale, projectionDelta }) => {\n        const original = latest;\n        /**\n         * We need to first strip and store CSS variables from the string.\n         */\n        const containsCSSVariables = latest.includes(\"var(\");\n        const cssVariables = [];\n        if (containsCSSVariables) {\n            latest = latest.replace(cssVariableRegex, (match) => {\n                cssVariables.push(match);\n                return varToken;\n            });\n        }\n        const shadow = complex.parse(latest);\n        // TODO: Doesn't support multiple shadows\n        if (shadow.length > 5)\n            return original;\n        const template = complex.createTransformer(latest);\n        const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n        // Calculate the overall context scale\n        const xScale = projectionDelta.x.scale * treeScale.x;\n        const yScale = projectionDelta.y.scale * treeScale.y;\n        shadow[0 + offset] /= xScale;\n        shadow[1 + offset] /= yScale;\n        /**\n         * Ideally we'd correct x and y scales individually, but because blur and\n         * spread apply to both we have to take a scale average and apply that instead.\n         * We could potentially improve the outcome of this by incorporating the ratio between\n         * the two scales.\n         */\n        const averageScale = mix(xScale, yScale, 0.5);\n        // Blur\n        if (typeof shadow[2 + offset] === \"number\")\n            shadow[2 + offset] /= averageScale;\n        // Spread\n        if (typeof shadow[3 + offset] === \"number\")\n            shadow[3 + offset] /= averageScale;\n        let output = template(shadow);\n        if (containsCSSVariables) {\n            let i = 0;\n            output = output.replace(varToken, () => {\n                const cssVariable = cssVariables[i];\n                i++;\n                return cssVariable;\n            });\n        }\n        return output;\n    },\n};\n\nexport { correctBoxShadow };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,qDAAqD;AACtF,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAE7D,MAAMC,QAAQ,GAAG,OAAO;AACxB,MAAMC,gBAAgB,GAAG;EACrBC,OAAO,EAAEA,CAACC,MAAM,EAAE;IAAEC,SAAS;IAAEC;EAAgB,CAAC,KAAK;IACjD,MAAMC,QAAQ,GAAGH,MAAM;IACvB;AACR;AACA;IACQ,MAAMI,oBAAoB,GAAGJ,MAAM,CAACK,QAAQ,CAAC,MAAM,CAAC;IACpD,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAIF,oBAAoB,EAAE;MACtBJ,MAAM,GAAGA,MAAM,CAACO,OAAO,CAACb,gBAAgB,EAAGc,KAAK,IAAK;QACjDF,YAAY,CAACG,IAAI,CAACD,KAAK,CAAC;QACxB,OAAOX,QAAQ;MACnB,CAAC,CAAC;IACN;IACA,MAAMa,MAAM,GAAGd,OAAO,CAACe,KAAK,CAACX,MAAM,CAAC;IACpC;IACA,IAAIU,MAAM,CAACE,MAAM,GAAG,CAAC,EACjB,OAAOT,QAAQ;IACnB,MAAMU,QAAQ,GAAGjB,OAAO,CAACkB,iBAAiB,CAACd,MAAM,CAAC;IAClD,MAAMe,MAAM,GAAG,OAAOL,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;IACpD;IACA,MAAMM,MAAM,GAAGd,eAAe,CAACe,CAAC,CAACC,KAAK,GAAGjB,SAAS,CAACgB,CAAC;IACpD,MAAME,MAAM,GAAGjB,eAAe,CAACkB,CAAC,CAACF,KAAK,GAAGjB,SAAS,CAACmB,CAAC;IACpDV,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAIC,MAAM;IAC5BN,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAII,MAAM;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,MAAME,YAAY,GAAG1B,GAAG,CAACqB,MAAM,EAAEG,MAAM,EAAE,GAAG,CAAC;IAC7C;IACA,IAAI,OAAOT,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,KAAK,QAAQ,EACtCL,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAIM,YAAY;IACtC;IACA,IAAI,OAAOX,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,KAAK,QAAQ,EACtCL,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAIM,YAAY;IACtC,IAAIC,MAAM,GAAGT,QAAQ,CAACH,MAAM,CAAC;IAC7B,IAAIN,oBAAoB,EAAE;MACtB,IAAImB,CAAC,GAAG,CAAC;MACTD,MAAM,GAAGA,MAAM,CAACf,OAAO,CAACV,QAAQ,EAAE,MAAM;QACpC,MAAM2B,WAAW,GAAGlB,YAAY,CAACiB,CAAC,CAAC;QACnCA,CAAC,EAAE;QACH,OAAOC,WAAW;MACtB,CAAC,CAAC;IACN;IACA,OAAOF,MAAM;EACjB;AACJ,CAAC;AAED,SAASxB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}