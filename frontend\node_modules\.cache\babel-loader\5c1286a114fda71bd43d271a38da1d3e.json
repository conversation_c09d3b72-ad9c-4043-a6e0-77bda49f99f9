{"ast": null, "code": "'use strict';\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = require('function-bind');\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);", "map": {"version": 3, "names": ["call", "Function", "prototype", "$hasOwn", "Object", "hasOwnProperty", "bind", "require", "module", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/hasown/index.js"], "sourcesContent": ["'use strict';\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = require('function-bind');\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,QAAQ,CAACC,SAAS,CAACF,IAAI;AAClC,IAAIG,OAAO,GAAGC,MAAM,CAACF,SAAS,CAACG,cAAc;AAC7C,IAAIC,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEnC;AACAC,MAAM,CAACC,OAAO,GAAGH,IAAI,CAACN,IAAI,CAACA,IAAI,EAAEG,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}