{"ast": null, "code": "import { setTarget } from './setters.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { transformProps } from '../html/utils/transform.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { handoffOptimizedAppearAnimation } from '../../animation/optimized-appear/handoff.mjs';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { createMotionValueAnimation } from '../../animation/index.mjs';\nfunction animateVisualElement(visualElement, definition, options = {}) {\n  visualElement.notify(\"AnimationStart\", definition);\n  let animation;\n  if (Array.isArray(definition)) {\n    const animations = definition.map(variant => animateVariant(visualElement, variant, options));\n    animation = Promise.all(animations);\n  } else if (typeof definition === \"string\") {\n    animation = animateVariant(visualElement, definition, options);\n  } else {\n    const resolvedDefinition = typeof definition === \"function\" ? resolveVariant(visualElement, definition, options.custom) : definition;\n    animation = animateTarget(visualElement, resolvedDefinition, options);\n  }\n  return animation.then(() => visualElement.notify(\"AnimationComplete\", definition));\n}\nfunction animateVariant(visualElement, variant, options = {}) {\n  var _a;\n  const resolved = resolveVariant(visualElement, variant, options.custom);\n  let {\n    transition = visualElement.getDefaultTransition() || {}\n  } = resolved || {};\n  if (options.transitionOverride) {\n    transition = options.transitionOverride;\n  }\n  /**\n   * If we have a variant, create a callback that runs it as an animation.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getAnimation = resolved ? () => animateTarget(visualElement, resolved, options) : () => Promise.resolve();\n  /**\n   * If we have children, create a callback that runs all their animations.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getChildAnimations = ((_a = visualElement.variantChildren) === null || _a === void 0 ? void 0 : _a.size) ? (forwardDelay = 0) => {\n    const {\n      delayChildren = 0,\n      staggerChildren,\n      staggerDirection\n    } = transition;\n    return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n  } : () => Promise.resolve();\n  /**\n   * If the transition explicitly defines a \"when\" option, we need to resolve either\n   * this animation or all children animations before playing the other.\n   */\n  const {\n    when\n  } = transition;\n  if (when) {\n    const [first, last] = when === \"beforeChildren\" ? [getAnimation, getChildAnimations] : [getChildAnimations, getAnimation];\n    return first().then(last);\n  } else {\n    return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n  }\n}\n/**\n * @internal\n */\nfunction animateTarget(visualElement, definition, {\n  delay = 0,\n  transitionOverride,\n  type\n} = {}) {\n  var _a;\n  let {\n    transition = visualElement.getDefaultTransition(),\n    transitionEnd,\n    ...target\n  } = visualElement.makeTargetAnimatable(definition);\n  const willChange = visualElement.getValue(\"willChange\");\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && ((_a = visualElement.animationState) === null || _a === void 0 ? void 0 : _a.getState()[type]);\n  for (const key in target) {\n    const value = visualElement.getValue(key);\n    const valueTarget = target[key];\n    if (!value || valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    let valueTransition = {\n      delay,\n      elapsed: 0,\n      ...transition\n    };\n    /**\n     * Make animation instant if this is a transform prop and we should reduce motion.\n     */\n    if (visualElement.shouldReduceMotion && transformProps.has(key)) {\n      valueTransition = {\n        ...valueTransition,\n        type: false,\n        delay: 0\n      };\n    }\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    if (!value.hasAnimated) {\n      const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n      if (appearId) {\n        valueTransition.elapsed = handoffOptimizedAppearAnimation(appearId, key);\n      }\n    }\n    let animation = value.start(createMotionValueAnimation(key, value, valueTarget, valueTransition));\n    if (isWillChangeMotionValue(willChange)) {\n      willChange.add(key);\n      animation = animation.then(() => willChange.remove(key));\n    }\n    animations.push(animation);\n  }\n  return Promise.all(animations).then(() => {\n    transitionEnd && setTarget(visualElement, transitionEnd);\n  });\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n  const animations = [];\n  const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n  const generateStaggerDuration = staggerDirection === 1 ? (i = 0) => i * staggerChildren : (i = 0) => maxStaggerDuration - i * staggerChildren;\n  Array.from(visualElement.variantChildren).sort(sortByTreeOrder).forEach((child, i) => {\n    animations.push(animateVariant(child, variant, {\n      ...options,\n      delay: delayChildren + generateStaggerDuration(i)\n    }).then(() => child.notify(\"AnimationComplete\", variant)));\n  });\n  return Promise.all(animations);\n}\nfunction stopAnimation(visualElement) {\n  visualElement.values.forEach(value => value.stop());\n}\nfunction sortByTreeOrder(a, b) {\n  return a.sortNodePosition(b);\n}\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({\n  protectedKeys,\n  needsAnimating\n}, key) {\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nexport { animateVisualElement, sortByTreeOrder, stopAnimation };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "resolveV<PERSON>t", "transformProps", "isWillChangeMotionValue", "handoffOptimizedAppearAnimation", "optimizedAppearDataAttribute", "createMotionValueAnimation", "animateVisualElement", "visualElement", "definition", "options", "notify", "animation", "Array", "isArray", "animations", "map", "variant", "animate<PERSON><PERSON><PERSON>", "Promise", "all", "resolvedDefinition", "custom", "animate<PERSON>arget", "then", "_a", "resolved", "transition", "getDefaultTransition", "transitionOverride", "getAnimation", "resolve", "getChildAnimations", "variant<PERSON><PERSON><PERSON>n", "size", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "animate<PERSON><PERSON><PERSON><PERSON>", "when", "first", "last", "delay", "type", "transitionEnd", "target", "makeTargetAnimatable", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "animationTypeState", "animationState", "getState", "key", "value", "valueTarget", "undefined", "shouldBlockAnimation", "valueTransition", "elapsed", "shouldReduceMotion", "has", "hasAnimated", "appearId", "getProps", "start", "add", "remove", "push", "maxStaggerDuration", "generateStaggerDuration", "i", "from", "sort", "sortByTreeOrder", "for<PERSON>ach", "child", "stopAnimation", "values", "stop", "a", "b", "sortNodePosition", "protected<PERSON><PERSON>s", "needsAnimating", "shouldBlock", "hasOwnProperty"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/utils/animation.mjs"], "sourcesContent": ["import { setTarget } from './setters.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { transformProps } from '../html/utils/transform.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { handoffOptimizedAppearAnimation } from '../../animation/optimized-appear/handoff.mjs';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { createMotionValueAnimation } from '../../animation/index.mjs';\n\nfunction animateVisualElement(visualElement, definition, options = {}) {\n    visualElement.notify(\"AnimationStart\", definition);\n    let animation;\n    if (Array.isArray(definition)) {\n        const animations = definition.map((variant) => animateVariant(visualElement, variant, options));\n        animation = Promise.all(animations);\n    }\n    else if (typeof definition === \"string\") {\n        animation = animateVariant(visualElement, definition, options);\n    }\n    else {\n        const resolvedDefinition = typeof definition === \"function\"\n            ? resolveVariant(visualElement, definition, options.custom)\n            : definition;\n        animation = animateTarget(visualElement, resolvedDefinition, options);\n    }\n    return animation.then(() => visualElement.notify(\"AnimationComplete\", definition));\n}\nfunction animateVariant(visualElement, variant, options = {}) {\n    var _a;\n    const resolved = resolveVariant(visualElement, variant, options.custom);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => animateTarget(visualElement, resolved, options)\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = ((_a = visualElement.variantChildren) === null || _a === void 0 ? void 0 : _a.size)\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(last);\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\n/**\n * @internal\n */\nfunction animateTarget(visualElement, definition, { delay = 0, transitionOverride, type } = {}) {\n    var _a;\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = visualElement.makeTargetAnimatable(definition);\n    const willChange = visualElement.getValue(\"willChange\");\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type && ((_a = visualElement.animationState) === null || _a === void 0 ? void 0 : _a.getState()[type]);\n    for (const key in target) {\n        const value = visualElement.getValue(key);\n        const valueTarget = target[key];\n        if (!value ||\n            valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        let valueTransition = { delay, elapsed: 0, ...transition };\n        /**\n         * Make animation instant if this is a transform prop and we should reduce motion.\n         */\n        if (visualElement.shouldReduceMotion && transformProps.has(key)) {\n            valueTransition = {\n                ...valueTransition,\n                type: false,\n                delay: 0,\n            };\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        if (!value.hasAnimated) {\n            const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n            if (appearId) {\n                valueTransition.elapsed = handoffOptimizedAppearAnimation(appearId, key);\n            }\n        }\n        let animation = value.start(createMotionValueAnimation(key, value, valueTarget, valueTransition));\n        if (isWillChangeMotionValue(willChange)) {\n            willChange.add(key);\n            animation = animation.then(() => willChange.remove(key));\n        }\n        animations.push(animation);\n    }\n    return Promise.all(animations).then(() => {\n        transitionEnd && setTarget(visualElement, transitionEnd);\n    });\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n    const generateStaggerDuration = staggerDirection === 1\n        ? (i = 0) => i * staggerChildren\n        : (i = 0) => maxStaggerDuration - i * staggerChildren;\n    Array.from(visualElement.variantChildren)\n        .sort(sortByTreeOrder)\n        .forEach((child, i) => {\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delayChildren + generateStaggerDuration(i),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    });\n    return Promise.all(animations);\n}\nfunction stopAnimation(visualElement) {\n    visualElement.values.forEach((value) => value.stop());\n}\nfunction sortByTreeOrder(a, b) {\n    return a.sortNodePosition(b);\n}\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\n\nexport { animateVisualElement, sortByTreeOrder, stopAnimation };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,+BAA+B,QAAQ,8CAA8C;AAC9F,SAASC,4BAA4B,QAAQ,8CAA8C;AAC3F,SAASC,0BAA0B,QAAQ,2BAA2B;AAEtE,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACnEF,aAAa,CAACG,MAAM,CAAC,gBAAgB,EAAEF,UAAU,CAAC;EAClD,IAAIG,SAAS;EACb,IAAIC,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;IAC3B,MAAMM,UAAU,GAAGN,UAAU,CAACO,GAAG,CAAEC,OAAO,IAAKC,cAAc,CAACV,aAAa,EAAES,OAAO,EAAEP,OAAO,CAAC,CAAC;IAC/FE,SAAS,GAAGO,OAAO,CAACC,GAAG,CAACL,UAAU,CAAC;EACvC,CAAC,MACI,IAAI,OAAON,UAAU,KAAK,QAAQ,EAAE;IACrCG,SAAS,GAAGM,cAAc,CAACV,aAAa,EAAEC,UAAU,EAAEC,OAAO,CAAC;EAClE,CAAC,MACI;IACD,MAAMW,kBAAkB,GAAG,OAAOZ,UAAU,KAAK,UAAU,GACrDR,cAAc,CAACO,aAAa,EAAEC,UAAU,EAAEC,OAAO,CAACY,MAAM,CAAC,GACzDb,UAAU;IAChBG,SAAS,GAAGW,aAAa,CAACf,aAAa,EAAEa,kBAAkB,EAAEX,OAAO,CAAC;EACzE;EACA,OAAOE,SAAS,CAACY,IAAI,CAAC,MAAMhB,aAAa,CAACG,MAAM,CAAC,mBAAmB,EAAEF,UAAU,CAAC,CAAC;AACtF;AACA,SAASS,cAAcA,CAACV,aAAa,EAAES,OAAO,EAAEP,OAAO,GAAG,CAAC,CAAC,EAAE;EAC1D,IAAIe,EAAE;EACN,MAAMC,QAAQ,GAAGzB,cAAc,CAACO,aAAa,EAAES,OAAO,EAAEP,OAAO,CAACY,MAAM,CAAC;EACvE,IAAI;IAAEK,UAAU,GAAGnB,aAAa,CAACoB,oBAAoB,CAAC,CAAC,IAAI,CAAC;EAAE,CAAC,GAAGF,QAAQ,IAAI,CAAC,CAAC;EAChF,IAAIhB,OAAO,CAACmB,kBAAkB,EAAE;IAC5BF,UAAU,GAAGjB,OAAO,CAACmB,kBAAkB;EAC3C;EACA;AACJ;AACA;AACA;EACI,MAAMC,YAAY,GAAGJ,QAAQ,GACvB,MAAMH,aAAa,CAACf,aAAa,EAAEkB,QAAQ,EAAEhB,OAAO,CAAC,GACrD,MAAMS,OAAO,CAACY,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAMC,kBAAkB,GAAG,CAAC,CAACP,EAAE,GAAGjB,aAAa,CAACyB,eAAe,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,IACvG,CAACC,YAAY,GAAG,CAAC,KAAK;IACpB,MAAM;MAAEC,aAAa,GAAG,CAAC;MAAEC,eAAe;MAAEC;IAAkB,CAAC,GAAGX,UAAU;IAC5E,OAAOY,eAAe,CAAC/B,aAAa,EAAES,OAAO,EAAEmB,aAAa,GAAGD,YAAY,EAAEE,eAAe,EAAEC,gBAAgB,EAAE5B,OAAO,CAAC;EAC5H,CAAC,GACC,MAAMS,OAAO,CAACY,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAM;IAAES;EAAK,CAAC,GAAGb,UAAU;EAC3B,IAAIa,IAAI,EAAE;IACN,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGF,IAAI,KAAK,gBAAgB,GACzC,CAACV,YAAY,EAAEE,kBAAkB,CAAC,GAClC,CAACA,kBAAkB,EAAEF,YAAY,CAAC;IACxC,OAAOW,KAAK,CAAC,CAAC,CAACjB,IAAI,CAACkB,IAAI,CAAC;EAC7B,CAAC,MACI;IACD,OAAOvB,OAAO,CAACC,GAAG,CAAC,CAACU,YAAY,CAAC,CAAC,EAAEE,kBAAkB,CAACtB,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;EAC3E;AACJ;AACA;AACA;AACA;AACA,SAASpB,aAAaA,CAACf,aAAa,EAAEC,UAAU,EAAE;EAAEkC,KAAK,GAAG,CAAC;EAAEd,kBAAkB;EAAEe;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5F,IAAInB,EAAE;EACN,IAAI;IAAEE,UAAU,GAAGnB,aAAa,CAACoB,oBAAoB,CAAC,CAAC;IAAEiB,aAAa;IAAE,GAAGC;EAAO,CAAC,GAAGtC,aAAa,CAACuC,oBAAoB,CAACtC,UAAU,CAAC;EACpI,MAAMuC,UAAU,GAAGxC,aAAa,CAACyC,QAAQ,CAAC,YAAY,CAAC;EACvD,IAAIpB,kBAAkB,EAClBF,UAAU,GAAGE,kBAAkB;EACnC,MAAMd,UAAU,GAAG,EAAE;EACrB,MAAMmC,kBAAkB,GAAGN,IAAI,KAAK,CAACnB,EAAE,GAAGjB,aAAa,CAAC2C,cAAc,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,QAAQ,CAAC,CAAC,CAACR,IAAI,CAAC,CAAC;EACjI,KAAK,MAAMS,GAAG,IAAIP,MAAM,EAAE;IACtB,MAAMQ,KAAK,GAAG9C,aAAa,CAACyC,QAAQ,CAACI,GAAG,CAAC;IACzC,MAAME,WAAW,GAAGT,MAAM,CAACO,GAAG,CAAC;IAC/B,IAAI,CAACC,KAAK,IACNC,WAAW,KAAKC,SAAS,IACxBN,kBAAkB,IACfO,oBAAoB,CAACP,kBAAkB,EAAEG,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,IAAIK,eAAe,GAAG;MAAEf,KAAK;MAAEgB,OAAO,EAAE,CAAC;MAAE,GAAGhC;IAAW,CAAC;IAC1D;AACR;AACA;IACQ,IAAInB,aAAa,CAACoD,kBAAkB,IAAI1D,cAAc,CAAC2D,GAAG,CAACR,GAAG,CAAC,EAAE;MAC7DK,eAAe,GAAG;QACd,GAAGA,eAAe;QAClBd,IAAI,EAAE,KAAK;QACXD,KAAK,EAAE;MACX,CAAC;IACL;IACA;AACR;AACA;AACA;IACQ,IAAI,CAACW,KAAK,CAACQ,WAAW,EAAE;MACpB,MAAMC,QAAQ,GAAGvD,aAAa,CAACwD,QAAQ,CAAC,CAAC,CAAC3D,4BAA4B,CAAC;MACvE,IAAI0D,QAAQ,EAAE;QACVL,eAAe,CAACC,OAAO,GAAGvD,+BAA+B,CAAC2D,QAAQ,EAAEV,GAAG,CAAC;MAC5E;IACJ;IACA,IAAIzC,SAAS,GAAG0C,KAAK,CAACW,KAAK,CAAC3D,0BAA0B,CAAC+C,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEG,eAAe,CAAC,CAAC;IACjG,IAAIvD,uBAAuB,CAAC6C,UAAU,CAAC,EAAE;MACrCA,UAAU,CAACkB,GAAG,CAACb,GAAG,CAAC;MACnBzC,SAAS,GAAGA,SAAS,CAACY,IAAI,CAAC,MAAMwB,UAAU,CAACmB,MAAM,CAACd,GAAG,CAAC,CAAC;IAC5D;IACAtC,UAAU,CAACqD,IAAI,CAACxD,SAAS,CAAC;EAC9B;EACA,OAAOO,OAAO,CAACC,GAAG,CAACL,UAAU,CAAC,CAACS,IAAI,CAAC,MAAM;IACtCqB,aAAa,IAAI7C,SAAS,CAACQ,aAAa,EAAEqC,aAAa,CAAC;EAC5D,CAAC,CAAC;AACN;AACA,SAASN,eAAeA,CAAC/B,aAAa,EAAES,OAAO,EAAEmB,aAAa,GAAG,CAAC,EAAEC,eAAe,GAAG,CAAC,EAAEC,gBAAgB,GAAG,CAAC,EAAE5B,OAAO,EAAE;EACpH,MAAMK,UAAU,GAAG,EAAE;EACrB,MAAMsD,kBAAkB,GAAG,CAAC7D,aAAa,CAACyB,eAAe,CAACC,IAAI,GAAG,CAAC,IAAIG,eAAe;EACrF,MAAMiC,uBAAuB,GAAGhC,gBAAgB,KAAK,CAAC,GAChD,CAACiC,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAGlC,eAAe,GAC9B,CAACkC,CAAC,GAAG,CAAC,KAAKF,kBAAkB,GAAGE,CAAC,GAAGlC,eAAe;EACzDxB,KAAK,CAAC2D,IAAI,CAAChE,aAAa,CAACyB,eAAe,CAAC,CACpCwC,IAAI,CAACC,eAAe,CAAC,CACrBC,OAAO,CAAC,CAACC,KAAK,EAAEL,CAAC,KAAK;IACvBxD,UAAU,CAACqD,IAAI,CAAClD,cAAc,CAAC0D,KAAK,EAAE3D,OAAO,EAAE;MAC3C,GAAGP,OAAO;MACViC,KAAK,EAAEP,aAAa,GAAGkC,uBAAuB,CAACC,CAAC;IACpD,CAAC,CAAC,CAAC/C,IAAI,CAAC,MAAMoD,KAAK,CAACjE,MAAM,CAAC,mBAAmB,EAAEM,OAAO,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,OAAOE,OAAO,CAACC,GAAG,CAACL,UAAU,CAAC;AAClC;AACA,SAAS8D,aAAaA,CAACrE,aAAa,EAAE;EAClCA,aAAa,CAACsE,MAAM,CAACH,OAAO,CAAErB,KAAK,IAAKA,KAAK,CAACyB,IAAI,CAAC,CAAC,CAAC;AACzD;AACA,SAASL,eAAeA,CAACM,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAOD,CAAC,CAACE,gBAAgB,CAACD,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxB,oBAAoBA,CAAC;EAAE0B,aAAa;EAAEC;AAAe,CAAC,EAAE/B,GAAG,EAAE;EAClE,MAAMgC,WAAW,GAAGF,aAAa,CAACG,cAAc,CAACjC,GAAG,CAAC,IAAI+B,cAAc,CAAC/B,GAAG,CAAC,KAAK,IAAI;EACrF+B,cAAc,CAAC/B,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOgC,WAAW;AACtB;AAEA,SAAS9E,oBAAoB,EAAEmE,eAAe,EAAEG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}