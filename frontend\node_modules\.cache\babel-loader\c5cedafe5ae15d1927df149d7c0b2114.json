{"ast": null, "code": "const underDampedSpring = () => ({\n  type: \"spring\",\n  stiffness: 500,\n  damping: 25,\n  restSpeed: 10\n});\nconst criticallyDampedSpring = target => ({\n  type: \"spring\",\n  stiffness: 550,\n  damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n  restSpeed: 10\n});\nconst linearTween = () => ({\n  type: \"keyframes\",\n  ease: \"linear\",\n  duration: 0.3\n});\nconst keyframesTransition = {\n  type: \"keyframes\",\n  duration: 0.8\n};\nconst defaultTransitions = {\n  x: underDampedSpring,\n  y: underDampedSpring,\n  z: underDampedSpring,\n  rotate: underDampedSpring,\n  rotateX: underDampedSpring,\n  rotateY: underDampedSpring,\n  rotateZ: underDampedSpring,\n  scaleX: criticallyDampedSpring,\n  scaleY: criticallyDampedSpring,\n  scale: criticallyDampedSpring,\n  opacity: linearTween,\n  backgroundColor: linearTween,\n  color: linearTween,\n  default: criticallyDampedSpring\n};\nconst getDefaultTransition = (valueKey, {\n  keyframes\n}) => {\n  if (keyframes.length > 2) {\n    return keyframesTransition;\n  } else {\n    const factory = defaultTransitions[valueKey] || defaultTransitions.default;\n    return factory(keyframes[1]);\n  }\n};\nexport { criticallyDampedSpring, getDefaultTransition, linearTween, underDampedSpring };", "map": {"version": 3, "names": ["underDampedSpring", "type", "stiffness", "damping", "restSpeed", "criticallyDampedSpring", "target", "Math", "sqrt", "linearTween", "ease", "duration", "keyframesTransition", "defaultTransitions", "x", "y", "z", "rotate", "rotateX", "rotateY", "rotateZ", "scaleX", "scaleY", "scale", "opacity", "backgroundColor", "color", "default", "getDefaultTransition", "valueKey", "keyframes", "length", "factory"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs"], "sourcesContent": ["const underDampedSpring = () => ({\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n});\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst linearTween = () => ({\n    type: \"keyframes\",\n    ease: \"linear\",\n    duration: 0.3,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\nconst defaultTransitions = {\n    x: underDampedSpring,\n    y: underDampedSpring,\n    z: underDampedSpring,\n    rotate: underDampedSpring,\n    rotateX: underDampedSpring,\n    rotateY: underDampedSpring,\n    rotateZ: underDampedSpring,\n    scaleX: criticallyDampedSpring,\n    scaleY: criticallyDampedSpring,\n    scale: criticallyDampedSpring,\n    opacity: linearTween,\n    backgroundColor: linearTween,\n    color: linearTween,\n    default: criticallyDampedSpring,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else {\n        const factory = defaultTransitions[valueKey] || defaultTransitions.default;\n        return factory(keyframes[1]);\n    }\n};\n\nexport { criticallyDampedSpring, getDefaultTransition, linearTween, underDampedSpring };\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGA,CAAA,MAAO;EAC7BC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,GAAG;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE;AACf,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAIC,MAAM,KAAM;EACxCL,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,GAAG;EACdC,OAAO,EAAEG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EAC/CJ,SAAS,EAAE;AACf,CAAC,CAAC;AACF,MAAMK,WAAW,GAAGA,CAAA,MAAO;EACvBR,IAAI,EAAE,WAAW;EACjBS,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,mBAAmB,GAAG;EACxBX,IAAI,EAAE,WAAW;EACjBU,QAAQ,EAAE;AACd,CAAC;AACD,MAAME,kBAAkB,GAAG;EACvBC,CAAC,EAAEd,iBAAiB;EACpBe,CAAC,EAAEf,iBAAiB;EACpBgB,CAAC,EAAEhB,iBAAiB;EACpBiB,MAAM,EAAEjB,iBAAiB;EACzBkB,OAAO,EAAElB,iBAAiB;EAC1BmB,OAAO,EAAEnB,iBAAiB;EAC1BoB,OAAO,EAAEpB,iBAAiB;EAC1BqB,MAAM,EAAEhB,sBAAsB;EAC9BiB,MAAM,EAAEjB,sBAAsB;EAC9BkB,KAAK,EAAElB,sBAAsB;EAC7BmB,OAAO,EAAEf,WAAW;EACpBgB,eAAe,EAAEhB,WAAW;EAC5BiB,KAAK,EAAEjB,WAAW;EAClBkB,OAAO,EAAEtB;AACb,CAAC;AACD,MAAMuB,oBAAoB,GAAGA,CAACC,QAAQ,EAAE;EAAEC;AAAU,CAAC,KAAK;EACtD,IAAIA,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACtB,OAAOnB,mBAAmB;EAC9B,CAAC,MACI;IACD,MAAMoB,OAAO,GAAGnB,kBAAkB,CAACgB,QAAQ,CAAC,IAAIhB,kBAAkB,CAACc,OAAO;IAC1E,OAAOK,OAAO,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC;AACJ,CAAC;AAED,SAASzB,sBAAsB,EAAEuB,oBAAoB,EAAEnB,WAAW,EAAET,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}