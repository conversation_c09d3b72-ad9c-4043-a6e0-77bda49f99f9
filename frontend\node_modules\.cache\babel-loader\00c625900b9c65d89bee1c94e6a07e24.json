{"ast": null, "code": "import { defaults } from './defaults.es.js';\nconst calcDampingRatio = (stiffness = defaults.stiffness, damping = defaults.damping, mass = defaults.mass) => damping / (2 * Math.sqrt(stiffness * mass));\nexport { calcDampingRatio };", "map": {"version": 3, "names": ["defaults", "calcDampingRatio", "stiffness", "damping", "mass", "Math", "sqrt"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/generators/dist/spring/utils.es.js"], "sourcesContent": ["import { defaults } from './defaults.es.js';\n\nconst calcDampingRatio = (stiffness = defaults.stiffness, damping = defaults.damping, mass = defaults.mass) => damping / (2 * Math.sqrt(stiffness * mass));\n\nexport { calcDampingRatio };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAE3C,MAAMC,gBAAgB,GAAGA,CAACC,SAAS,GAAGF,QAAQ,CAACE,SAAS,EAAEC,OAAO,GAAGH,QAAQ,CAACG,OAAO,EAAEC,IAAI,GAAGJ,QAAQ,CAACI,IAAI,KAAKD,OAAO,IAAI,CAAC,GAAGE,IAAI,CAACC,IAAI,CAACJ,SAAS,GAAGE,IAAI,CAAC,CAAC;AAE1J,SAASH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}