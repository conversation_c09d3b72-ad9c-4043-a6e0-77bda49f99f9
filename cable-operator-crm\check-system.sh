#!/bin/bash

# Quick system status checker
echo "🔍 Cable Operator CRM - System Status Check"
echo "=============================================="

# Check if backend is running
echo -n "Backend (Port 8000): "
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not running"
fi

# Check if frontend is running
echo -n "Frontend (Port 3000): "
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not running"
fi

# Check Docker services
echo -n "Docker Services: "
if docker-compose ps 2>/dev/null | grep -q "Up"; then
    echo "✅ Running"
    echo ""
    echo "Docker Services Status:"
    docker-compose ps
else
    echo "❌ Not running"
fi

echo ""
echo "📋 Quick Access URLs:"
echo "  Frontend: http://localhost:3000"
echo "  Backend API: http://localhost:8000"
echo "  API Docs: http://localhost:8000/docs"
echo "  Login: <EMAIL> / admin123"
