{"ast": null, "code": "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { calcSVGTransformOrigin } from './transform-origin.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, {\n  attrX,\n  attrY,\n  originX,\n  originY,\n  pathLength,\n  pathSpacing = 1,\n  pathOffset = 0,\n  // This is object creation, which we try to avoid per-frame.\n  ...latest\n}, options, isSVGTag, transformTemplate) {\n  buildHTMLStyles(state, latest, options, transformTemplate);\n  /**\n   * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n   * as normal HTML tags.\n   */\n  if (isSVGTag) {\n    if (state.style.viewBox) {\n      state.attrs.viewBox = state.style.viewBox;\n    }\n    return;\n  }\n  state.attrs = state.style;\n  state.style = {};\n  const {\n    attrs,\n    style,\n    dimensions\n  } = state;\n  /**\n   * However, we apply transforms as CSS transforms. So if we detect a transform we take it from attrs\n   * and copy it into style.\n   */\n  if (attrs.transform) {\n    if (dimensions) style.transform = attrs.transform;\n    delete attrs.transform;\n  }\n  // Parse transformOrigin\n  if (dimensions && (originX !== undefined || originY !== undefined || style.transform)) {\n    style.transformOrigin = calcSVGTransformOrigin(dimensions, originX !== undefined ? originX : 0.5, originY !== undefined ? originY : 0.5);\n  }\n  // Treat x/y not as shortcuts but as actual attributes\n  if (attrX !== undefined) attrs.x = attrX;\n  if (attrY !== undefined) attrs.y = attrY;\n  // Build SVG path if one has been defined\n  if (pathLength !== undefined) {\n    buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n  }\n}\nexport { buildSVGAttrs };", "map": {"version": 3, "names": ["buildHTMLStyles", "calcSVGTransformOrigin", "buildSVGPath", "buildSVGAttrs", "state", "attrX", "attrY", "originX", "originY", "<PERSON><PERSON><PERSON><PERSON>", "pathSpacing", "pathOffset", "latest", "options", "isSVGTag", "transformTemplate", "style", "viewBox", "attrs", "dimensions", "transform", "undefined", "transform<PERSON><PERSON>in", "x", "y"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs"], "sourcesContent": ["import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { calcSVGTransformOrigin } from './transform-origin.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, originX, originY, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, options, isSVGTag, transformTemplate) {\n    buildHTMLStyles(state, latest, options, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style, dimensions } = state;\n    /**\n     * However, we apply transforms as CSS transforms. So if we detect a transform we take it from attrs\n     * and copy it into style.\n     */\n    if (attrs.transform) {\n        if (dimensions)\n            style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    // Parse transformOrigin\n    if (dimensions &&\n        (originX !== undefined || originY !== undefined || style.transform)) {\n        style.transformOrigin = calcSVGTransformOrigin(dimensions, originX !== undefined ? originX : 0.5, originY !== undefined ? originY : 0.5);\n    }\n    // Treat x/y not as shortcuts but as actual attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mCAAmC;AACnE,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SAASC,YAAY,QAAQ,YAAY;;AAEzC;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAAEC,KAAK;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,UAAU;EAAEC,WAAW,GAAG,CAAC;EAAEC,UAAU,GAAG,CAAC;EAC3G;EACA,GAAGC;AAAO,CAAC,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;EAC/Cf,eAAe,CAACI,KAAK,EAAEQ,MAAM,EAAEC,OAAO,EAAEE,iBAAiB,CAAC;EAC1D;AACJ;AACA;AACA;EACI,IAAID,QAAQ,EAAE;IACV,IAAIV,KAAK,CAACY,KAAK,CAACC,OAAO,EAAE;MACrBb,KAAK,CAACc,KAAK,CAACD,OAAO,GAAGb,KAAK,CAACY,KAAK,CAACC,OAAO;IAC7C;IACA;EACJ;EACAb,KAAK,CAACc,KAAK,GAAGd,KAAK,CAACY,KAAK;EACzBZ,KAAK,CAACY,KAAK,GAAG,CAAC,CAAC;EAChB,MAAM;IAAEE,KAAK;IAAEF,KAAK;IAAEG;EAAW,CAAC,GAAGf,KAAK;EAC1C;AACJ;AACA;AACA;EACI,IAAIc,KAAK,CAACE,SAAS,EAAE;IACjB,IAAID,UAAU,EACVH,KAAK,CAACI,SAAS,GAAGF,KAAK,CAACE,SAAS;IACrC,OAAOF,KAAK,CAACE,SAAS;EAC1B;EACA;EACA,IAAID,UAAU,KACTZ,OAAO,KAAKc,SAAS,IAAIb,OAAO,KAAKa,SAAS,IAAIL,KAAK,CAACI,SAAS,CAAC,EAAE;IACrEJ,KAAK,CAACM,eAAe,GAAGrB,sBAAsB,CAACkB,UAAU,EAAEZ,OAAO,KAAKc,SAAS,GAAGd,OAAO,GAAG,GAAG,EAAEC,OAAO,KAAKa,SAAS,GAAGb,OAAO,GAAG,GAAG,CAAC;EAC5I;EACA;EACA,IAAIH,KAAK,KAAKgB,SAAS,EACnBH,KAAK,CAACK,CAAC,GAAGlB,KAAK;EACnB,IAAIC,KAAK,KAAKe,SAAS,EACnBH,KAAK,CAACM,CAAC,GAAGlB,KAAK;EACnB;EACA,IAAIG,UAAU,KAAKY,SAAS,EAAE;IAC1BnB,YAAY,CAACgB,KAAK,EAAET,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE,KAAK,CAAC;EACnE;AACJ;AAEA,SAASR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}