{"ast": null, "code": "const isNumber = value => typeof value === \"number\";\nexport { isNumber };", "map": {"version": 3, "names": ["isNumber", "value"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/is-number.es.js"], "sourcesContent": ["const isNumber = (value) => typeof value === \"number\";\n\nexport { isNumber };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ;AAErD,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}