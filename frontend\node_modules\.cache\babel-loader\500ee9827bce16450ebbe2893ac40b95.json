{"ast": null, "code": "import { env } from '../../../utils/process.mjs';\nimport { useRef, useEffect } from 'react';\nimport { AnimationType } from '../../../render/utils/types.mjs';\nimport { warnOnce } from '../../../utils/warn-once.mjs';\nimport { observeIntersection } from './observers.mjs';\nfunction useViewport({\n  visualElement,\n  whileInView,\n  onViewportEnter,\n  onViewportLeave,\n  viewport = {}\n}) {\n  const state = useRef({\n    hasEnteredView: false,\n    isInView: false\n  });\n  let shouldObserve = Boolean(whileInView || onViewportEnter || onViewportLeave);\n  if (viewport.once && state.current.hasEnteredView) shouldObserve = false;\n  const useObserver = typeof IntersectionObserver === \"undefined\" ? useMissingIntersectionObserver : useIntersectionObserver;\n  useObserver(shouldObserve, state.current, visualElement, viewport);\n}\nconst thresholdNames = {\n  some: 0,\n  all: 1\n};\nfunction useIntersectionObserver(shouldObserve, state, visualElement, {\n  root,\n  margin: rootMargin,\n  amount = \"some\",\n  once\n}) {\n  useEffect(() => {\n    if (!shouldObserve || !visualElement.current) return;\n    const options = {\n      root: root === null || root === void 0 ? void 0 : root.current,\n      rootMargin,\n      threshold: typeof amount === \"number\" ? amount : thresholdNames[amount]\n    };\n    const intersectionCallback = entry => {\n      const {\n        isIntersecting\n      } = entry;\n      /**\n       * If there's been no change in the viewport state, early return.\n       */\n      if (state.isInView === isIntersecting) return;\n      state.isInView = isIntersecting;\n      /**\n       * Handle hasEnteredView. If this is only meant to run once, and\n       * element isn't visible, early return. Otherwise set hasEnteredView to true.\n       */\n      if (once && !isIntersecting && state.hasEnteredView) {\n        return;\n      } else if (isIntersecting) {\n        state.hasEnteredView = true;\n      }\n      if (visualElement.animationState) {\n        visualElement.animationState.setActive(AnimationType.InView, isIntersecting);\n      }\n      /**\n       * Use the latest committed props rather than the ones in scope\n       * when this observer is created\n       */\n      const props = visualElement.getProps();\n      const callback = isIntersecting ? props.onViewportEnter : props.onViewportLeave;\n      callback && callback(entry);\n    };\n    return observeIntersection(visualElement.current, options, intersectionCallback);\n  }, [shouldObserve, root, rootMargin, amount]);\n}\n/**\n * If IntersectionObserver is missing, we activate inView and fire onViewportEnter\n * on mount. This way, the page will be in the state the author expects users\n * to see it in for everyone.\n */\nfunction useMissingIntersectionObserver(shouldObserve, state, visualElement, {\n  fallback = true\n}) {\n  useEffect(() => {\n    if (!shouldObserve || !fallback) return;\n    if (env !== \"production\") {\n      warnOnce(false, \"IntersectionObserver not available on this device. whileInView animations will trigger on mount.\");\n    }\n    /**\n     * Fire this in an rAF because, at this point, the animation state\n     * won't have flushed for the first time and there's certain logic in\n     * there that behaves differently on the initial animation.\n     *\n     * This hook should be quite rarely called so setting this in an rAF\n     * is preferred to changing the behaviour of the animation state.\n     */\n    requestAnimationFrame(() => {\n      state.hasEnteredView = true;\n      const {\n        onViewportEnter\n      } = visualElement.getProps();\n      onViewportEnter && onViewportEnter(null);\n      if (visualElement.animationState) {\n        visualElement.animationState.setActive(AnimationType.InView, true);\n      }\n    });\n  }, [shouldObserve]);\n}\nexport { useViewport };", "map": {"version": 3, "names": ["env", "useRef", "useEffect", "AnimationType", "warnOnce", "observeIntersection", "useViewport", "visualElement", "whileInView", "onViewportEnter", "onViewportLeave", "viewport", "state", "hasEnteredView", "isInView", "shouldObserve", "Boolean", "once", "current", "useObserver", "IntersectionObserver", "useMissingIntersectionObserver", "useIntersectionObserver", "thresholdNames", "some", "all", "root", "margin", "rootMargin", "amount", "options", "threshold", "intersectionCallback", "entry", "isIntersecting", "animationState", "setActive", "InView", "props", "getProps", "callback", "fallback", "requestAnimationFrame"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/features/viewport/use-viewport.mjs"], "sourcesContent": ["import { env } from '../../../utils/process.mjs';\nimport { useRef, useEffect } from 'react';\nimport { AnimationType } from '../../../render/utils/types.mjs';\nimport { warnOnce } from '../../../utils/warn-once.mjs';\nimport { observeIntersection } from './observers.mjs';\n\nfunction useViewport({ visualElement, whileInView, onViewportEnter, onViewportLeave, viewport = {}, }) {\n    const state = useRef({\n        hasEnteredView: false,\n        isInView: false,\n    });\n    let shouldObserve = Boolean(whileInView || onViewportEnter || onViewportLeave);\n    if (viewport.once && state.current.hasEnteredView)\n        shouldObserve = false;\n    const useObserver = typeof IntersectionObserver === \"undefined\"\n        ? useMissingIntersectionObserver\n        : useIntersectionObserver;\n    useObserver(shouldObserve, state.current, visualElement, viewport);\n}\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nfunction useIntersectionObserver(shouldObserve, state, visualElement, { root, margin: rootMargin, amount = \"some\", once }) {\n    useEffect(() => {\n        if (!shouldObserve || !visualElement.current)\n            return;\n        const options = {\n            root: root === null || root === void 0 ? void 0 : root.current,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const intersectionCallback = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (state.isInView === isIntersecting)\n                return;\n            state.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && state.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                state.hasEnteredView = true;\n            }\n            if (visualElement.animationState) {\n                visualElement.animationState.setActive(AnimationType.InView, isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const props = visualElement.getProps();\n            const callback = isIntersecting\n                ? props.onViewportEnter\n                : props.onViewportLeave;\n            callback && callback(entry);\n        };\n        return observeIntersection(visualElement.current, options, intersectionCallback);\n    }, [shouldObserve, root, rootMargin, amount]);\n}\n/**\n * If IntersectionObserver is missing, we activate inView and fire onViewportEnter\n * on mount. This way, the page will be in the state the author expects users\n * to see it in for everyone.\n */\nfunction useMissingIntersectionObserver(shouldObserve, state, visualElement, { fallback = true }) {\n    useEffect(() => {\n        if (!shouldObserve || !fallback)\n            return;\n        if (env !== \"production\") {\n            warnOnce(false, \"IntersectionObserver not available on this device. whileInView animations will trigger on mount.\");\n        }\n        /**\n         * Fire this in an rAF because, at this point, the animation state\n         * won't have flushed for the first time and there's certain logic in\n         * there that behaves differently on the initial animation.\n         *\n         * This hook should be quite rarely called so setting this in an rAF\n         * is preferred to changing the behaviour of the animation state.\n         */\n        requestAnimationFrame(() => {\n            state.hasEnteredView = true;\n            const { onViewportEnter } = visualElement.getProps();\n            onViewportEnter && onViewportEnter(null);\n            if (visualElement.animationState) {\n                visualElement.animationState.setActive(AnimationType.InView, true);\n            }\n        });\n    }, [shouldObserve]);\n}\n\nexport { useViewport };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,4BAA4B;AAChD,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,mBAAmB,QAAQ,iBAAiB;AAErD,SAASC,WAAWA,CAAC;EAAEC,aAAa;EAAEC,WAAW;EAAEC,eAAe;EAAEC,eAAe;EAAEC,QAAQ,GAAG,CAAC;AAAG,CAAC,EAAE;EACnG,MAAMC,KAAK,GAAGX,MAAM,CAAC;IACjBY,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,IAAIC,aAAa,GAAGC,OAAO,CAACR,WAAW,IAAIC,eAAe,IAAIC,eAAe,CAAC;EAC9E,IAAIC,QAAQ,CAACM,IAAI,IAAIL,KAAK,CAACM,OAAO,CAACL,cAAc,EAC7CE,aAAa,GAAG,KAAK;EACzB,MAAMI,WAAW,GAAG,OAAOC,oBAAoB,KAAK,WAAW,GACzDC,8BAA8B,GAC9BC,uBAAuB;EAC7BH,WAAW,CAACJ,aAAa,EAAEH,KAAK,CAACM,OAAO,EAAEX,aAAa,EAAEI,QAAQ,CAAC;AACtE;AACA,MAAMY,cAAc,GAAG;EACnBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE;AACT,CAAC;AACD,SAASH,uBAAuBA,CAACP,aAAa,EAAEH,KAAK,EAAEL,aAAa,EAAE;EAAEmB,IAAI;EAAEC,MAAM,EAAEC,UAAU;EAAEC,MAAM,GAAG,MAAM;EAAEZ;AAAK,CAAC,EAAE;EACvHf,SAAS,CAAC,MAAM;IACZ,IAAI,CAACa,aAAa,IAAI,CAACR,aAAa,CAACW,OAAO,EACxC;IACJ,MAAMY,OAAO,GAAG;MACZJ,IAAI,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACR,OAAO;MAC9DU,UAAU;MACVG,SAAS,EAAE,OAAOF,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGN,cAAc,CAACM,MAAM;IAC1E,CAAC;IACD,MAAMG,oBAAoB,GAAIC,KAAK,IAAK;MACpC,MAAM;QAAEC;MAAe,CAAC,GAAGD,KAAK;MAChC;AACZ;AACA;MACY,IAAIrB,KAAK,CAACE,QAAQ,KAAKoB,cAAc,EACjC;MACJtB,KAAK,CAACE,QAAQ,GAAGoB,cAAc;MAC/B;AACZ;AACA;AACA;MACY,IAAIjB,IAAI,IAAI,CAACiB,cAAc,IAAItB,KAAK,CAACC,cAAc,EAAE;QACjD;MACJ,CAAC,MACI,IAAIqB,cAAc,EAAE;QACrBtB,KAAK,CAACC,cAAc,GAAG,IAAI;MAC/B;MACA,IAAIN,aAAa,CAAC4B,cAAc,EAAE;QAC9B5B,aAAa,CAAC4B,cAAc,CAACC,SAAS,CAACjC,aAAa,CAACkC,MAAM,EAAEH,cAAc,CAAC;MAChF;MACA;AACZ;AACA;AACA;MACY,MAAMI,KAAK,GAAG/B,aAAa,CAACgC,QAAQ,CAAC,CAAC;MACtC,MAAMC,QAAQ,GAAGN,cAAc,GACzBI,KAAK,CAAC7B,eAAe,GACrB6B,KAAK,CAAC5B,eAAe;MAC3B8B,QAAQ,IAAIA,QAAQ,CAACP,KAAK,CAAC;IAC/B,CAAC;IACD,OAAO5B,mBAAmB,CAACE,aAAa,CAACW,OAAO,EAAEY,OAAO,EAAEE,oBAAoB,CAAC;EACpF,CAAC,EAAE,CAACjB,aAAa,EAAEW,IAAI,EAAEE,UAAU,EAAEC,MAAM,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASR,8BAA8BA,CAACN,aAAa,EAAEH,KAAK,EAAEL,aAAa,EAAE;EAAEkC,QAAQ,GAAG;AAAK,CAAC,EAAE;EAC9FvC,SAAS,CAAC,MAAM;IACZ,IAAI,CAACa,aAAa,IAAI,CAAC0B,QAAQ,EAC3B;IACJ,IAAIzC,GAAG,KAAK,YAAY,EAAE;MACtBI,QAAQ,CAAC,KAAK,EAAE,kGAAkG,CAAC;IACvH;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQsC,qBAAqB,CAAC,MAAM;MACxB9B,KAAK,CAACC,cAAc,GAAG,IAAI;MAC3B,MAAM;QAAEJ;MAAgB,CAAC,GAAGF,aAAa,CAACgC,QAAQ,CAAC,CAAC;MACpD9B,eAAe,IAAIA,eAAe,CAAC,IAAI,CAAC;MACxC,IAAIF,aAAa,CAAC4B,cAAc,EAAE;QAC9B5B,aAAa,CAAC4B,cAAc,CAACC,SAAS,CAACjC,aAAa,CAACkC,MAAM,EAAE,IAAI,CAAC;MACtE;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACtB,aAAa,CAAC,CAAC;AACvB;AAEA,SAAST,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}