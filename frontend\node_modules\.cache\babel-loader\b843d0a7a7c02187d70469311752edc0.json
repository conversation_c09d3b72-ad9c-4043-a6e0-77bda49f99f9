{"ast": null, "code": "import { time } from '@motionone/utils';\nimport { calcGeneratorVelocity } from '../utils/velocity.es.js';\nimport { spring } from '../spring/index.es.js';\nconst glide = ({\n  from = 0,\n  velocity = 0.0,\n  power = 0.8,\n  decay = 0.325,\n  bounceDamping,\n  bounceStiffness,\n  changeTarget,\n  min,\n  max,\n  restDistance = 0.5,\n  restSpeed\n}) => {\n  decay = time.ms(decay);\n  const state = {\n    hasReachedTarget: false,\n    done: false,\n    current: from,\n    target: from\n  };\n  const isOutOfBounds = v => min !== undefined && v < min || max !== undefined && v > max;\n  const nearestBoundary = v => {\n    if (min === undefined) return max;\n    if (max === undefined) return min;\n    return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n  };\n  let amplitude = power * velocity;\n  const ideal = from + amplitude;\n  const target = changeTarget === undefined ? ideal : changeTarget(ideal);\n  state.target = target;\n  /**\n   * If the target has changed we need to re-calculate the amplitude, otherwise\n   * the animation will start from the wrong position.\n   */\n  if (target !== ideal) amplitude = target - from;\n  const calcDelta = t => -amplitude * Math.exp(-t / decay);\n  const calcLatest = t => target + calcDelta(t);\n  const applyFriction = t => {\n    const delta = calcDelta(t);\n    const latest = calcLatest(t);\n    state.done = Math.abs(delta) <= restDistance;\n    state.current = state.done ? target : latest;\n  };\n  /**\n   * Ideally this would resolve for t in a stateless way, we could\n   * do that by always precalculating the animation but as we know\n   * this will be done anyway we can assume that spring will\n   * be discovered during that.\n   */\n  let timeReachedBoundary;\n  let spring$1;\n  const checkCatchBoundary = t => {\n    if (!isOutOfBounds(state.current)) return;\n    timeReachedBoundary = t;\n    spring$1 = spring({\n      from: state.current,\n      to: nearestBoundary(state.current),\n      velocity: calcGeneratorVelocity(calcLatest, t, state.current),\n      // TODO: This should be passing * 1000\n      damping: bounceDamping,\n      stiffness: bounceStiffness,\n      restDistance,\n      restSpeed\n    });\n  };\n  checkCatchBoundary(0);\n  return t => {\n    /**\n     * We need to resolve the friction to figure out if we need a\n     * spring but we don't want to do this twice per frame. So here\n     * we flag if we updated for this frame and later if we did\n     * we can skip doing it again.\n     */\n    let hasUpdatedFrame = false;\n    if (!spring$1 && timeReachedBoundary === undefined) {\n      hasUpdatedFrame = true;\n      applyFriction(t);\n      checkCatchBoundary(t);\n    }\n    /**\n     * If we have a spring and the provided t is beyond the moment the friction\n     * animation crossed the min/max boundary, use the spring.\n     */\n    if (timeReachedBoundary !== undefined && t > timeReachedBoundary) {\n      state.hasReachedTarget = true;\n      return spring$1(t - timeReachedBoundary);\n    } else {\n      state.hasReachedTarget = false;\n      !hasUpdatedFrame && applyFriction(t);\n      return state;\n    }\n  };\n};\nexport { glide };", "map": {"version": 3, "names": ["time", "calcGeneratorVelocity", "spring", "glide", "from", "velocity", "power", "decay", "bounceDamping", "bounceStiffness", "change<PERSON>arget", "min", "max", "restDistance", "restSpeed", "ms", "state", "hasReached<PERSON><PERSON><PERSON>", "done", "current", "target", "isOutOfBounds", "v", "undefined", "nearestBoundary", "Math", "abs", "amplitude", "ideal", "calcDelta", "t", "exp", "calcLatest", "applyFriction", "delta", "latest", "timeReached<PERSON><PERSON><PERSON><PERSON>", "spring$1", "checkCatchBoundary", "to", "damping", "stiffness", "hasUpdatedFrame"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/generators/dist/glide/index.es.js"], "sourcesContent": ["import { time } from '@motionone/utils';\nimport { calcGeneratorVelocity } from '../utils/velocity.es.js';\nimport { spring } from '../spring/index.es.js';\n\nconst glide = ({ from = 0, velocity = 0.0, power = 0.8, decay = 0.325, bounceDamping, bounceStiffness, changeTarget, min, max, restDistance = 0.5, restSpeed, }) => {\n    decay = time.ms(decay);\n    const state = {\n        hasReachedTarget: false,\n        done: false,\n        current: from,\n        target: from,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = from + amplitude;\n    const target = changeTarget === undefined ? ideal : changeTarget(ideal);\n    state.target = target;\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - from;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / decay);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDistance;\n        state.current = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.current))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = spring({\n            from: state.current,\n            to: nearestBoundary(state.current),\n            velocity: calcGeneratorVelocity(calcLatest, t, state.current), // TODO: This should be passing * 1000\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDistance,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return (t) => {\n        /**\n         * We need to resolve the friction to figure out if we need a\n         * spring but we don't want to do this twice per frame. So here\n         * we flag if we updated for this frame and later if we did\n         * we can skip doing it again.\n         */\n        let hasUpdatedFrame = false;\n        if (!spring$1 && timeReachedBoundary === undefined) {\n            hasUpdatedFrame = true;\n            applyFriction(t);\n            checkCatchBoundary(t);\n        }\n        /**\n         * If we have a spring and the provided t is beyond the moment the friction\n         * animation crossed the min/max boundary, use the spring.\n         */\n        if (timeReachedBoundary !== undefined && t > timeReachedBoundary) {\n            state.hasReachedTarget = true;\n            return spring$1(t - timeReachedBoundary);\n        }\n        else {\n            state.hasReachedTarget = false;\n            !hasUpdatedFrame && applyFriction(t);\n            return state;\n        }\n    };\n};\n\nexport { glide };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,MAAM,QAAQ,uBAAuB;AAE9C,MAAMC,KAAK,GAAGA,CAAC;EAAEC,IAAI,GAAG,CAAC;EAAEC,QAAQ,GAAG,GAAG;EAAEC,KAAK,GAAG,GAAG;EAAEC,KAAK,GAAG,KAAK;EAAEC,aAAa;EAAEC,eAAe;EAAEC,YAAY;EAAEC,GAAG;EAAEC,GAAG;EAAEC,YAAY,GAAG,GAAG;EAAEC;AAAW,CAAC,KAAK;EAChKP,KAAK,GAAGP,IAAI,CAACe,EAAE,CAACR,KAAK,CAAC;EACtB,MAAMS,KAAK,GAAG;IACVC,gBAAgB,EAAE,KAAK;IACvBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAEf,IAAI;IACbgB,MAAM,EAAEhB;EACZ,CAAC;EACD,MAAMiB,aAAa,GAAIC,CAAC,IAAMX,GAAG,KAAKY,SAAS,IAAID,CAAC,GAAGX,GAAG,IAAMC,GAAG,KAAKW,SAAS,IAAID,CAAC,GAAGV,GAAI;EAC7F,MAAMY,eAAe,GAAIF,CAAC,IAAK;IAC3B,IAAIX,GAAG,KAAKY,SAAS,EACjB,OAAOX,GAAG;IACd,IAAIA,GAAG,KAAKW,SAAS,EACjB,OAAOZ,GAAG;IACd,OAAOc,IAAI,CAACC,GAAG,CAACf,GAAG,GAAGW,CAAC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACd,GAAG,GAAGU,CAAC,CAAC,GAAGX,GAAG,GAAGC,GAAG;EAC5D,CAAC;EACD,IAAIe,SAAS,GAAGrB,KAAK,GAAGD,QAAQ;EAChC,MAAMuB,KAAK,GAAGxB,IAAI,GAAGuB,SAAS;EAC9B,MAAMP,MAAM,GAAGV,YAAY,KAAKa,SAAS,GAAGK,KAAK,GAAGlB,YAAY,CAACkB,KAAK,CAAC;EACvEZ,KAAK,CAACI,MAAM,GAAGA,MAAM;EACrB;AACJ;AACA;AACA;EACI,IAAIA,MAAM,KAAKQ,KAAK,EAChBD,SAAS,GAAGP,MAAM,GAAGhB,IAAI;EAC7B,MAAMyB,SAAS,GAAIC,CAAC,IAAK,CAACH,SAAS,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACD,CAAC,GAAGvB,KAAK,CAAC;EAC1D,MAAMyB,UAAU,GAAIF,CAAC,IAAKV,MAAM,GAAGS,SAAS,CAACC,CAAC,CAAC;EAC/C,MAAMG,aAAa,GAAIH,CAAC,IAAK;IACzB,MAAMI,KAAK,GAAGL,SAAS,CAACC,CAAC,CAAC;IAC1B,MAAMK,MAAM,GAAGH,UAAU,CAACF,CAAC,CAAC;IAC5Bd,KAAK,CAACE,IAAI,GAAGO,IAAI,CAACC,GAAG,CAACQ,KAAK,CAAC,IAAIrB,YAAY;IAC5CG,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACE,IAAI,GAAGE,MAAM,GAAGe,MAAM;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmB;EACvB,IAAIC,QAAQ;EACZ,MAAMC,kBAAkB,GAAIR,CAAC,IAAK;IAC9B,IAAI,CAACT,aAAa,CAACL,KAAK,CAACG,OAAO,CAAC,EAC7B;IACJiB,mBAAmB,GAAGN,CAAC;IACvBO,QAAQ,GAAGnC,MAAM,CAAC;MACdE,IAAI,EAAEY,KAAK,CAACG,OAAO;MACnBoB,EAAE,EAAEf,eAAe,CAACR,KAAK,CAACG,OAAO,CAAC;MAClCd,QAAQ,EAAEJ,qBAAqB,CAAC+B,UAAU,EAAEF,CAAC,EAAEd,KAAK,CAACG,OAAO,CAAC;MAAE;MAC/DqB,OAAO,EAAEhC,aAAa;MACtBiC,SAAS,EAAEhC,eAAe;MAC1BI,YAAY;MACZC;IACJ,CAAC,CAAC;EACN,CAAC;EACDwB,kBAAkB,CAAC,CAAC,CAAC;EACrB,OAAQR,CAAC,IAAK;IACV;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIY,eAAe,GAAG,KAAK;IAC3B,IAAI,CAACL,QAAQ,IAAID,mBAAmB,KAAKb,SAAS,EAAE;MAChDmB,eAAe,GAAG,IAAI;MACtBT,aAAa,CAACH,CAAC,CAAC;MAChBQ,kBAAkB,CAACR,CAAC,CAAC;IACzB;IACA;AACR;AACA;AACA;IACQ,IAAIM,mBAAmB,KAAKb,SAAS,IAAIO,CAAC,GAAGM,mBAAmB,EAAE;MAC9DpB,KAAK,CAACC,gBAAgB,GAAG,IAAI;MAC7B,OAAOoB,QAAQ,CAACP,CAAC,GAAGM,mBAAmB,CAAC;IAC5C,CAAC,MACI;MACDpB,KAAK,CAACC,gBAAgB,GAAG,KAAK;MAC9B,CAACyB,eAAe,IAAIT,aAAa,CAACH,CAAC,CAAC;MACpC,OAAOd,KAAK;IAChB;EACJ,CAAC;AACL,CAAC;AAED,SAASb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}