{"ast": null, "code": "import { useRef } from 'react';\nimport { isNodeOrChild } from './utils/is-node-or-child.mjs';\nimport { usePointerEvent, addPointerEvent } from '../events/use-pointer-event.mjs';\nimport { useUnmountEffect } from '../utils/use-unmount-effect.mjs';\nimport { AnimationType } from '../render/utils/types.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { pipe } from '../utils/pipe.mjs';\n\n/**\n * @param handlers -\n * @internal\n */\nfunction useTapGesture({\n  onTap,\n  onTapStart,\n  onTapCancel,\n  whileTap,\n  visualElement\n}) {\n  const hasPressListeners = onTap || onTapStart || onTapCancel || whileTap;\n  const isPressing = useRef(false);\n  const cancelPointerEndListeners = useRef(null);\n  /**\n   * Only set listener to passive if there are no external listeners.\n   */\n  const eventOptions = {\n    passive: !(onTapStart || onTap || onTapCancel || onPointerDown)\n  };\n  function removePointerEndListener() {\n    cancelPointerEndListeners.current && cancelPointerEndListeners.current();\n    cancelPointerEndListeners.current = null;\n  }\n  function checkPointerEnd() {\n    removePointerEndListener();\n    isPressing.current = false;\n    visualElement.animationState && visualElement.animationState.setActive(AnimationType.Tap, false);\n    return !isDragActive();\n  }\n  function onPointerUp(event, info) {\n    if (!checkPointerEnd()) return;\n    /**\n     * We only count this as a tap gesture if the event.target is the same\n     * as, or a child of, this component's element\n     */\n    !isNodeOrChild(visualElement.current, event.target) ? onTapCancel && onTapCancel(event, info) : onTap && onTap(event, info);\n  }\n  function onPointerCancel(event, info) {\n    if (!checkPointerEnd()) return;\n    onTapCancel && onTapCancel(event, info);\n  }\n  function onPointerDown(event, info) {\n    removePointerEndListener();\n    if (isPressing.current) return;\n    isPressing.current = true;\n    cancelPointerEndListeners.current = pipe(addPointerEvent(window, \"pointerup\", onPointerUp, eventOptions), addPointerEvent(window, \"pointercancel\", onPointerCancel, eventOptions));\n    /**\n     * Ensure we trigger animations before firing event callback\n     */\n    visualElement.animationState && visualElement.animationState.setActive(AnimationType.Tap, true);\n    onTapStart && onTapStart(event, info);\n  }\n  usePointerEvent(visualElement, \"pointerdown\", hasPressListeners ? onPointerDown : undefined, eventOptions);\n  useUnmountEffect(removePointerEndListener);\n}\nexport { useTapGesture };", "map": {"version": 3, "names": ["useRef", "isNodeOrChild", "usePointerEvent", "addPointerEvent", "useUnmountEffect", "AnimationType", "isDragActive", "pipe", "useTapGesture", "onTap", "onTapStart", "onTapCancel", "whileTap", "visualElement", "hasPressListeners", "isPressing", "cancelPointerEndListeners", "eventOptions", "passive", "onPointerDown", "removePointerEndListener", "current", "checkPointerEnd", "animationState", "setActive", "Tap", "onPointerUp", "event", "info", "target", "onPointerCancel", "window", "undefined"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/use-tap-gesture.mjs"], "sourcesContent": ["import { useRef } from 'react';\nimport { isNodeOrChild } from './utils/is-node-or-child.mjs';\nimport { usePointerEvent, addPointerEvent } from '../events/use-pointer-event.mjs';\nimport { useUnmountEffect } from '../utils/use-unmount-effect.mjs';\nimport { AnimationType } from '../render/utils/types.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { pipe } from '../utils/pipe.mjs';\n\n/**\n * @param handlers -\n * @internal\n */\nfunction useTapGesture({ onTap, onTapStart, onTapCancel, whileTap, visualElement, }) {\n    const hasPressListeners = onTap || onTapStart || onTapCancel || whileTap;\n    const isPressing = useRef(false);\n    const cancelPointerEndListeners = useRef(null);\n    /**\n     * Only set listener to passive if there are no external listeners.\n     */\n    const eventOptions = {\n        passive: !(onTapStart || onTap || onTapCancel || onPointerDown),\n    };\n    function removePointerEndListener() {\n        cancelPointerEndListeners.current && cancelPointerEndListeners.current();\n        cancelPointerEndListeners.current = null;\n    }\n    function checkPointerEnd() {\n        removePointerEndListener();\n        isPressing.current = false;\n        visualElement.animationState &&\n            visualElement.animationState.setActive(AnimationType.Tap, false);\n        return !isDragActive();\n    }\n    function onPointerUp(event, info) {\n        if (!checkPointerEnd())\n            return;\n        /**\n         * We only count this as a tap gesture if the event.target is the same\n         * as, or a child of, this component's element\n         */\n        !isNodeOrChild(visualElement.current, event.target)\n            ? onTapCancel && onTapCancel(event, info)\n            : onTap && onTap(event, info);\n    }\n    function onPointerCancel(event, info) {\n        if (!checkPointerEnd())\n            return;\n        onTapCancel && onTapCancel(event, info);\n    }\n    function onPointerDown(event, info) {\n        removePointerEndListener();\n        if (isPressing.current)\n            return;\n        isPressing.current = true;\n        cancelPointerEndListeners.current = pipe(addPointerEvent(window, \"pointerup\", onPointerUp, eventOptions), addPointerEvent(window, \"pointercancel\", onPointerCancel, eventOptions));\n        /**\n         * Ensure we trigger animations before firing event callback\n         */\n        visualElement.animationState &&\n            visualElement.animationState.setActive(AnimationType.Tap, true);\n        onTapStart && onTapStart(event, info);\n    }\n    usePointerEvent(visualElement, \"pointerdown\", hasPressListeners ? onPointerDown : undefined, eventOptions);\n    useUnmountEffect(removePointerEndListener);\n}\n\nexport { useTapGesture };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,eAAe,EAAEC,eAAe,QAAQ,iCAAiC;AAClF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,IAAI,QAAQ,mBAAmB;;AAExC;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAC;EAAEC,KAAK;EAAEC,UAAU;EAAEC,WAAW;EAAEC,QAAQ;EAAEC;AAAe,CAAC,EAAE;EACjF,MAAMC,iBAAiB,GAAGL,KAAK,IAAIC,UAAU,IAAIC,WAAW,IAAIC,QAAQ;EACxE,MAAMG,UAAU,GAAGf,MAAM,CAAC,KAAK,CAAC;EAChC,MAAMgB,yBAAyB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC9C;AACJ;AACA;EACI,MAAMiB,YAAY,GAAG;IACjBC,OAAO,EAAE,EAAER,UAAU,IAAID,KAAK,IAAIE,WAAW,IAAIQ,aAAa;EAClE,CAAC;EACD,SAASC,wBAAwBA,CAAA,EAAG;IAChCJ,yBAAyB,CAACK,OAAO,IAAIL,yBAAyB,CAACK,OAAO,CAAC,CAAC;IACxEL,yBAAyB,CAACK,OAAO,GAAG,IAAI;EAC5C;EACA,SAASC,eAAeA,CAAA,EAAG;IACvBF,wBAAwB,CAAC,CAAC;IAC1BL,UAAU,CAACM,OAAO,GAAG,KAAK;IAC1BR,aAAa,CAACU,cAAc,IACxBV,aAAa,CAACU,cAAc,CAACC,SAAS,CAACnB,aAAa,CAACoB,GAAG,EAAE,KAAK,CAAC;IACpE,OAAO,CAACnB,YAAY,CAAC,CAAC;EAC1B;EACA,SAASoB,WAAWA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC9B,IAAI,CAACN,eAAe,CAAC,CAAC,EAClB;IACJ;AACR;AACA;AACA;IACQ,CAACrB,aAAa,CAACY,aAAa,CAACQ,OAAO,EAAEM,KAAK,CAACE,MAAM,CAAC,GAC7ClB,WAAW,IAAIA,WAAW,CAACgB,KAAK,EAAEC,IAAI,CAAC,GACvCnB,KAAK,IAAIA,KAAK,CAACkB,KAAK,EAAEC,IAAI,CAAC;EACrC;EACA,SAASE,eAAeA,CAACH,KAAK,EAAEC,IAAI,EAAE;IAClC,IAAI,CAACN,eAAe,CAAC,CAAC,EAClB;IACJX,WAAW,IAAIA,WAAW,CAACgB,KAAK,EAAEC,IAAI,CAAC;EAC3C;EACA,SAAST,aAAaA,CAACQ,KAAK,EAAEC,IAAI,EAAE;IAChCR,wBAAwB,CAAC,CAAC;IAC1B,IAAIL,UAAU,CAACM,OAAO,EAClB;IACJN,UAAU,CAACM,OAAO,GAAG,IAAI;IACzBL,yBAAyB,CAACK,OAAO,GAAGd,IAAI,CAACJ,eAAe,CAAC4B,MAAM,EAAE,WAAW,EAAEL,WAAW,EAAET,YAAY,CAAC,EAAEd,eAAe,CAAC4B,MAAM,EAAE,eAAe,EAAED,eAAe,EAAEb,YAAY,CAAC,CAAC;IAClL;AACR;AACA;IACQJ,aAAa,CAACU,cAAc,IACxBV,aAAa,CAACU,cAAc,CAACC,SAAS,CAACnB,aAAa,CAACoB,GAAG,EAAE,IAAI,CAAC;IACnEf,UAAU,IAAIA,UAAU,CAACiB,KAAK,EAAEC,IAAI,CAAC;EACzC;EACA1B,eAAe,CAACW,aAAa,EAAE,aAAa,EAAEC,iBAAiB,GAAGK,aAAa,GAAGa,SAAS,EAAEf,YAAY,CAAC;EAC1Gb,gBAAgB,CAACgB,wBAAwB,CAAC;AAC9C;AAEA,SAASZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}