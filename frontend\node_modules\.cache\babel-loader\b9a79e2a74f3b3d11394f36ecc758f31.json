{"ast": null, "code": "'use strict';\n\nvar styleObject = require('./style-object.cjs.js');\nconst camelLetterToPipeLetter = letter => `-${letter.toLowerCase()}`;\nconst camelToPipeCase = str => str.replace(/[A-Z]/g, camelLetterToPipeLetter);\nfunction createStyleString(target = {}) {\n  const styles = styleObject.createStyles(target);\n  let style = \"\";\n  for (const key in styles) {\n    style += key.startsWith(\"--\") ? key : camelToPipeCase(key);\n    style += `: ${styles[key]}; `;\n  }\n  return style;\n}\nexports.createStyleString = createStyleString;", "map": {"version": 3, "names": ["styleObject", "require", "camelLetterToPipeLetter", "letter", "toLowerCase", "camelToPipeCase", "str", "replace", "createStyleString", "target", "styles", "createStyles", "style", "key", "startsWith", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/style-string.cjs.js"], "sourcesContent": ["'use strict';\n\nvar styleObject = require('./style-object.cjs.js');\n\nconst camelLetterToPipeLetter = (letter) => `-${letter.toLowerCase()}`;\nconst camelToPipeCase = (str) => str.replace(/[A-Z]/g, camelLetterToPipeLetter);\nfunction createStyleString(target = {}) {\n    const styles = styleObject.createStyles(target);\n    let style = \"\";\n    for (const key in styles) {\n        style += key.startsWith(\"--\") ? key : camelToPipeCase(key);\n        style += `: ${styles[key]}; `;\n    }\n    return style;\n}\n\nexports.createStyleString = createStyleString;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAElD,MAAMC,uBAAuB,GAAIC,MAAM,IAAK,IAAIA,MAAM,CAACC,WAAW,CAAC,CAAC,EAAE;AACtE,MAAMC,eAAe,GAAIC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,QAAQ,EAAEL,uBAAuB,CAAC;AAC/E,SAASM,iBAAiBA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EACpC,MAAMC,MAAM,GAAGV,WAAW,CAACW,YAAY,CAACF,MAAM,CAAC;EAC/C,IAAIG,KAAK,GAAG,EAAE;EACd,KAAK,MAAMC,GAAG,IAAIH,MAAM,EAAE;IACtBE,KAAK,IAAIC,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC,GAAGD,GAAG,GAAGR,eAAe,CAACQ,GAAG,CAAC;IAC1DD,KAAK,IAAI,KAAKF,MAAM,CAACG,GAAG,CAAC,IAAI;EACjC;EACA,OAAOD,KAAK;AAChB;AAEAG,OAAO,CAACP,iBAAiB,GAAGA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}