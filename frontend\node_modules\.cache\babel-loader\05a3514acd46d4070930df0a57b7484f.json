{"ast": null, "code": "'use strict';\n\nvar transforms = require('./transforms.cjs.js');\nconst isCssVar = name => name.startsWith(\"--\");\nconst registeredProperties = new Set();\nfunction registerCssVariable(name) {\n  if (registeredProperties.has(name)) return;\n  registeredProperties.add(name);\n  try {\n    const {\n      syntax,\n      initialValue\n    } = transforms.transformDefinitions.has(name) ? transforms.transformDefinitions.get(name) : {};\n    CSS.registerProperty({\n      name,\n      inherits: false,\n      syntax,\n      initialValue\n    });\n  } catch (e) {}\n}\nexports.isCssVar = isCssVar;\nexports.registerCssVariable = registerCssVariable;\nexports.registeredProperties = registeredProperties;", "map": {"version": 3, "names": ["transforms", "require", "isCssVar", "name", "startsWith", "registeredProperties", "Set", "registerCssVariable", "has", "add", "syntax", "initialValue", "transformDefinitions", "get", "CSS", "registerProperty", "inherits", "e", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/css-var.cjs.js"], "sourcesContent": ["'use strict';\n\nvar transforms = require('./transforms.cjs.js');\n\nconst isCssVar = (name) => name.startsWith(\"--\");\nconst registeredProperties = new Set();\nfunction registerCssVariable(name) {\n    if (registeredProperties.has(name))\n        return;\n    registeredProperties.add(name);\n    try {\n        const { syntax, initialValue } = transforms.transformDefinitions.has(name)\n            ? transforms.transformDefinitions.get(name)\n            : {};\n        CSS.registerProperty({\n            name,\n            inherits: false,\n            syntax,\n            initialValue,\n        });\n    }\n    catch (e) { }\n}\n\nexports.isCssVar = isCssVar;\nexports.registerCssVariable = registerCssVariable;\nexports.registeredProperties = registeredProperties;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE/C,MAAMC,QAAQ,GAAIC,IAAI,IAAKA,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;AAChD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACtC,SAASC,mBAAmBA,CAACJ,IAAI,EAAE;EAC/B,IAAIE,oBAAoB,CAACG,GAAG,CAACL,IAAI,CAAC,EAC9B;EACJE,oBAAoB,CAACI,GAAG,CAACN,IAAI,CAAC;EAC9B,IAAI;IACA,MAAM;MAAEO,MAAM;MAAEC;IAAa,CAAC,GAAGX,UAAU,CAACY,oBAAoB,CAACJ,GAAG,CAACL,IAAI,CAAC,GACpEH,UAAU,CAACY,oBAAoB,CAACC,GAAG,CAACV,IAAI,CAAC,GACzC,CAAC,CAAC;IACRW,GAAG,CAACC,gBAAgB,CAAC;MACjBZ,IAAI;MACJa,QAAQ,EAAE,KAAK;MACfN,MAAM;MACNC;IACJ,CAAC,CAAC;EACN,CAAC,CACD,OAAOM,CAAC,EAAE,CAAE;AAChB;AAEAC,OAAO,CAAChB,QAAQ,GAAGA,QAAQ;AAC3BgB,OAAO,CAACX,mBAAmB,GAAGA,mBAAmB;AACjDW,OAAO,CAACb,oBAAoB,GAAGA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}