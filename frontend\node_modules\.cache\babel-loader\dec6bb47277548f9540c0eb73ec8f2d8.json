{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nfunction eraseKeyframes(sequence, startTime, endTime) {\n  for (let i = 0; i < sequence.length; i++) {\n    const keyframe = sequence[i];\n    if (keyframe.at > startTime && keyframe.at < endTime) {\n      utils.removeItem(sequence, keyframe);\n      // If we remove this item we have to push the pointer back one\n      i--;\n    }\n  }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n  /**\n   * Erase every existing value between currentTime and targetTime,\n   * this will essentially splice this timeline into any currently\n   * defined ones.\n   */\n  eraseKeyframes(sequence, startTime, endTime);\n  for (let i = 0; i < keyframes.length; i++) {\n    sequence.push({\n      value: keyframes[i],\n      at: utils.mix(startTime, endTime, offset[i]),\n      easing: utils.getEasingForSegment(easing, i)\n    });\n  }\n}\nexports.addKeyframes = addKeyframes;\nexports.eraseKeyframes = eraseKeyframes;", "map": {"version": 3, "names": ["utils", "require", "eraseKeyframes", "sequence", "startTime", "endTime", "i", "length", "keyframe", "at", "removeItem", "addKeyframes", "keyframes", "easing", "offset", "push", "value", "mix", "getEasingForSegment", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/timeline/utils/edit.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\n\nfunction eraseKeyframes(sequence, startTime, endTime) {\n    for (let i = 0; i < sequence.length; i++) {\n        const keyframe = sequence[i];\n        if (keyframe.at > startTime && keyframe.at < endTime) {\n            utils.removeItem(sequence, keyframe);\n            // If we remove this item we have to push the pointer back one\n            i--;\n        }\n    }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n    /**\n     * Erase every existing value between currentTime and targetTime,\n     * this will essentially splice this timeline into any currently\n     * defined ones.\n     */\n    eraseKeyframes(sequence, startTime, endTime);\n    for (let i = 0; i < keyframes.length; i++) {\n        sequence.push({\n            value: keyframes[i],\n            at: utils.mix(startTime, endTime, offset[i]),\n            easing: utils.getEasingForSegment(easing, i),\n        });\n    }\n}\n\nexports.addKeyframes = addKeyframes;\nexports.eraseKeyframes = eraseKeyframes;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,SAASC,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAME,QAAQ,GAAGL,QAAQ,CAACG,CAAC,CAAC;IAC5B,IAAIE,QAAQ,CAACC,EAAE,GAAGL,SAAS,IAAII,QAAQ,CAACC,EAAE,GAAGJ,OAAO,EAAE;MAClDL,KAAK,CAACU,UAAU,CAACP,QAAQ,EAAEK,QAAQ,CAAC;MACpC;MACAF,CAAC,EAAE;IACP;EACJ;AACJ;AACA,SAASK,YAAYA,CAACR,QAAQ,EAAES,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEV,SAAS,EAAEC,OAAO,EAAE;EAC3E;AACJ;AACA;AACA;AACA;EACIH,cAAc,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,SAAS,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCH,QAAQ,CAACY,IAAI,CAAC;MACVC,KAAK,EAAEJ,SAAS,CAACN,CAAC,CAAC;MACnBG,EAAE,EAAET,KAAK,CAACiB,GAAG,CAACb,SAAS,EAAEC,OAAO,EAAES,MAAM,CAACR,CAAC,CAAC,CAAC;MAC5CO,MAAM,EAAEb,KAAK,CAACkB,mBAAmB,CAACL,MAAM,EAAEP,CAAC;IAC/C,CAAC,CAAC;EACN;AACJ;AAEAa,OAAO,CAACR,YAAY,GAAGA,YAAY;AACnCQ,OAAO,CAACjB,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script"}