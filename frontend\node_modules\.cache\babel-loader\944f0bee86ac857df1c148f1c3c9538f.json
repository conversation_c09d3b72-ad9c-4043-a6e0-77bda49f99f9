{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nvar animation = require('@motionone/animation');\nfunction stagger(duration = 0.1, {\n  start = 0,\n  from = 0,\n  easing\n} = {}) {\n  return (i, total) => {\n    const fromIndex = utils.isNumber(from) ? from : getFromIndex(from, total);\n    const distance = Math.abs(fromIndex - i);\n    let delay = duration * distance;\n    if (easing) {\n      const maxDelay = total * duration;\n      const easingFunction = animation.getEasingFunction(easing);\n      delay = easingFunction(delay / maxDelay) * maxDelay;\n    }\n    return start + delay;\n  };\n}\nfunction getFromIndex(from, total) {\n  if (from === \"first\") {\n    return 0;\n  } else {\n    const lastIndex = total - 1;\n    return from === \"last\" ? lastIndex : lastIndex / 2;\n  }\n}\nfunction resolveOption(option, i, total) {\n  return utils.isFunction(option) ? option(i, total) : option;\n}\nexports.getFromIndex = getFromIndex;\nexports.resolveOption = resolveOption;\nexports.stagger = stagger;", "map": {"version": 3, "names": ["utils", "require", "animation", "stagger", "duration", "start", "from", "easing", "i", "total", "fromIndex", "isNumber", "getFromIndex", "distance", "Math", "abs", "delay", "max<PERSON><PERSON><PERSON>", "easingFunction", "getEasingFunction", "lastIndex", "resolveOption", "option", "isFunction", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/utils/stagger.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\nvar animation = require('@motionone/animation');\n\nfunction stagger(duration = 0.1, { start = 0, from = 0, easing } = {}) {\n    return (i, total) => {\n        const fromIndex = utils.isNumber(from) ? from : getFromIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (easing) {\n            const maxDelay = total * duration;\n            const easingFunction = animation.getEasingFunction(easing);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return start + delay;\n    };\n}\nfunction getFromIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction resolveOption(option, i, total) {\n    return utils.isFunction(option) ? option(i, total) : option;\n}\n\nexports.getFromIndex = getFromIndex;\nexports.resolveOption = resolveOption;\nexports.stagger = stagger;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,SAAS,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAE/C,SAASE,OAAOA,CAACC,QAAQ,GAAG,GAAG,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,IAAI,GAAG,CAAC;EAAEC;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EACnE,OAAO,CAACC,CAAC,EAAEC,KAAK,KAAK;IACjB,MAAMC,SAAS,GAAGV,KAAK,CAACW,QAAQ,CAACL,IAAI,CAAC,GAAGA,IAAI,GAAGM,YAAY,CAACN,IAAI,EAAEG,KAAK,CAAC;IACzE,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACL,SAAS,GAAGF,CAAC,CAAC;IACxC,IAAIQ,KAAK,GAAGZ,QAAQ,GAAGS,QAAQ;IAC/B,IAAIN,MAAM,EAAE;MACR,MAAMU,QAAQ,GAAGR,KAAK,GAAGL,QAAQ;MACjC,MAAMc,cAAc,GAAGhB,SAAS,CAACiB,iBAAiB,CAACZ,MAAM,CAAC;MAC1DS,KAAK,GAAGE,cAAc,CAACF,KAAK,GAAGC,QAAQ,CAAC,GAAGA,QAAQ;IACvD;IACA,OAAOZ,KAAK,GAAGW,KAAK;EACxB,CAAC;AACL;AACA,SAASJ,YAAYA,CAACN,IAAI,EAAEG,KAAK,EAAE;EAC/B,IAAIH,IAAI,KAAK,OAAO,EAAE;IAClB,OAAO,CAAC;EACZ,CAAC,MACI;IACD,MAAMc,SAAS,GAAGX,KAAK,GAAG,CAAC;IAC3B,OAAOH,IAAI,KAAK,MAAM,GAAGc,SAAS,GAAGA,SAAS,GAAG,CAAC;EACtD;AACJ;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEd,CAAC,EAAEC,KAAK,EAAE;EACrC,OAAOT,KAAK,CAACuB,UAAU,CAACD,MAAM,CAAC,GAAGA,MAAM,CAACd,CAAC,EAAEC,KAAK,CAAC,GAAGa,MAAM;AAC/D;AAEAE,OAAO,CAACZ,YAAY,GAAGA,YAAY;AACnCY,OAAO,CAACH,aAAa,GAAGA,aAAa;AACrCG,OAAO,CAACrB,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}