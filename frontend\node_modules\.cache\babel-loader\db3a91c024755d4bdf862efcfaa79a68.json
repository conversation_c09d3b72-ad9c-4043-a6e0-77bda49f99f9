{"ast": null, "code": "import { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { transformProps } from '../html/utils/transform.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nclass SVGVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.isSVGTag = false;\n  }\n  getBaseTargetFromProps(props, key) {\n    return props[key];\n  }\n  readValueFromInstance(instance, key) {\n    var _a;\n    if (transformProps.has(key)) {\n      return ((_a = getDefaultValueType(key)) === null || _a === void 0 ? void 0 : _a.default) || 0;\n    }\n    key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n    return instance.getAttribute(key);\n  }\n  measureInstanceViewportBox() {\n    return createBox();\n  }\n  scrapeMotionValuesFromProps(props) {\n    return scrapeMotionValuesFromProps(props);\n  }\n  build(renderState, latestValues, options, props) {\n    buildSVGAttrs(renderState, latestValues, options, this.isSVGTag, props.transformTemplate);\n  }\n  renderInstance(instance, renderState, styleProp, projection) {\n    renderSVG(instance, renderState, styleProp, projection);\n  }\n  mount(instance) {\n    this.isSVGTag = isSVGTag(instance.tagName);\n    super.mount(instance);\n  }\n}\nexport { SVGVisualElement };", "map": {"version": 3, "names": ["scrapeMotionValuesFromProps", "DOMVisualElement", "buildSVGAttrs", "camelToDash", "camelCaseAttributes", "transformProps", "renderSVG", "getDefaultValueType", "createBox", "isSVGTag", "SVGVisualElement", "constructor", "arguments", "getBaseTargetFromProps", "props", "key", "readValueFromInstance", "instance", "_a", "has", "default", "getAttribute", "measureInstanceViewportBox", "build", "renderState", "latestValues", "options", "transformTemplate", "renderInstance", "styleProp", "projection", "mount", "tagName"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs"], "sourcesContent": ["import { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { transformProps } from '../html/utils/transform.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.isSVGTag = false;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        var _a;\n        if (transformProps.has(key)) {\n            return ((_a = getDefaultValueType(key)) === null || _a === void 0 ? void 0 : _a.default) || 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    measureInstanceViewportBox() {\n        return createBox();\n    }\n    scrapeMotionValuesFromProps(props) {\n        return scrapeMotionValuesFromProps(props);\n    }\n    build(renderState, latestValues, options, props) {\n        buildSVGAttrs(renderState, latestValues, options, this.isSVGTag, props.transformTemplate);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,gBAAgB,SAAST,gBAAgB,CAAC;EAC5CU,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACH,QAAQ,GAAG,KAAK;EACzB;EACAI,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACC,GAAG,CAAC;EACrB;EACAC,qBAAqBA,CAACC,QAAQ,EAAEF,GAAG,EAAE;IACjC,IAAIG,EAAE;IACN,IAAIb,cAAc,CAACc,GAAG,CAACJ,GAAG,CAAC,EAAE;MACzB,OAAO,CAAC,CAACG,EAAE,GAAGX,mBAAmB,CAACQ,GAAG,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,OAAO,KAAK,CAAC;IACjG;IACAL,GAAG,GAAG,CAACX,mBAAmB,CAACe,GAAG,CAACJ,GAAG,CAAC,GAAGZ,WAAW,CAACY,GAAG,CAAC,GAAGA,GAAG;IAC5D,OAAOE,QAAQ,CAACI,YAAY,CAACN,GAAG,CAAC;EACrC;EACAO,0BAA0BA,CAAA,EAAG;IACzB,OAAOd,SAAS,CAAC,CAAC;EACtB;EACAR,2BAA2BA,CAACc,KAAK,EAAE;IAC/B,OAAOd,2BAA2B,CAACc,KAAK,CAAC;EAC7C;EACAS,KAAKA,CAACC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEZ,KAAK,EAAE;IAC7CZ,aAAa,CAACsB,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAE,IAAI,CAACjB,QAAQ,EAAEK,KAAK,CAACa,iBAAiB,CAAC;EAC7F;EACAC,cAAcA,CAACX,QAAQ,EAAEO,WAAW,EAAEK,SAAS,EAAEC,UAAU,EAAE;IACzDxB,SAAS,CAACW,QAAQ,EAAEO,WAAW,EAAEK,SAAS,EAAEC,UAAU,CAAC;EAC3D;EACAC,KAAKA,CAACd,QAAQ,EAAE;IACZ,IAAI,CAACR,QAAQ,GAAGA,QAAQ,CAACQ,QAAQ,CAACe,OAAO,CAAC;IAC1C,KAAK,CAACD,KAAK,CAACd,QAAQ,CAAC;EACzB;AACJ;AAEA,SAASP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}