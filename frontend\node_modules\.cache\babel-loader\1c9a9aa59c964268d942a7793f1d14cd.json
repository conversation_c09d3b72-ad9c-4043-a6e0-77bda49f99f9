{"ast": null, "code": "'use strict';\n\nfunction resolveElements(elements, selectorCache) {\n  var _a;\n  if (typeof elements === \"string\") {\n    if (selectorCache) {\n      (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : selectorCache[elements] = document.querySelectorAll(elements);\n      elements = selectorCache[elements];\n    } else {\n      elements = document.querySelectorAll(elements);\n    }\n  } else if (elements instanceof Element) {\n    elements = [elements];\n  }\n  /**\n   * Return an empty array\n   */\n  return Array.from(elements || []);\n}\nexports.resolveElements = resolveElements;", "map": {"version": 3, "names": ["resolveElements", "elements", "selectorCache", "_a", "document", "querySelectorAll", "Element", "Array", "from", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/utils/resolve-elements.cjs.js"], "sourcesContent": ["'use strict';\n\nfunction resolveElements(elements, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = document.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = document.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\nexports.resolveElements = resolveElements;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAeA,CAACC,QAAQ,EAAEC,aAAa,EAAE;EAC9C,IAAIC,EAAE;EACN,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAAE;IAC9B,IAAIC,aAAa,EAAE;MACf,CAACC,EAAE,GAAGD,aAAa,CAACD,QAAQ,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAID,aAAa,CAACD,QAAQ,CAAC,GAAGG,QAAQ,CAACC,gBAAgB,CAACJ,QAAQ,CAAE;MAC/HA,QAAQ,GAAGC,aAAa,CAACD,QAAQ,CAAC;IACtC,CAAC,MACI;MACDA,QAAQ,GAAGG,QAAQ,CAACC,gBAAgB,CAACJ,QAAQ,CAAC;IAClD;EACJ,CAAC,MACI,IAAIA,QAAQ,YAAYK,OAAO,EAAE;IAClCL,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACzB;EACA;AACJ;AACA;EACI,OAAOM,KAAK,CAACC,IAAI,CAACP,QAAQ,IAAI,EAAE,CAAC;AACrC;AAEAQ,OAAO,CAACT,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script"}