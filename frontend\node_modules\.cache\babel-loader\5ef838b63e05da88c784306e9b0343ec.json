{"ast": null, "code": "import { buildTransform } from './build-transform.mjs';\nimport { isCSSVariable } from '../../dom/utils/is-css-variable.mjs';\nimport { transformProps } from './transform.mjs';\nimport { getValueAsType } from '../../dom/value-types/get-as-type.mjs';\nimport { numberValueTypes } from '../../dom/value-types/number.mjs';\nfunction buildHTMLStyles(state, latestValues, options, transformTemplate) {\n  const {\n    style,\n    vars,\n    transform,\n    transformKeys,\n    transformOrigin\n  } = state;\n  transformKeys.length = 0;\n  // Track whether we encounter any transform or transformOrigin values.\n  let hasTransform = false;\n  let hasTransformOrigin = false;\n  // Does the calculated transform essentially equal \"none\"?\n  let transformIsNone = true;\n  /**\n   * Loop over all our latest animated values and decide whether to handle them\n   * as a style or CSS variable.\n   *\n   * Transforms and transform origins are kept seperately for further processing.\n   */\n  for (const key in latestValues) {\n    const value = latestValues[key];\n    /**\n     * If this is a CSS variable we don't do any further processing.\n     */\n    if (isCSSVariable(key)) {\n      vars[key] = value;\n      continue;\n    }\n    // Convert the value to its default value type, ie 0 -> \"0px\"\n    const valueType = numberValueTypes[key];\n    const valueAsType = getValueAsType(value, valueType);\n    if (transformProps.has(key)) {\n      // If this is a transform, flag to enable further transform processing\n      hasTransform = true;\n      transform[key] = valueAsType;\n      transformKeys.push(key);\n      // If we already know we have a non-default transform, early return\n      if (!transformIsNone) continue;\n      // Otherwise check to see if this is a default transform\n      if (value !== (valueType.default || 0)) transformIsNone = false;\n    } else if (key.startsWith(\"origin\")) {\n      // If this is a transform origin, flag and enable further transform-origin processing\n      hasTransformOrigin = true;\n      transformOrigin[key] = valueAsType;\n    } else {\n      style[key] = valueAsType;\n    }\n  }\n  if (!latestValues.transform) {\n    if (hasTransform || transformTemplate) {\n      style.transform = buildTransform(state, options, transformIsNone, transformTemplate);\n    } else if (style.transform) {\n      /**\n       * If we have previously created a transform but currently don't have any,\n       * reset transform style to none.\n       */\n      style.transform = \"none\";\n    }\n  }\n  /**\n   * Build a transformOrigin style. Uses the same defaults as the browser for\n   * undefined origins.\n   */\n  if (hasTransformOrigin) {\n    const {\n      originX = \"50%\",\n      originY = \"50%\",\n      originZ = 0\n    } = transformOrigin;\n    style.transformOrigin = `${originX} ${originY} ${originZ}`;\n  }\n}\nexport { buildHTMLStyles };", "map": {"version": 3, "names": ["buildTransform", "isCSSVariable", "transformProps", "getValueAsType", "numberValueTypes", "buildHTMLStyles", "state", "latestValues", "options", "transformTemplate", "style", "vars", "transform", "transformKeys", "transform<PERSON><PERSON>in", "length", "hasTransform", "hasTransformOrigin", "transformIsNone", "key", "value", "valueType", "valueAsType", "has", "push", "default", "startsWith", "originX", "originY", "originZ"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs"], "sourcesContent": ["import { buildTransform } from './build-transform.mjs';\nimport { isCSSVariable } from '../../dom/utils/is-css-variable.mjs';\nimport { transformProps } from './transform.mjs';\nimport { getValueAsType } from '../../dom/value-types/get-as-type.mjs';\nimport { numberValueTypes } from '../../dom/value-types/number.mjs';\n\nfunction buildHTMLStyles(state, latestValues, options, transformTemplate) {\n    const { style, vars, transform, transformKeys, transformOrigin } = state;\n    transformKeys.length = 0;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    // Does the calculated transform essentially equal \"none\"?\n    let transformIsNone = true;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept seperately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        /**\n         * If this is a CSS variable we don't do any further processing.\n         */\n        if (isCSSVariable(key)) {\n            vars[key] = value;\n            continue;\n        }\n        // Convert the value to its default value type, ie 0 -> \"0px\"\n        const valueType = numberValueTypes[key];\n        const valueAsType = getValueAsType(value, valueType);\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            transform[key] = valueAsType;\n            transformKeys.push(key);\n            // If we already know we have a non-default transform, early return\n            if (!transformIsNone)\n                continue;\n            // Otherwise check to see if this is a default transform\n            if (value !== (valueType.default || 0))\n                transformIsNone = false;\n        }\n        else if (key.startsWith(\"origin\")) {\n            // If this is a transform origin, flag and enable further transform-origin processing\n            hasTransformOrigin = true;\n            transformOrigin[key] = valueAsType;\n        }\n        else {\n            style[key] = valueAsType;\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(state, options, transformIsNone, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uBAAuB;AACtD,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,gBAAgB,QAAQ,kCAAkC;AAEnE,SAASC,eAAeA,CAACC,KAAK,EAAEC,YAAY,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;EACtE,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC,SAAS;IAAEC,aAAa;IAAEC;EAAgB,CAAC,GAAGR,KAAK;EACxEO,aAAa,CAACE,MAAM,GAAG,CAAC;EACxB;EACA,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,kBAAkB,GAAG,KAAK;EAC9B;EACA,IAAIC,eAAe,GAAG,IAAI;EAC1B;AACJ;AACA;AACA;AACA;AACA;EACI,KAAK,MAAMC,GAAG,IAAIZ,YAAY,EAAE;IAC5B,MAAMa,KAAK,GAAGb,YAAY,CAACY,GAAG,CAAC;IAC/B;AACR;AACA;IACQ,IAAIlB,aAAa,CAACkB,GAAG,CAAC,EAAE;MACpBR,IAAI,CAACQ,GAAG,CAAC,GAAGC,KAAK;MACjB;IACJ;IACA;IACA,MAAMC,SAAS,GAAGjB,gBAAgB,CAACe,GAAG,CAAC;IACvC,MAAMG,WAAW,GAAGnB,cAAc,CAACiB,KAAK,EAAEC,SAAS,CAAC;IACpD,IAAInB,cAAc,CAACqB,GAAG,CAACJ,GAAG,CAAC,EAAE;MACzB;MACAH,YAAY,GAAG,IAAI;MACnBJ,SAAS,CAACO,GAAG,CAAC,GAAGG,WAAW;MAC5BT,aAAa,CAACW,IAAI,CAACL,GAAG,CAAC;MACvB;MACA,IAAI,CAACD,eAAe,EAChB;MACJ;MACA,IAAIE,KAAK,MAAMC,SAAS,CAACI,OAAO,IAAI,CAAC,CAAC,EAClCP,eAAe,GAAG,KAAK;IAC/B,CAAC,MACI,IAAIC,GAAG,CAACO,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC/B;MACAT,kBAAkB,GAAG,IAAI;MACzBH,eAAe,CAACK,GAAG,CAAC,GAAGG,WAAW;IACtC,CAAC,MACI;MACDZ,KAAK,CAACS,GAAG,CAAC,GAAGG,WAAW;IAC5B;EACJ;EACA,IAAI,CAACf,YAAY,CAACK,SAAS,EAAE;IACzB,IAAII,YAAY,IAAIP,iBAAiB,EAAE;MACnCC,KAAK,CAACE,SAAS,GAAGZ,cAAc,CAACM,KAAK,EAAEE,OAAO,EAAEU,eAAe,EAAET,iBAAiB,CAAC;IACxF,CAAC,MACI,IAAIC,KAAK,CAACE,SAAS,EAAE;MACtB;AACZ;AACA;AACA;MACYF,KAAK,CAACE,SAAS,GAAG,MAAM;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIK,kBAAkB,EAAE;IACpB,MAAM;MAAEU,OAAO,GAAG,KAAK;MAAEC,OAAO,GAAG,KAAK;MAAEC,OAAO,GAAG;IAAG,CAAC,GAAGf,eAAe;IAC1EJ,KAAK,CAACI,eAAe,GAAG,GAAGa,OAAO,IAAIC,OAAO,IAAIC,OAAO,EAAE;EAC9D;AACJ;AAEA,SAASxB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}