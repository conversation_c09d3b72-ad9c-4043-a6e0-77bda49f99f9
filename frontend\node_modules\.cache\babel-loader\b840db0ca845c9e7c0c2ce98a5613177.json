{"ast": null, "code": "'use strict';\n\nvar types = require('@motionone/types');\nconst data = new WeakMap();\nfunction getAnimationData(element) {\n  if (!data.has(element)) {\n    data.set(element, {\n      transforms: [],\n      values: new Map()\n    });\n  }\n  return data.get(element);\n}\nfunction getMotionValue(motionValues, name) {\n  if (!motionValues.has(name)) {\n    motionValues.set(name, new types.MotionValue());\n  }\n  return motionValues.get(name);\n}\nexports.getAnimationData = getAnimationData;\nexports.getMotionValue = getMotionValue;", "map": {"version": 3, "names": ["types", "require", "data", "WeakMap", "getAnimationData", "element", "has", "set", "transforms", "values", "Map", "get", "getMotionValue", "motionValues", "name", "MotionValue", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/data.cjs.js"], "sourcesContent": ["'use strict';\n\nvar types = require('@motionone/types');\n\nconst data = new WeakMap();\nfunction getAnimationData(element) {\n    if (!data.has(element)) {\n        data.set(element, {\n            transforms: [],\n            values: new Map(),\n        });\n    }\n    return data.get(element);\n}\nfunction getMotionValue(motionValues, name) {\n    if (!motionValues.has(name)) {\n        motionValues.set(name, new types.MotionValue());\n    }\n    return motionValues.get(name);\n}\n\nexports.getAnimationData = getAnimationData;\nexports.getMotionValue = getMotionValue;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,MAAMC,IAAI,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC1B,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,IAAI,CAACH,IAAI,CAACI,GAAG,CAACD,OAAO,CAAC,EAAE;IACpBH,IAAI,CAACK,GAAG,CAACF,OAAO,EAAE;MACdG,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,IAAIC,GAAG,CAAC;IACpB,CAAC,CAAC;EACN;EACA,OAAOR,IAAI,CAACS,GAAG,CAACN,OAAO,CAAC;AAC5B;AACA,SAASO,cAAcA,CAACC,YAAY,EAAEC,IAAI,EAAE;EACxC,IAAI,CAACD,YAAY,CAACP,GAAG,CAACQ,IAAI,CAAC,EAAE;IACzBD,YAAY,CAACN,GAAG,CAACO,IAAI,EAAE,IAAId,KAAK,CAACe,WAAW,CAAC,CAAC,CAAC;EACnD;EACA,OAAOF,YAAY,CAACF,GAAG,CAACG,IAAI,CAAC;AACjC;AAEAE,OAAO,CAACZ,gBAAgB,GAAGA,gBAAgB;AAC3CY,OAAO,CAACJ,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script"}