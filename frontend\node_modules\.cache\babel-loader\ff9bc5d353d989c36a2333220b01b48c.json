{"ast": null, "code": "function isWaapiSupportedEasing(easing) {\n  return !easing ||\n  // Default easing\n  Array.isArray(easing) ||\n  // Bezier curve\n  typeof easing === \"string\" && supportedWaapiEasing[easing];\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n  linear: \"linear\",\n  ease: \"ease\",\n  easeIn: \"ease-in\",\n  easeOut: \"ease-out\",\n  easeInOut: \"ease-in-out\",\n  circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n  circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n  backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n  backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99])\n};\nfunction mapEasingToNativeEasing(easing) {\n  if (!easing) return undefined;\n  return Array.isArray(easing) ? cubicBezierAsString(easing) : supportedWaapiEasing[easing];\n}\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };", "map": {"version": 3, "names": ["isWaapiSupportedEasing", "easing", "Array", "isArray", "supportedWaapiEasing", "cubicBezierAsString", "a", "b", "c", "d", "linear", "ease", "easeIn", "easeOut", "easeInOut", "circIn", "circOut", "backIn", "backOut", "mapEasingToNativeEasing", "undefined"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/waapi/easing.mjs"], "sourcesContent": ["function isWaapiSupportedEasing(easing) {\n    return (!easing || // Default easing\n        Array.isArray(easing) || // Bezier curve\n        (typeof easing === \"string\" && supportedWaapiEasing[easing]));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing)\n        return undefined;\n    return Array.isArray(easing)\n        ? cubicBezierAsString(easing)\n        : supportedWaapiEasing[easing];\n}\n\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };\n"], "mappings": "AAAA,SAASA,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAQ,CAACA,MAAM;EAAI;EACfC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC;EAAI;EACxB,OAAOA,MAAM,KAAK,QAAQ,IAAIG,oBAAoB,CAACH,MAAM,CAAE;AACpE;AACA,MAAMI,mBAAmB,GAAGA,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,KAAK,gBAAgBH,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAAG;AACpF,MAAML,oBAAoB,GAAG;EACzBM,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,aAAa;EACxBC,MAAM,EAAEV,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/CW,OAAO,EAAEX,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAChDY,MAAM,EAAEZ,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;EACtDa,OAAO,EAAEb,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AACD,SAASc,uBAAuBA,CAAClB,MAAM,EAAE;EACrC,IAAI,CAACA,MAAM,EACP,OAAOmB,SAAS;EACpB,OAAOlB,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GACtBI,mBAAmB,CAACJ,MAAM,CAAC,GAC3BG,oBAAoB,CAACH,MAAM,CAAC;AACtC;AAEA,SAASI,mBAAmB,EAAEL,sBAAsB,EAAEmB,uBAAuB,EAAEf,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}