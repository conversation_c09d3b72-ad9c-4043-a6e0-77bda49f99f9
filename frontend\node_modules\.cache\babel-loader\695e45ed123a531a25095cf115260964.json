{"ast": null, "code": "'use strict';\n\nvar tslib = require('tslib');\nvar heyListen = require('hey-listen');\nvar utils = require('@motionone/utils');\nvar animateStyle = require('../animate/animate-style.cjs.js');\nvar style = require('../animate/style.cjs.js');\nvar options = require('../animate/utils/options.cjs.js');\nvar hasChanged = require('./utils/has-changed.cjs.js');\nvar resolveVariant = require('./utils/resolve-variant.cjs.js');\nvar schedule = require('./utils/schedule.cjs.js');\nvar inView = require('./gestures/in-view.cjs.js');\nvar hover = require('./gestures/hover.cjs.js');\nvar press = require('./gestures/press.cjs.js');\nvar events = require('./utils/events.cjs.js');\nvar animation = require('@motionone/animation');\nconst gestures = {\n  inView: inView.inView,\n  hover: hover.hover,\n  press: press.press\n};\n/**\n * A list of state types, in priority order. If a value is defined in\n * a righter-most type, it will override any definition in a lefter-most.\n */\nconst stateTypes = [\"initial\", \"animate\", ...Object.keys(gestures), \"exit\"];\n/**\n * A global store of all generated motion states. This can be used to lookup\n * a motion state for a given Element.\n */\nconst mountedStates = new WeakMap();\nfunction createMotionState(options$1 = {}, parent) {\n  /**\n   * The element represented by the motion state. This is an empty reference\n   * when we create the state to support SSR and allow for later mounting\n   * in view libraries.\n   *\n   * @ts-ignore\n   */\n  let element;\n  /**\n   * Calculate a depth that we can use to order motion states by tree depth.\n   */\n  let depth = parent ? parent.getDepth() + 1 : 0;\n  /**\n   * Track which states are currently active.\n   */\n  const activeStates = {\n    initial: true,\n    animate: true\n  };\n  /**\n   * A map of functions that, when called, will remove event listeners for\n   * a given gesture.\n   */\n  const gestureSubscriptions = {};\n  /**\n   * Initialise a context to share through motion states. This\n   * will be populated by variant names (if any).\n   */\n  const context = {};\n  for (const name of stateTypes) {\n    context[name] = typeof options$1[name] === \"string\" ? options$1[name] : parent === null || parent === void 0 ? void 0 : parent.getContext()[name];\n  }\n  /**\n   * If initial is set to false we use the animate prop as the initial\n   * animation state.\n   */\n  const initialVariantSource = options$1.initial === false ? \"animate\" : \"initial\";\n  /**\n   * Destructure an initial target out from the resolved initial variant.\n   */\n  let _a = resolveVariant.resolveVariant(options$1[initialVariantSource] || context[initialVariantSource], options$1.variants) || {},\n    target = tslib.__rest(_a, [\"transition\"]);\n  /**\n   * The base target is a cached map of values that we'll use to animate\n   * back to if a value is removed from all active state types. This\n   * is usually the initial value as read from the DOM, for instance if\n   * it hasn't been defined in initial.\n   */\n  const baseTarget = Object.assign({}, target);\n  /**\n   * A generator that will be processed by the global animation scheduler.\n   * This yields when it switches from reading the DOM to writing to it\n   * to prevent layout thrashing.\n   */\n  function* animateUpdates() {\n    var _a, _b;\n    const prevTarget = target;\n    target = {};\n    const animationOptions = {};\n    for (const name of stateTypes) {\n      if (!activeStates[name]) continue;\n      const variant = resolveVariant.resolveVariant(options$1[name]);\n      if (!variant) continue;\n      for (const key in variant) {\n        if (key === \"transition\") continue;\n        target[key] = variant[key];\n        animationOptions[key] = options.getOptions((_b = (_a = variant.transition) !== null && _a !== void 0 ? _a : options$1.transition) !== null && _b !== void 0 ? _b : {}, key);\n      }\n    }\n    const allTargetKeys = new Set([...Object.keys(target), ...Object.keys(prevTarget)]);\n    const animationFactories = [];\n    allTargetKeys.forEach(key => {\n      var _a;\n      if (target[key] === undefined) {\n        target[key] = baseTarget[key];\n      }\n      if (hasChanged.hasChanged(prevTarget[key], target[key])) {\n        (_a = baseTarget[key]) !== null && _a !== void 0 ? _a : baseTarget[key] = style.style.get(element, key);\n        animationFactories.push(animateStyle.animateStyle(element, key, target[key], animationOptions[key], animation.Animation));\n      }\n    });\n    // Wait for all animation states to read from the DOM\n    yield;\n    const animations = animationFactories.map(factory => factory()).filter(Boolean);\n    if (!animations.length) return;\n    const animationTarget = target;\n    element.dispatchEvent(events.motionEvent(\"motionstart\", animationTarget));\n    Promise.all(animations.map(animation => animation.finished)).then(() => {\n      element.dispatchEvent(events.motionEvent(\"motioncomplete\", animationTarget));\n    }).catch(utils.noop);\n  }\n  const setGesture = (name, isActive) => () => {\n    activeStates[name] = isActive;\n    schedule.scheduleAnimation(state);\n  };\n  const updateGestureSubscriptions = () => {\n    for (const name in gestures) {\n      const isGestureActive = gestures[name].isActive(options$1);\n      const remove = gestureSubscriptions[name];\n      if (isGestureActive && !remove) {\n        gestureSubscriptions[name] = gestures[name].subscribe(element, {\n          enable: setGesture(name, true),\n          disable: setGesture(name, false)\n        }, options$1);\n      } else if (!isGestureActive && remove) {\n        remove();\n        delete gestureSubscriptions[name];\n      }\n    }\n  };\n  const state = {\n    update: newOptions => {\n      if (!element) return;\n      options$1 = newOptions;\n      updateGestureSubscriptions();\n      schedule.scheduleAnimation(state);\n    },\n    setActive: (name, isActive) => {\n      if (!element) return;\n      activeStates[name] = isActive;\n      schedule.scheduleAnimation(state);\n    },\n    animateUpdates,\n    getDepth: () => depth,\n    getTarget: () => target,\n    getOptions: () => options$1,\n    getContext: () => context,\n    mount: newElement => {\n      heyListen.invariant(Boolean(newElement), \"Animation state must be mounted with valid Element\");\n      element = newElement;\n      mountedStates.set(element, state);\n      updateGestureSubscriptions();\n      return () => {\n        mountedStates.delete(element);\n        schedule.unscheduleAnimation(state);\n        for (const key in gestureSubscriptions) {\n          gestureSubscriptions[key]();\n        }\n      };\n    },\n    isMounted: () => Boolean(element)\n  };\n  return state;\n}\nexports.createMotionState = createMotionState;\nexports.mountedStates = mountedStates;", "map": {"version": 3, "names": ["tslib", "require", "heyListen", "utils", "animateStyle", "style", "options", "has<PERSON><PERSON>ed", "resolveV<PERSON>t", "schedule", "inView", "hover", "press", "events", "animation", "gestures", "stateTypes", "Object", "keys", "mountedStates", "WeakMap", "createMotionState", "options$1", "parent", "element", "depth", "<PERSON><PERSON><PERSON>h", "activeStates", "initial", "animate", "gestureSubscriptions", "context", "name", "getContext", "initialVariantSource", "_a", "variants", "target", "__rest", "baseTarget", "assign", "animateUpdates", "_b", "prevTarget", "animationOptions", "variant", "key", "getOptions", "transition", "allTargetKeys", "Set", "animationFactories", "for<PERSON>ach", "undefined", "get", "push", "Animation", "animations", "map", "factory", "filter", "Boolean", "length", "animationTarget", "dispatchEvent", "motionEvent", "Promise", "all", "finished", "then", "catch", "noop", "setGesture", "isActive", "scheduleAnimation", "state", "updateGestureSubscriptions", "isGestureActive", "remove", "subscribe", "enable", "disable", "update", "newOptions", "setActive", "get<PERSON><PERSON><PERSON>", "mount", "newElement", "invariant", "set", "delete", "unscheduleAnimation", "isMounted", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar tslib = require('tslib');\nvar heyListen = require('hey-listen');\nvar utils = require('@motionone/utils');\nvar animateStyle = require('../animate/animate-style.cjs.js');\nvar style = require('../animate/style.cjs.js');\nvar options = require('../animate/utils/options.cjs.js');\nvar hasChanged = require('./utils/has-changed.cjs.js');\nvar resolveVariant = require('./utils/resolve-variant.cjs.js');\nvar schedule = require('./utils/schedule.cjs.js');\nvar inView = require('./gestures/in-view.cjs.js');\nvar hover = require('./gestures/hover.cjs.js');\nvar press = require('./gestures/press.cjs.js');\nvar events = require('./utils/events.cjs.js');\nvar animation = require('@motionone/animation');\n\nconst gestures = { inView: inView.inView, hover: hover.hover, press: press.press };\n/**\n * A list of state types, in priority order. If a value is defined in\n * a righter-most type, it will override any definition in a lefter-most.\n */\nconst stateTypes = [\"initial\", \"animate\", ...Object.keys(gestures), \"exit\"];\n/**\n * A global store of all generated motion states. This can be used to lookup\n * a motion state for a given Element.\n */\nconst mountedStates = new WeakMap();\nfunction createMotionState(options$1 = {}, parent) {\n    /**\n     * The element represented by the motion state. This is an empty reference\n     * when we create the state to support SSR and allow for later mounting\n     * in view libraries.\n     *\n     * @ts-ignore\n     */\n    let element;\n    /**\n     * Calculate a depth that we can use to order motion states by tree depth.\n     */\n    let depth = parent ? parent.getDepth() + 1 : 0;\n    /**\n     * Track which states are currently active.\n     */\n    const activeStates = { initial: true, animate: true };\n    /**\n     * A map of functions that, when called, will remove event listeners for\n     * a given gesture.\n     */\n    const gestureSubscriptions = {};\n    /**\n     * Initialise a context to share through motion states. This\n     * will be populated by variant names (if any).\n     */\n    const context = {};\n    for (const name of stateTypes) {\n        context[name] =\n            typeof options$1[name] === \"string\"\n                ? options$1[name]\n                : parent === null || parent === void 0 ? void 0 : parent.getContext()[name];\n    }\n    /**\n     * If initial is set to false we use the animate prop as the initial\n     * animation state.\n     */\n    const initialVariantSource = options$1.initial === false ? \"animate\" : \"initial\";\n    /**\n     * Destructure an initial target out from the resolved initial variant.\n     */\n    let _a = resolveVariant.resolveVariant(options$1[initialVariantSource] || context[initialVariantSource], options$1.variants) || {}, target = tslib.__rest(_a, [\"transition\"]);\n    /**\n     * The base target is a cached map of values that we'll use to animate\n     * back to if a value is removed from all active state types. This\n     * is usually the initial value as read from the DOM, for instance if\n     * it hasn't been defined in initial.\n     */\n    const baseTarget = Object.assign({}, target);\n    /**\n     * A generator that will be processed by the global animation scheduler.\n     * This yields when it switches from reading the DOM to writing to it\n     * to prevent layout thrashing.\n     */\n    function* animateUpdates() {\n        var _a, _b;\n        const prevTarget = target;\n        target = {};\n        const animationOptions = {};\n        for (const name of stateTypes) {\n            if (!activeStates[name])\n                continue;\n            const variant = resolveVariant.resolveVariant(options$1[name]);\n            if (!variant)\n                continue;\n            for (const key in variant) {\n                if (key === \"transition\")\n                    continue;\n                target[key] = variant[key];\n                animationOptions[key] = options.getOptions((_b = (_a = variant.transition) !== null && _a !== void 0 ? _a : options$1.transition) !== null && _b !== void 0 ? _b : {}, key);\n            }\n        }\n        const allTargetKeys = new Set([\n            ...Object.keys(target),\n            ...Object.keys(prevTarget),\n        ]);\n        const animationFactories = [];\n        allTargetKeys.forEach((key) => {\n            var _a;\n            if (target[key] === undefined) {\n                target[key] = baseTarget[key];\n            }\n            if (hasChanged.hasChanged(prevTarget[key], target[key])) {\n                (_a = baseTarget[key]) !== null && _a !== void 0 ? _a : (baseTarget[key] = style.style.get(element, key));\n                animationFactories.push(animateStyle.animateStyle(element, key, target[key], animationOptions[key], animation.Animation));\n            }\n        });\n        // Wait for all animation states to read from the DOM\n        yield;\n        const animations = animationFactories\n            .map((factory) => factory())\n            .filter(Boolean);\n        if (!animations.length)\n            return;\n        const animationTarget = target;\n        element.dispatchEvent(events.motionEvent(\"motionstart\", animationTarget));\n        Promise.all(animations.map((animation) => animation.finished))\n            .then(() => {\n            element.dispatchEvent(events.motionEvent(\"motioncomplete\", animationTarget));\n        })\n            .catch(utils.noop);\n    }\n    const setGesture = (name, isActive) => () => {\n        activeStates[name] = isActive;\n        schedule.scheduleAnimation(state);\n    };\n    const updateGestureSubscriptions = () => {\n        for (const name in gestures) {\n            const isGestureActive = gestures[name].isActive(options$1);\n            const remove = gestureSubscriptions[name];\n            if (isGestureActive && !remove) {\n                gestureSubscriptions[name] = gestures[name].subscribe(element, {\n                    enable: setGesture(name, true),\n                    disable: setGesture(name, false),\n                }, options$1);\n            }\n            else if (!isGestureActive && remove) {\n                remove();\n                delete gestureSubscriptions[name];\n            }\n        }\n    };\n    const state = {\n        update: (newOptions) => {\n            if (!element)\n                return;\n            options$1 = newOptions;\n            updateGestureSubscriptions();\n            schedule.scheduleAnimation(state);\n        },\n        setActive: (name, isActive) => {\n            if (!element)\n                return;\n            activeStates[name] = isActive;\n            schedule.scheduleAnimation(state);\n        },\n        animateUpdates,\n        getDepth: () => depth,\n        getTarget: () => target,\n        getOptions: () => options$1,\n        getContext: () => context,\n        mount: (newElement) => {\n            heyListen.invariant(Boolean(newElement), \"Animation state must be mounted with valid Element\");\n            element = newElement;\n            mountedStates.set(element, state);\n            updateGestureSubscriptions();\n            return () => {\n                mountedStates.delete(element);\n                schedule.unscheduleAnimation(state);\n                for (const key in gestureSubscriptions) {\n                    gestureSubscriptions[key]();\n                }\n            };\n        },\n        isMounted: () => Boolean(element),\n    };\n    return state;\n}\n\nexports.createMotionState = createMotionState;\nexports.mountedStates = mountedStates;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIE,KAAK,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIG,YAAY,GAAGH,OAAO,CAAC,iCAAiC,CAAC;AAC7D,IAAII,KAAK,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AAC9C,IAAIK,OAAO,GAAGL,OAAO,CAAC,iCAAiC,CAAC;AACxD,IAAIM,UAAU,GAAGN,OAAO,CAAC,4BAA4B,CAAC;AACtD,IAAIO,cAAc,GAAGP,OAAO,CAAC,gCAAgC,CAAC;AAC9D,IAAIQ,QAAQ,GAAGR,OAAO,CAAC,yBAAyB,CAAC;AACjD,IAAIS,MAAM,GAAGT,OAAO,CAAC,2BAA2B,CAAC;AACjD,IAAIU,KAAK,GAAGV,OAAO,CAAC,yBAAyB,CAAC;AAC9C,IAAIW,KAAK,GAAGX,OAAO,CAAC,yBAAyB,CAAC;AAC9C,IAAIY,MAAM,GAAGZ,OAAO,CAAC,uBAAuB,CAAC;AAC7C,IAAIa,SAAS,GAAGb,OAAO,CAAC,sBAAsB,CAAC;AAE/C,MAAMc,QAAQ,GAAG;EAAEL,MAAM,EAAEA,MAAM,CAACA,MAAM;EAAEC,KAAK,EAAEA,KAAK,CAACA,KAAK;EAAEC,KAAK,EAAEA,KAAK,CAACA;AAAM,CAAC;AAClF;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,EAAE,MAAM,CAAC;AAC3E;AACA;AACA;AACA;AACA,MAAMI,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AACnC,SAASC,iBAAiBA,CAACC,SAAS,GAAG,CAAC,CAAC,EAAEC,MAAM,EAAE;EAC/C;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,OAAO;EACX;AACJ;AACA;EACI,IAAIC,KAAK,GAAGF,MAAM,GAAGA,MAAM,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9C;AACJ;AACA;EACI,MAAMC,YAAY,GAAG;IAAEC,OAAO,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAK,CAAC;EACrD;AACJ;AACA;AACA;EACI,MAAMC,oBAAoB,GAAG,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;EACI,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClB,KAAK,MAAMC,IAAI,IAAIhB,UAAU,EAAE;IAC3Be,OAAO,CAACC,IAAI,CAAC,GACT,OAAOV,SAAS,CAACU,IAAI,CAAC,KAAK,QAAQ,GAC7BV,SAAS,CAACU,IAAI,CAAC,GACfT,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACU,UAAU,CAAC,CAAC,CAACD,IAAI,CAAC;EACvF;EACA;AACJ;AACA;AACA;EACI,MAAME,oBAAoB,GAAGZ,SAAS,CAACM,OAAO,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;EAChF;AACJ;AACA;EACI,IAAIO,EAAE,GAAG3B,cAAc,CAACA,cAAc,CAACc,SAAS,CAACY,oBAAoB,CAAC,IAAIH,OAAO,CAACG,oBAAoB,CAAC,EAAEZ,SAAS,CAACc,QAAQ,CAAC,IAAI,CAAC,CAAC;IAAEC,MAAM,GAAGrC,KAAK,CAACsC,MAAM,CAACH,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EAC7K;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMI,UAAU,GAAGtB,MAAM,CAACuB,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EAC5C;AACJ;AACA;AACA;AACA;EACI,UAAUI,cAAcA,CAAA,EAAG;IACvB,IAAIN,EAAE,EAAEO,EAAE;IACV,MAAMC,UAAU,GAAGN,MAAM;IACzBA,MAAM,GAAG,CAAC,CAAC;IACX,MAAMO,gBAAgB,GAAG,CAAC,CAAC;IAC3B,KAAK,MAAMZ,IAAI,IAAIhB,UAAU,EAAE;MAC3B,IAAI,CAACW,YAAY,CAACK,IAAI,CAAC,EACnB;MACJ,MAAMa,OAAO,GAAGrC,cAAc,CAACA,cAAc,CAACc,SAAS,CAACU,IAAI,CAAC,CAAC;MAC9D,IAAI,CAACa,OAAO,EACR;MACJ,KAAK,MAAMC,GAAG,IAAID,OAAO,EAAE;QACvB,IAAIC,GAAG,KAAK,YAAY,EACpB;QACJT,MAAM,CAACS,GAAG,CAAC,GAAGD,OAAO,CAACC,GAAG,CAAC;QAC1BF,gBAAgB,CAACE,GAAG,CAAC,GAAGxC,OAAO,CAACyC,UAAU,CAAC,CAACL,EAAE,GAAG,CAACP,EAAE,GAAGU,OAAO,CAACG,UAAU,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGb,SAAS,CAAC0B,UAAU,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,EAAEI,GAAG,CAAC;MAC/K;IACJ;IACA,MAAMG,aAAa,GAAG,IAAIC,GAAG,CAAC,CAC1B,GAAGjC,MAAM,CAACC,IAAI,CAACmB,MAAM,CAAC,EACtB,GAAGpB,MAAM,CAACC,IAAI,CAACyB,UAAU,CAAC,CAC7B,CAAC;IACF,MAAMQ,kBAAkB,GAAG,EAAE;IAC7BF,aAAa,CAACG,OAAO,CAAEN,GAAG,IAAK;MAC3B,IAAIX,EAAE;MACN,IAAIE,MAAM,CAACS,GAAG,CAAC,KAAKO,SAAS,EAAE;QAC3BhB,MAAM,CAACS,GAAG,CAAC,GAAGP,UAAU,CAACO,GAAG,CAAC;MACjC;MACA,IAAIvC,UAAU,CAACA,UAAU,CAACoC,UAAU,CAACG,GAAG,CAAC,EAAET,MAAM,CAACS,GAAG,CAAC,CAAC,EAAE;QACrD,CAACX,EAAE,GAAGI,UAAU,CAACO,GAAG,CAAC,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAII,UAAU,CAACO,GAAG,CAAC,GAAGzC,KAAK,CAACA,KAAK,CAACiD,GAAG,CAAC9B,OAAO,EAAEsB,GAAG,CAAE;QACzGK,kBAAkB,CAACI,IAAI,CAACnD,YAAY,CAACA,YAAY,CAACoB,OAAO,EAAEsB,GAAG,EAAET,MAAM,CAACS,GAAG,CAAC,EAAEF,gBAAgB,CAACE,GAAG,CAAC,EAAEhC,SAAS,CAAC0C,SAAS,CAAC,CAAC;MAC7H;IACJ,CAAC,CAAC;IACF;IACA,KAAK;IACL,MAAMC,UAAU,GAAGN,kBAAkB,CAChCO,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC,CAC3BC,MAAM,CAACC,OAAO,CAAC;IACpB,IAAI,CAACJ,UAAU,CAACK,MAAM,EAClB;IACJ,MAAMC,eAAe,GAAG1B,MAAM;IAC9Bb,OAAO,CAACwC,aAAa,CAACnD,MAAM,CAACoD,WAAW,CAAC,aAAa,EAAEF,eAAe,CAAC,CAAC;IACzEG,OAAO,CAACC,GAAG,CAACV,UAAU,CAACC,GAAG,CAAE5C,SAAS,IAAKA,SAAS,CAACsD,QAAQ,CAAC,CAAC,CACzDC,IAAI,CAAC,MAAM;MACZ7C,OAAO,CAACwC,aAAa,CAACnD,MAAM,CAACoD,WAAW,CAAC,gBAAgB,EAAEF,eAAe,CAAC,CAAC;IAChF,CAAC,CAAC,CACGO,KAAK,CAACnE,KAAK,CAACoE,IAAI,CAAC;EAC1B;EACA,MAAMC,UAAU,GAAGA,CAACxC,IAAI,EAAEyC,QAAQ,KAAK,MAAM;IACzC9C,YAAY,CAACK,IAAI,CAAC,GAAGyC,QAAQ;IAC7BhE,QAAQ,CAACiE,iBAAiB,CAACC,KAAK,CAAC;EACrC,CAAC;EACD,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACrC,KAAK,MAAM5C,IAAI,IAAIjB,QAAQ,EAAE;MACzB,MAAM8D,eAAe,GAAG9D,QAAQ,CAACiB,IAAI,CAAC,CAACyC,QAAQ,CAACnD,SAAS,CAAC;MAC1D,MAAMwD,MAAM,GAAGhD,oBAAoB,CAACE,IAAI,CAAC;MACzC,IAAI6C,eAAe,IAAI,CAACC,MAAM,EAAE;QAC5BhD,oBAAoB,CAACE,IAAI,CAAC,GAAGjB,QAAQ,CAACiB,IAAI,CAAC,CAAC+C,SAAS,CAACvD,OAAO,EAAE;UAC3DwD,MAAM,EAAER,UAAU,CAACxC,IAAI,EAAE,IAAI,CAAC;UAC9BiD,OAAO,EAAET,UAAU,CAACxC,IAAI,EAAE,KAAK;QACnC,CAAC,EAAEV,SAAS,CAAC;MACjB,CAAC,MACI,IAAI,CAACuD,eAAe,IAAIC,MAAM,EAAE;QACjCA,MAAM,CAAC,CAAC;QACR,OAAOhD,oBAAoB,CAACE,IAAI,CAAC;MACrC;IACJ;EACJ,CAAC;EACD,MAAM2C,KAAK,GAAG;IACVO,MAAM,EAAGC,UAAU,IAAK;MACpB,IAAI,CAAC3D,OAAO,EACR;MACJF,SAAS,GAAG6D,UAAU;MACtBP,0BAA0B,CAAC,CAAC;MAC5BnE,QAAQ,CAACiE,iBAAiB,CAACC,KAAK,CAAC;IACrC,CAAC;IACDS,SAAS,EAAEA,CAACpD,IAAI,EAAEyC,QAAQ,KAAK;MAC3B,IAAI,CAACjD,OAAO,EACR;MACJG,YAAY,CAACK,IAAI,CAAC,GAAGyC,QAAQ;MAC7BhE,QAAQ,CAACiE,iBAAiB,CAACC,KAAK,CAAC;IACrC,CAAC;IACDlC,cAAc;IACdf,QAAQ,EAAEA,CAAA,KAAMD,KAAK;IACrB4D,SAAS,EAAEA,CAAA,KAAMhD,MAAM;IACvBU,UAAU,EAAEA,CAAA,KAAMzB,SAAS;IAC3BW,UAAU,EAAEA,CAAA,KAAMF,OAAO;IACzBuD,KAAK,EAAGC,UAAU,IAAK;MACnBrF,SAAS,CAACsF,SAAS,CAAC3B,OAAO,CAAC0B,UAAU,CAAC,EAAE,oDAAoD,CAAC;MAC9F/D,OAAO,GAAG+D,UAAU;MACpBpE,aAAa,CAACsE,GAAG,CAACjE,OAAO,EAAEmD,KAAK,CAAC;MACjCC,0BAA0B,CAAC,CAAC;MAC5B,OAAO,MAAM;QACTzD,aAAa,CAACuE,MAAM,CAAClE,OAAO,CAAC;QAC7Bf,QAAQ,CAACkF,mBAAmB,CAAChB,KAAK,CAAC;QACnC,KAAK,MAAM7B,GAAG,IAAIhB,oBAAoB,EAAE;UACpCA,oBAAoB,CAACgB,GAAG,CAAC,CAAC,CAAC;QAC/B;MACJ,CAAC;IACL,CAAC;IACD8C,SAAS,EAAEA,CAAA,KAAM/B,OAAO,CAACrC,OAAO;EACpC,CAAC;EACD,OAAOmD,KAAK;AAChB;AAEAkB,OAAO,CAACxE,iBAAiB,GAAGA,iBAAiB;AAC7CwE,OAAO,CAAC1E,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}