{"ast": null, "code": "import { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from 'hey-listen';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nconst positionalKeys = new Set([\"width\", \"height\", \"top\", \"left\", \"right\", \"bottom\", \"x\", \"y\"]);\nconst isPositionalKey = key => positionalKeys.has(key);\nconst hasPositionalKey = target => {\n  return Object.keys(target).some(isPositionalKey);\n};\nconst setAndResetVelocity = (value, to) => {\n  // Looks odd but setting it twice doesn't render, it'll just\n  // set both prev and current to the latest value\n  value.set(to, false);\n  value.set(to);\n};\nconst isNumOrPxType = v => v === number || v === px;\nvar BoundingBoxDimension;\n(function (BoundingBoxDimension) {\n  BoundingBoxDimension[\"width\"] = \"width\";\n  BoundingBoxDimension[\"height\"] = \"height\";\n  BoundingBoxDimension[\"left\"] = \"left\";\n  BoundingBoxDimension[\"right\"] = \"right\";\n  BoundingBoxDimension[\"top\"] = \"top\";\n  BoundingBoxDimension[\"bottom\"] = \"bottom\";\n})(BoundingBoxDimension || (BoundingBoxDimension = {}));\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, {\n  transform\n}) => {\n  if (transform === \"none\" || !transform) return 0;\n  const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n  if (matrix3d) {\n    return getPosFromMatrix(matrix3d[1], pos3);\n  } else {\n    const matrix = transform.match(/^matrix\\((.+)\\)$/);\n    if (matrix) {\n      return getPosFromMatrix(matrix[1], pos2);\n    } else {\n      return 0;\n    }\n  }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter(key => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n  const removedTransforms = [];\n  nonTranslationalTransformKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (value !== undefined) {\n      removedTransforms.push([key, value.get()]);\n      value.set(key.startsWith(\"scale\") ? 1 : 0);\n    }\n  });\n  // Apply changes to element before measurement\n  if (removedTransforms.length) visualElement.render();\n  return removedTransforms;\n}\nconst positionalValues = {\n  // Dimensions\n  width: ({\n    x\n  }, {\n    paddingLeft = \"0\",\n    paddingRight = \"0\"\n  }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n  height: ({\n    y\n  }, {\n    paddingTop = \"0\",\n    paddingBottom = \"0\"\n  }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n  top: (_bbox, {\n    top\n  }) => parseFloat(top),\n  left: (_bbox, {\n    left\n  }) => parseFloat(left),\n  bottom: ({\n    y\n  }, {\n    top\n  }) => parseFloat(top) + (y.max - y.min),\n  right: ({\n    x\n  }, {\n    left\n  }) => parseFloat(left) + (x.max - x.min),\n  // Transform\n  x: getTranslateFromMatrix(4, 13),\n  y: getTranslateFromMatrix(5, 14)\n};\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n  const originBbox = visualElement.measureViewportBox();\n  const element = visualElement.current;\n  const elementComputedStyle = getComputedStyle(element);\n  const {\n    display\n  } = elementComputedStyle;\n  const origin = {};\n  // If the element is currently set to display: \"none\", make it visible before\n  // measuring the target bounding box\n  if (display === \"none\") {\n    visualElement.setStaticValue(\"display\", target.display || \"block\");\n  }\n  /**\n   * Record origins before we render and update styles\n   */\n  changedKeys.forEach(key => {\n    origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n  });\n  // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n  visualElement.render();\n  const targetBbox = visualElement.measureViewportBox();\n  changedKeys.forEach(key => {\n    // Restore styles to their **calculated computed style**, not their actual\n    // originally set style. This allows us to animate between equivalent pixel units.\n    const value = visualElement.getValue(key);\n    setAndResetVelocity(value, origin[key]);\n    target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n  });\n  return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n  target = {\n    ...target\n  };\n  transitionEnd = {\n    ...transitionEnd\n  };\n  const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n  // We want to remove any transform values that could affect the element's bounding box before\n  // it's measured. We'll reapply these later.\n  let removedTransformValues = [];\n  let hasAttemptedToRemoveTransformValues = false;\n  const changedValueTypeKeys = [];\n  targetPositionalKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (!visualElement.hasValue(key)) return;\n    let from = origin[key];\n    let fromType = findDimensionValueType(from);\n    const to = target[key];\n    let toType;\n    // TODO: The current implementation of this basically throws an error\n    // if you try and do value conversion via keyframes. There's probably\n    // a way of doing this but the performance implications would need greater scrutiny,\n    // as it'd be doing multiple resize-remeasure operations.\n    if (isKeyframesTarget(to)) {\n      const numKeyframes = to.length;\n      const fromIndex = to[0] === null ? 1 : 0;\n      from = to[fromIndex];\n      fromType = findDimensionValueType(from);\n      for (let i = fromIndex; i < numKeyframes; i++) {\n        if (!toType) {\n          toType = findDimensionValueType(to[i]);\n          invariant(toType === fromType || isNumOrPxType(fromType) && isNumOrPxType(toType), \"Keyframes must be of the same dimension as the current value\");\n        } else {\n          invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n        }\n      }\n    } else {\n      toType = findDimensionValueType(to);\n    }\n    if (fromType !== toType) {\n      // If they're both just number or px, convert them both to numbers rather than\n      // relying on resize/remeasure to convert (which is wasteful in this situation)\n      if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n        const current = value.get();\n        if (typeof current === \"string\") {\n          value.set(parseFloat(current));\n        }\n        if (typeof to === \"string\") {\n          target[key] = parseFloat(to);\n        } else if (Array.isArray(to) && toType === px) {\n          target[key] = to.map(parseFloat);\n        }\n      } else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) && (toType === null || toType === void 0 ? void 0 : toType.transform) && (from === 0 || to === 0)) {\n        // If one or the other value is 0, it's safe to coerce it to the\n        // type of the other without measurement\n        if (from === 0) {\n          value.set(toType.transform(from));\n        } else {\n          target[key] = fromType.transform(to);\n        }\n      } else {\n        // If we're going to do value conversion via DOM measurements, we first\n        // need to remove non-positional transform values that could affect the bbox measurements.\n        if (!hasAttemptedToRemoveTransformValues) {\n          removedTransformValues = removeNonTranslationalTransform(visualElement);\n          hasAttemptedToRemoveTransformValues = true;\n        }\n        changedValueTypeKeys.push(key);\n        transitionEnd[key] = transitionEnd[key] !== undefined ? transitionEnd[key] : target[key];\n        setAndResetVelocity(value, to);\n      }\n    }\n  });\n  if (changedValueTypeKeys.length) {\n    const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0 ? window.pageYOffset : null;\n    const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n    // If we removed transform values, reapply them before the next render\n    if (removedTransformValues.length) {\n      removedTransformValues.forEach(([key, value]) => {\n        visualElement.getValue(key).set(value);\n      });\n    }\n    // Reapply original values\n    visualElement.render();\n    // Restore scroll position\n    if (isBrowser && scrollY !== null) {\n      window.scrollTo({\n        top: scrollY\n      });\n    }\n    return {\n      target: convertedTarget,\n      transitionEnd\n    };\n  } else {\n    return {\n      target,\n      transitionEnd\n    };\n  }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n  return hasPositionalKey(target) ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd) : {\n    target,\n    transitionEnd\n  };\n}\nexport { BoundingBoxDimension, positionalValues, unitConversion };", "map": {"version": 3, "names": ["isKeyframesTarget", "invariant", "transformPropOrder", "findDimensionValueType", "<PERSON><PERSON><PERSON><PERSON>", "number", "px", "positional<PERSON>eys", "Set", "isPositionalKey", "key", "has", "hasPositionalKey", "target", "Object", "keys", "some", "setAndResetVelocity", "value", "to", "set", "isNumOrPxType", "v", "BoundingBoxDimension", "getPosFromMatrix", "matrix", "pos", "parseFloat", "split", "getTranslateFromMatrix", "pos2", "pos3", "_bbox", "transform", "matrix3d", "match", "transformKeys", "nonTranslationalTransformKeys", "filter", "removeNonTranslationalTransform", "visualElement", "removedTransforms", "for<PERSON>ach", "getValue", "undefined", "push", "get", "startsWith", "length", "render", "positionalV<PERSON>ues", "width", "x", "paddingLeft", "paddingRight", "max", "min", "height", "y", "paddingTop", "paddingBottom", "top", "left", "bottom", "right", "convertChangedValueTypes", "changed<PERSON><PERSON><PERSON>", "originBbox", "measureViewportBox", "element", "current", "elementComputedStyle", "getComputedStyle", "display", "origin", "setStaticValue", "targetBbox", "checkAndConvertChangedValueTypes", "transitionEnd", "targetPositionalKeys", "removedTransformValues", "hasAttemptedToRemoveTransformValues", "changedValueTypeKeys", "hasValue", "from", "fromType", "toType", "numKeyframes", "fromIndex", "i", "Array", "isArray", "map", "scrollY", "indexOf", "window", "pageYOffset", "convertedTarget", "scrollTo", "unitConversion"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs"], "sourcesContent": ["import { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from 'hey-listen';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    \"x\",\n    \"y\",\n]);\nconst isPositionalKey = (key) => positionalKeys.has(key);\nconst hasPositionalKey = (target) => {\n    return Object.keys(target).some(isPositionalKey);\n};\nconst setAndResetVelocity = (value, to) => {\n    // Looks odd but setting it twice doesn't render, it'll just\n    // set both prev and current to the latest value\n    value.set(to, false);\n    value.set(to);\n};\nconst isNumOrPxType = (v) => v === number || v === px;\nvar BoundingBoxDimension;\n(function (BoundingBoxDimension) {\n    BoundingBoxDimension[\"width\"] = \"width\";\n    BoundingBoxDimension[\"height\"] = \"height\";\n    BoundingBoxDimension[\"left\"] = \"left\";\n    BoundingBoxDimension[\"right\"] = \"right\";\n    BoundingBoxDimension[\"top\"] = \"top\";\n    BoundingBoxDimension[\"bottom\"] = \"bottom\";\n})(BoundingBoxDimension || (BoundingBoxDimension = {}));\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, { transform }) => {\n    if (transform === \"none\" || !transform)\n        return 0;\n    const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (matrix3d) {\n        return getPosFromMatrix(matrix3d[1], pos3);\n    }\n    else {\n        const matrix = transform.match(/^matrix\\((.+)\\)$/);\n        if (matrix) {\n            return getPosFromMatrix(matrix[1], pos2);\n        }\n        else {\n            return 0;\n        }\n    }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    // Apply changes to element before measurement\n    if (removedTransforms.length)\n        visualElement.render();\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: getTranslateFromMatrix(4, 13),\n    y: getTranslateFromMatrix(5, 14),\n};\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n    const originBbox = visualElement.measureViewportBox();\n    const element = visualElement.current;\n    const elementComputedStyle = getComputedStyle(element);\n    const { display } = elementComputedStyle;\n    const origin = {};\n    // If the element is currently set to display: \"none\", make it visible before\n    // measuring the target bounding box\n    if (display === \"none\") {\n        visualElement.setStaticValue(\"display\", target.display || \"block\");\n    }\n    /**\n     * Record origins before we render and update styles\n     */\n    changedKeys.forEach((key) => {\n        origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n    });\n    // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n    visualElement.render();\n    const targetBbox = visualElement.measureViewportBox();\n    changedKeys.forEach((key) => {\n        // Restore styles to their **calculated computed style**, not their actual\n        // originally set style. This allows us to animate between equivalent pixel units.\n        const value = visualElement.getValue(key);\n        setAndResetVelocity(value, origin[key]);\n        target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n    });\n    return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n    target = { ...target };\n    transitionEnd = { ...transitionEnd };\n    const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n    // We want to remove any transform values that could affect the element's bounding box before\n    // it's measured. We'll reapply these later.\n    let removedTransformValues = [];\n    let hasAttemptedToRemoveTransformValues = false;\n    const changedValueTypeKeys = [];\n    targetPositionalKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (!visualElement.hasValue(key))\n            return;\n        let from = origin[key];\n        let fromType = findDimensionValueType(from);\n        const to = target[key];\n        let toType;\n        // TODO: The current implementation of this basically throws an error\n        // if you try and do value conversion via keyframes. There's probably\n        // a way of doing this but the performance implications would need greater scrutiny,\n        // as it'd be doing multiple resize-remeasure operations.\n        if (isKeyframesTarget(to)) {\n            const numKeyframes = to.length;\n            const fromIndex = to[0] === null ? 1 : 0;\n            from = to[fromIndex];\n            fromType = findDimensionValueType(from);\n            for (let i = fromIndex; i < numKeyframes; i++) {\n                if (!toType) {\n                    toType = findDimensionValueType(to[i]);\n                    invariant(toType === fromType ||\n                        (isNumOrPxType(fromType) && isNumOrPxType(toType)), \"Keyframes must be of the same dimension as the current value\");\n                }\n                else {\n                    invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n                }\n            }\n        }\n        else {\n            toType = findDimensionValueType(to);\n        }\n        if (fromType !== toType) {\n            // If they're both just number or px, convert them both to numbers rather than\n            // relying on resize/remeasure to convert (which is wasteful in this situation)\n            if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n                const current = value.get();\n                if (typeof current === \"string\") {\n                    value.set(parseFloat(current));\n                }\n                if (typeof to === \"string\") {\n                    target[key] = parseFloat(to);\n                }\n                else if (Array.isArray(to) && toType === px) {\n                    target[key] = to.map(parseFloat);\n                }\n            }\n            else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) &&\n                (toType === null || toType === void 0 ? void 0 : toType.transform) &&\n                (from === 0 || to === 0)) {\n                // If one or the other value is 0, it's safe to coerce it to the\n                // type of the other without measurement\n                if (from === 0) {\n                    value.set(toType.transform(from));\n                }\n                else {\n                    target[key] = fromType.transform(to);\n                }\n            }\n            else {\n                // If we're going to do value conversion via DOM measurements, we first\n                // need to remove non-positional transform values that could affect the bbox measurements.\n                if (!hasAttemptedToRemoveTransformValues) {\n                    removedTransformValues =\n                        removeNonTranslationalTransform(visualElement);\n                    hasAttemptedToRemoveTransformValues = true;\n                }\n                changedValueTypeKeys.push(key);\n                transitionEnd[key] =\n                    transitionEnd[key] !== undefined\n                        ? transitionEnd[key]\n                        : target[key];\n                setAndResetVelocity(value, to);\n            }\n        }\n    });\n    if (changedValueTypeKeys.length) {\n        const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0\n            ? window.pageYOffset\n            : null;\n        const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n        // If we removed transform values, reapply them before the next render\n        if (removedTransformValues.length) {\n            removedTransformValues.forEach(([key, value]) => {\n                visualElement.getValue(key).set(value);\n            });\n        }\n        // Reapply original values\n        visualElement.render();\n        // Restore scroll position\n        if (isBrowser && scrollY !== null) {\n            window.scrollTo({ top: scrollY });\n        }\n        return { target: convertedTarget, transitionEnd };\n    }\n    else {\n        return { target, transitionEnd };\n    }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n    return hasPositionalKey(target)\n        ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd)\n        : { target, transitionEnd };\n}\n\nexport { BoundingBoxDimension, positionalValues, unitConversion };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,MAAM,QAAQ,wCAAwC;AAC/D,SAASC,EAAE,QAAQ,wCAAwC;AAE3D,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC3B,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,GAAG,EACH,GAAG,CACN,CAAC;AACF,MAAMC,eAAe,GAAIC,GAAG,IAAKH,cAAc,CAACI,GAAG,CAACD,GAAG,CAAC;AACxD,MAAME,gBAAgB,GAAIC,MAAM,IAAK;EACjC,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,IAAI,CAACP,eAAe,CAAC;AACpD,CAAC;AACD,MAAMQ,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,EAAE,KAAK;EACvC;EACA;EACAD,KAAK,CAACE,GAAG,CAACD,EAAE,EAAE,KAAK,CAAC;EACpBD,KAAK,CAACE,GAAG,CAACD,EAAE,CAAC;AACjB,CAAC;AACD,MAAME,aAAa,GAAIC,CAAC,IAAKA,CAAC,KAAKjB,MAAM,IAAIiB,CAAC,KAAKhB,EAAE;AACrD,IAAIiB,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO;EACvCA,oBAAoB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACzCA,oBAAoB,CAAC,MAAM,CAAC,GAAG,MAAM;EACrCA,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO;EACvCA,oBAAoB,CAAC,KAAK,CAAC,GAAG,KAAK;EACnCA,oBAAoB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC7C,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAKC,UAAU,CAACF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAACF,GAAG,CAAC,CAAC;AAC7E,MAAMG,sBAAsB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK,CAACC,KAAK,EAAE;EAAEC;AAAU,CAAC,KAAK;EACrE,IAAIA,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS,EAClC,OAAO,CAAC;EACZ,MAAMC,QAAQ,GAAGD,SAAS,CAACE,KAAK,CAAC,oBAAoB,CAAC;EACtD,IAAID,QAAQ,EAAE;IACV,OAAOV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC;EAC9C,CAAC,MACI;IACD,MAAMN,MAAM,GAAGQ,SAAS,CAACE,KAAK,CAAC,kBAAkB,CAAC;IAClD,IAAIV,MAAM,EAAE;MACR,OAAOD,gBAAgB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEK,IAAI,CAAC;IAC5C,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;AACJ,CAAC;AACD,MAAMM,aAAa,GAAG,IAAI5B,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9C,MAAM6B,6BAA6B,GAAGnC,kBAAkB,CAACoC,MAAM,CAAE5B,GAAG,IAAK,CAAC0B,aAAa,CAACzB,GAAG,CAACD,GAAG,CAAC,CAAC;AACjG,SAAS6B,+BAA+BA,CAACC,aAAa,EAAE;EACpD,MAAMC,iBAAiB,GAAG,EAAE;EAC5BJ,6BAA6B,CAACK,OAAO,CAAEhC,GAAG,IAAK;IAC3C,MAAMQ,KAAK,GAAGsB,aAAa,CAACG,QAAQ,CAACjC,GAAG,CAAC;IACzC,IAAIQ,KAAK,KAAK0B,SAAS,EAAE;MACrBH,iBAAiB,CAACI,IAAI,CAAC,CAACnC,GAAG,EAAEQ,KAAK,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1C5B,KAAK,CAACE,GAAG,CAACV,GAAG,CAACqC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;EACJ,CAAC,CAAC;EACF;EACA,IAAIN,iBAAiB,CAACO,MAAM,EACxBR,aAAa,CAACS,MAAM,CAAC,CAAC;EAC1B,OAAOR,iBAAiB;AAC5B;AACA,MAAMS,gBAAgB,GAAG;EACrB;EACAC,KAAK,EAAEA,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAEC,WAAW,GAAG,GAAG;IAAEC,YAAY,GAAG;EAAI,CAAC,KAAKF,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,GAAG7B,UAAU,CAAC0B,WAAW,CAAC,GAAG1B,UAAU,CAAC2B,YAAY,CAAC;EAC/HG,MAAM,EAAEA,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAEC,UAAU,GAAG,GAAG;IAAEC,aAAa,GAAG;EAAI,CAAC,KAAKF,CAAC,CAACH,GAAG,GAAGG,CAAC,CAACF,GAAG,GAAG7B,UAAU,CAACgC,UAAU,CAAC,GAAGhC,UAAU,CAACiC,aAAa,CAAC;EAChIC,GAAG,EAAEA,CAAC7B,KAAK,EAAE;IAAE6B;EAAI,CAAC,KAAKlC,UAAU,CAACkC,GAAG,CAAC;EACxCC,IAAI,EAAEA,CAAC9B,KAAK,EAAE;IAAE8B;EAAK,CAAC,KAAKnC,UAAU,CAACmC,IAAI,CAAC;EAC3CC,MAAM,EAAEA,CAAC;IAAEL;EAAE,CAAC,EAAE;IAAEG;EAAI,CAAC,KAAKlC,UAAU,CAACkC,GAAG,CAAC,IAAIH,CAAC,CAACH,GAAG,GAAGG,CAAC,CAACF,GAAG,CAAC;EAC7DQ,KAAK,EAAEA,CAAC;IAAEZ;EAAE,CAAC,EAAE;IAAEU;EAAK,CAAC,KAAKnC,UAAU,CAACmC,IAAI,CAAC,IAAIV,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,CAAC;EAC9D;EACAJ,CAAC,EAAEvB,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;EAChC6B,CAAC,EAAE7B,sBAAsB,CAAC,CAAC,EAAE,EAAE;AACnC,CAAC;AACD,MAAMoC,wBAAwB,GAAGA,CAACpD,MAAM,EAAE2B,aAAa,EAAE0B,WAAW,KAAK;EACrE,MAAMC,UAAU,GAAG3B,aAAa,CAAC4B,kBAAkB,CAAC,CAAC;EACrD,MAAMC,OAAO,GAAG7B,aAAa,CAAC8B,OAAO;EACrC,MAAMC,oBAAoB,GAAGC,gBAAgB,CAACH,OAAO,CAAC;EACtD,MAAM;IAAEI;EAAQ,CAAC,GAAGF,oBAAoB;EACxC,MAAMG,MAAM,GAAG,CAAC,CAAC;EACjB;EACA;EACA,IAAID,OAAO,KAAK,MAAM,EAAE;IACpBjC,aAAa,CAACmC,cAAc,CAAC,SAAS,EAAE9D,MAAM,CAAC4D,OAAO,IAAI,OAAO,CAAC;EACtE;EACA;AACJ;AACA;EACIP,WAAW,CAACxB,OAAO,CAAEhC,GAAG,IAAK;IACzBgE,MAAM,CAAChE,GAAG,CAAC,GAAGwC,gBAAgB,CAACxC,GAAG,CAAC,CAACyD,UAAU,EAAEI,oBAAoB,CAAC;EACzE,CAAC,CAAC;EACF;EACA/B,aAAa,CAACS,MAAM,CAAC,CAAC;EACtB,MAAM2B,UAAU,GAAGpC,aAAa,CAAC4B,kBAAkB,CAAC,CAAC;EACrDF,WAAW,CAACxB,OAAO,CAAEhC,GAAG,IAAK;IACzB;IACA;IACA,MAAMQ,KAAK,GAAGsB,aAAa,CAACG,QAAQ,CAACjC,GAAG,CAAC;IACzCO,mBAAmB,CAACC,KAAK,EAAEwD,MAAM,CAAChE,GAAG,CAAC,CAAC;IACvCG,MAAM,CAACH,GAAG,CAAC,GAAGwC,gBAAgB,CAACxC,GAAG,CAAC,CAACkE,UAAU,EAAEL,oBAAoB,CAAC;EACzE,CAAC,CAAC;EACF,OAAO1D,MAAM;AACjB,CAAC;AACD,MAAMgE,gCAAgC,GAAGA,CAACrC,aAAa,EAAE3B,MAAM,EAAE6D,MAAM,GAAG,CAAC,CAAC,EAAEI,aAAa,GAAG,CAAC,CAAC,KAAK;EACjGjE,MAAM,GAAG;IAAE,GAAGA;EAAO,CAAC;EACtBiE,aAAa,GAAG;IAAE,GAAGA;EAAc,CAAC;EACpC,MAAMC,oBAAoB,GAAGjE,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACyB,MAAM,CAAC7B,eAAe,CAAC;EACxE;EACA;EACA,IAAIuE,sBAAsB,GAAG,EAAE;EAC/B,IAAIC,mCAAmC,GAAG,KAAK;EAC/C,MAAMC,oBAAoB,GAAG,EAAE;EAC/BH,oBAAoB,CAACrC,OAAO,CAAEhC,GAAG,IAAK;IAClC,MAAMQ,KAAK,GAAGsB,aAAa,CAACG,QAAQ,CAACjC,GAAG,CAAC;IACzC,IAAI,CAAC8B,aAAa,CAAC2C,QAAQ,CAACzE,GAAG,CAAC,EAC5B;IACJ,IAAI0E,IAAI,GAAGV,MAAM,CAAChE,GAAG,CAAC;IACtB,IAAI2E,QAAQ,GAAGlF,sBAAsB,CAACiF,IAAI,CAAC;IAC3C,MAAMjE,EAAE,GAAGN,MAAM,CAACH,GAAG,CAAC;IACtB,IAAI4E,MAAM;IACV;IACA;IACA;IACA;IACA,IAAItF,iBAAiB,CAACmB,EAAE,CAAC,EAAE;MACvB,MAAMoE,YAAY,GAAGpE,EAAE,CAAC6B,MAAM;MAC9B,MAAMwC,SAAS,GAAGrE,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;MACxCiE,IAAI,GAAGjE,EAAE,CAACqE,SAAS,CAAC;MACpBH,QAAQ,GAAGlF,sBAAsB,CAACiF,IAAI,CAAC;MACvC,KAAK,IAAIK,CAAC,GAAGD,SAAS,EAAEC,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;QAC3C,IAAI,CAACH,MAAM,EAAE;UACTA,MAAM,GAAGnF,sBAAsB,CAACgB,EAAE,CAACsE,CAAC,CAAC,CAAC;UACtCxF,SAAS,CAACqF,MAAM,KAAKD,QAAQ,IACxBhE,aAAa,CAACgE,QAAQ,CAAC,IAAIhE,aAAa,CAACiE,MAAM,CAAE,EAAE,8DAA8D,CAAC;QAC3H,CAAC,MACI;UACDrF,SAAS,CAACE,sBAAsB,CAACgB,EAAE,CAACsE,CAAC,CAAC,CAAC,KAAKH,MAAM,EAAE,wCAAwC,CAAC;QACjG;MACJ;IACJ,CAAC,MACI;MACDA,MAAM,GAAGnF,sBAAsB,CAACgB,EAAE,CAAC;IACvC;IACA,IAAIkE,QAAQ,KAAKC,MAAM,EAAE;MACrB;MACA;MACA,IAAIjE,aAAa,CAACgE,QAAQ,CAAC,IAAIhE,aAAa,CAACiE,MAAM,CAAC,EAAE;QAClD,MAAMhB,OAAO,GAAGpD,KAAK,CAAC4B,GAAG,CAAC,CAAC;QAC3B,IAAI,OAAOwB,OAAO,KAAK,QAAQ,EAAE;UAC7BpD,KAAK,CAACE,GAAG,CAACO,UAAU,CAAC2C,OAAO,CAAC,CAAC;QAClC;QACA,IAAI,OAAOnD,EAAE,KAAK,QAAQ,EAAE;UACxBN,MAAM,CAACH,GAAG,CAAC,GAAGiB,UAAU,CAACR,EAAE,CAAC;QAChC,CAAC,MACI,IAAIuE,KAAK,CAACC,OAAO,CAACxE,EAAE,CAAC,IAAImE,MAAM,KAAKhF,EAAE,EAAE;UACzCO,MAAM,CAACH,GAAG,CAAC,GAAGS,EAAE,CAACyE,GAAG,CAACjE,UAAU,CAAC;QACpC;MACJ,CAAC,MACI,IAAI,CAAC0D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpD,SAAS,MAC3EqD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACrD,SAAS,CAAC,KACjEmD,IAAI,KAAK,CAAC,IAAIjE,EAAE,KAAK,CAAC,CAAC,EAAE;QAC1B;QACA;QACA,IAAIiE,IAAI,KAAK,CAAC,EAAE;UACZlE,KAAK,CAACE,GAAG,CAACkE,MAAM,CAACrD,SAAS,CAACmD,IAAI,CAAC,CAAC;QACrC,CAAC,MACI;UACDvE,MAAM,CAACH,GAAG,CAAC,GAAG2E,QAAQ,CAACpD,SAAS,CAACd,EAAE,CAAC;QACxC;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAI,CAAC8D,mCAAmC,EAAE;UACtCD,sBAAsB,GAClBzC,+BAA+B,CAACC,aAAa,CAAC;UAClDyC,mCAAmC,GAAG,IAAI;QAC9C;QACAC,oBAAoB,CAACrC,IAAI,CAACnC,GAAG,CAAC;QAC9BoE,aAAa,CAACpE,GAAG,CAAC,GACdoE,aAAa,CAACpE,GAAG,CAAC,KAAKkC,SAAS,GAC1BkC,aAAa,CAACpE,GAAG,CAAC,GAClBG,MAAM,CAACH,GAAG,CAAC;QACrBO,mBAAmB,CAACC,KAAK,EAAEC,EAAE,CAAC;MAClC;IACJ;EACJ,CAAC,CAAC;EACF,IAAI+D,oBAAoB,CAAClC,MAAM,EAAE;IAC7B,MAAM6C,OAAO,GAAGX,oBAAoB,CAACY,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GACrDC,MAAM,CAACC,WAAW,GAClB,IAAI;IACV,MAAMC,eAAe,GAAGhC,wBAAwB,CAACpD,MAAM,EAAE2B,aAAa,EAAE0C,oBAAoB,CAAC;IAC7F;IACA,IAAIF,sBAAsB,CAAChC,MAAM,EAAE;MAC/BgC,sBAAsB,CAACtC,OAAO,CAAC,CAAC,CAAChC,GAAG,EAAEQ,KAAK,CAAC,KAAK;QAC7CsB,aAAa,CAACG,QAAQ,CAACjC,GAAG,CAAC,CAACU,GAAG,CAACF,KAAK,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;IACAsB,aAAa,CAACS,MAAM,CAAC,CAAC;IACtB;IACA,IAAI7C,SAAS,IAAIyF,OAAO,KAAK,IAAI,EAAE;MAC/BE,MAAM,CAACG,QAAQ,CAAC;QAAErC,GAAG,EAAEgC;MAAQ,CAAC,CAAC;IACrC;IACA,OAAO;MAAEhF,MAAM,EAAEoF,eAAe;MAAEnB;IAAc,CAAC;EACrD,CAAC,MACI;IACD,OAAO;MAAEjE,MAAM;MAAEiE;IAAc,CAAC;EACpC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,cAAcA,CAAC3D,aAAa,EAAE3B,MAAM,EAAE6D,MAAM,EAAEI,aAAa,EAAE;EAClE,OAAOlE,gBAAgB,CAACC,MAAM,CAAC,GACzBgE,gCAAgC,CAACrC,aAAa,EAAE3B,MAAM,EAAE6D,MAAM,EAAEI,aAAa,CAAC,GAC9E;IAAEjE,MAAM;IAAEiE;EAAc,CAAC;AACnC;AAEA,SAASvD,oBAAoB,EAAE2B,gBAAgB,EAAEiD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}