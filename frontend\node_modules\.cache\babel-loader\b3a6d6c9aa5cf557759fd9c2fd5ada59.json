{"ast": null, "code": "import { mapEasingToNativeEasing } from './easing.mjs';\nfunction animateStyle(element, valueName, keyframes, {\n  delay = 0,\n  duration,\n  repeat = 0,\n  repeatType = \"loop\",\n  ease,\n  times\n} = {}) {\n  return element.animate({\n    [valueName]: keyframes,\n    offset: times\n  }, {\n    delay,\n    duration,\n    easing: mapEasingToNativeEasing(ease),\n    fill: \"both\",\n    iterations: repeat + 1,\n    direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\"\n  });\n}\nexport { animateStyle };", "map": {"version": 3, "names": ["mapEasingToNativeEasing", "animateStyle", "element", "valueName", "keyframes", "delay", "duration", "repeat", "repeatType", "ease", "times", "animate", "offset", "easing", "fill", "iterations", "direction"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/waapi/index.mjs"], "sourcesContent": ["import { mapEasingToNativeEasing } from './easing.mjs';\n\nfunction animateStyle(element, valueName, keyframes, { delay = 0, duration, repeat = 0, repeatType = \"loop\", ease, times, } = {}) {\n    return element.animate({ [valueName]: keyframes, offset: times }, {\n        delay,\n        duration,\n        easing: mapEasingToNativeEasing(ease),\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    });\n}\n\nexport { animateStyle };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,cAAc;AAEtD,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,QAAQ;EAAEC,MAAM,GAAG,CAAC;EAAEC,UAAU,GAAG,MAAM;EAAEC,IAAI;EAAEC;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9H,OAAOR,OAAO,CAACS,OAAO,CAAC;IAAE,CAACR,SAAS,GAAGC,SAAS;IAAEQ,MAAM,EAAEF;EAAM,CAAC,EAAE;IAC9DL,KAAK;IACLC,QAAQ;IACRO,MAAM,EAAEb,uBAAuB,CAACS,IAAI,CAAC;IACrCK,IAAI,EAAE,MAAM;IACZC,UAAU,EAAER,MAAM,GAAG,CAAC;IACtBS,SAAS,EAAER,UAAU,KAAK,SAAS,GAAG,WAAW,GAAG;EACxD,CAAC,CAAC;AACN;AAEA,SAASP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}