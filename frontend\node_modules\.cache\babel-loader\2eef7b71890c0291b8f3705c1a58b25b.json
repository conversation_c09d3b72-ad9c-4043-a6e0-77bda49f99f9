{"ast": null, "code": "import { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { pipe } from './pipe.mjs';\nimport { warning } from 'hey-listen';\nimport { color } from '../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../value/types/complex/index.mjs';\nfunction getMixer(origin, target) {\n  if (typeof origin === \"number\") {\n    return v => mix(origin, target, v);\n  } else if (color.test(origin)) {\n    return mixColor(origin, target);\n  } else {\n    return mixComplex(origin, target);\n  }\n}\nconst mixArray = (from, to) => {\n  const output = [...from];\n  const numValues = output.length;\n  const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n  return v => {\n    for (let i = 0; i < numValues; i++) {\n      output[i] = blendValue[i](v);\n    }\n    return output;\n  };\n};\nconst mixObject = (origin, target) => {\n  const output = {\n    ...origin,\n    ...target\n  };\n  const blendValue = {};\n  for (const key in output) {\n    if (origin[key] !== undefined && target[key] !== undefined) {\n      blendValue[key] = getMixer(origin[key], target[key]);\n    }\n  }\n  return v => {\n    for (const key in blendValue) {\n      output[key] = blendValue[key](v);\n    }\n    return output;\n  };\n};\nconst mixComplex = (origin, target) => {\n  const template = complex.createTransformer(target);\n  const originStats = analyseComplexValue(origin);\n  const targetStats = analyseComplexValue(target);\n  const canInterpolate = originStats.numColors === targetStats.numColors && originStats.numNumbers >= targetStats.numNumbers;\n  if (canInterpolate) {\n    return pipe(mixArray(originStats.values, targetStats.values), template);\n  } else {\n    warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n    return p => `${p > 0 ? target : origin}`;\n  }\n};\nexport { mixArray, mixComplex, mixObject };", "map": {"version": 3, "names": ["mix", "mixColor", "pipe", "warning", "color", "complex", "analyseComplexValue", "getMixer", "origin", "target", "v", "test", "mixComplex", "mixArray", "from", "to", "output", "numValues", "length", "blendValue", "map", "fromThis", "i", "mixObject", "key", "undefined", "template", "createTransformer", "originStats", "targetStats", "canInterpolate", "numColors", "numNumbers", "values", "p"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/utils/mix-complex.mjs"], "sourcesContent": ["import { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { pipe } from './pipe.mjs';\nimport { warning } from 'hey-listen';\nimport { color } from '../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../value/types/complex/index.mjs';\n\nfunction getMixer(origin, target) {\n    if (typeof origin === \"number\") {\n        return (v) => mix(origin, target, v);\n    }\n    else if (color.test(origin)) {\n        return mixColor(origin, target);\n    }\n    else {\n        return mixComplex(origin, target);\n    }\n}\nconst mixArray = (from, to) => {\n    const output = [...from];\n    const numValues = output.length;\n    const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n    return (v) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](v);\n        }\n        return output;\n    };\n};\nconst mixObject = (origin, target) => {\n    const output = { ...origin, ...target };\n    const blendValue = {};\n    for (const key in output) {\n        if (origin[key] !== undefined && target[key] !== undefined) {\n            blendValue[key] = getMixer(origin[key], target[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n};\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.numColors === targetStats.numColors &&\n        originStats.numNumbers >= targetStats.numNumbers;\n    if (canInterpolate) {\n        return pipe(mixArray(originStats.values, targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return (p) => `${p > 0 ? target : origin}`;\n    }\n};\n\nexport { mixArray, mixComplex, mixObject };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,WAAW;AAC/B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,KAAK,QAAQ,gCAAgC;AACtD,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,kCAAkC;AAE/E,SAASC,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC9B,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAQE,CAAC,IAAKV,GAAG,CAACQ,MAAM,EAAEC,MAAM,EAAEC,CAAC,CAAC;EACxC,CAAC,MACI,IAAIN,KAAK,CAACO,IAAI,CAACH,MAAM,CAAC,EAAE;IACzB,OAAOP,QAAQ,CAACO,MAAM,EAAEC,MAAM,CAAC;EACnC,CAAC,MACI;IACD,OAAOG,UAAU,CAACJ,MAAM,EAAEC,MAAM,CAAC;EACrC;AACJ;AACA,MAAMI,QAAQ,GAAGA,CAACC,IAAI,EAAEC,EAAE,KAAK;EAC3B,MAAMC,MAAM,GAAG,CAAC,GAAGF,IAAI,CAAC;EACxB,MAAMG,SAAS,GAAGD,MAAM,CAACE,MAAM;EAC/B,MAAMC,UAAU,GAAGL,IAAI,CAACM,GAAG,CAAC,CAACC,QAAQ,EAAEC,CAAC,KAAKf,QAAQ,CAACc,QAAQ,EAAEN,EAAE,CAACO,CAAC,CAAC,CAAC,CAAC;EACvE,OAAQZ,CAAC,IAAK;IACV,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;MAChCN,MAAM,CAACM,CAAC,CAAC,GAAGH,UAAU,CAACG,CAAC,CAAC,CAACZ,CAAC,CAAC;IAChC;IACA,OAAOM,MAAM;EACjB,CAAC;AACL,CAAC;AACD,MAAMO,SAAS,GAAGA,CAACf,MAAM,EAAEC,MAAM,KAAK;EAClC,MAAMO,MAAM,GAAG;IAAE,GAAGR,MAAM;IAAE,GAAGC;EAAO,CAAC;EACvC,MAAMU,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMK,GAAG,IAAIR,MAAM,EAAE;IACtB,IAAIR,MAAM,CAACgB,GAAG,CAAC,KAAKC,SAAS,IAAIhB,MAAM,CAACe,GAAG,CAAC,KAAKC,SAAS,EAAE;MACxDN,UAAU,CAACK,GAAG,CAAC,GAAGjB,QAAQ,CAACC,MAAM,CAACgB,GAAG,CAAC,EAAEf,MAAM,CAACe,GAAG,CAAC,CAAC;IACxD;EACJ;EACA,OAAQd,CAAC,IAAK;IACV,KAAK,MAAMc,GAAG,IAAIL,UAAU,EAAE;MAC1BH,MAAM,CAACQ,GAAG,CAAC,GAAGL,UAAU,CAACK,GAAG,CAAC,CAACd,CAAC,CAAC;IACpC;IACA,OAAOM,MAAM;EACjB,CAAC;AACL,CAAC;AACD,MAAMJ,UAAU,GAAGA,CAACJ,MAAM,EAAEC,MAAM,KAAK;EACnC,MAAMiB,QAAQ,GAAGrB,OAAO,CAACsB,iBAAiB,CAAClB,MAAM,CAAC;EAClD,MAAMmB,WAAW,GAAGtB,mBAAmB,CAACE,MAAM,CAAC;EAC/C,MAAMqB,WAAW,GAAGvB,mBAAmB,CAACG,MAAM,CAAC;EAC/C,MAAMqB,cAAc,GAAGF,WAAW,CAACG,SAAS,KAAKF,WAAW,CAACE,SAAS,IAClEH,WAAW,CAACI,UAAU,IAAIH,WAAW,CAACG,UAAU;EACpD,IAAIF,cAAc,EAAE;IAChB,OAAO5B,IAAI,CAACW,QAAQ,CAACe,WAAW,CAACK,MAAM,EAAEJ,WAAW,CAACI,MAAM,CAAC,EAAEP,QAAQ,CAAC;EAC3E,CAAC,MACI;IACDvB,OAAO,CAAC,IAAI,EAAE,mBAAmBK,MAAM,UAAUC,MAAM,0KAA0K,CAAC;IAClO,OAAQyB,CAAC,IAAK,GAAGA,CAAC,GAAG,CAAC,GAAGzB,MAAM,GAAGD,MAAM,EAAE;EAC9C;AACJ,CAAC;AAED,SAASK,QAAQ,EAAED,UAAU,EAAEW,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}