"""
Customer model for managing cable/internet customers
"""

import enum
from datetime import datetime, date
from sqlalchemy import Column, String, <PERSON>olean, Foreign<PERSON>ey, Integer, Date, Text, Enum, Float
from sqlalchemy.orm import relationship

from .base import BaseModel


class CustomerStatus(enum.Enum):
    """Customer status options"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"


class ServiceType(enum.Enum):
    """Service type options"""
    CABLE = "cable"
    INTERNET = "internet"
    BOTH = "both"


class Customer(BaseModel):
    """Customer model"""
    
    __tablename__ = "customers"
    
    # Basic Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(20), nullable=False, index=True)
    alternate_phone = Column(String(20), nullable=True)
    
    # Address Information
    address_line1 = Column(String(255), nullable=False)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=False)
    state = Column(String(100), nullable=False)
    postal_code = Column(String(20), nullable=False)
    country = Column(String(100), nullable=False, default="United States")
    
    # Service Information
    service_type = Column(Enum(ServiceType), nullable=False)
    status = Column(Enum(CustomerStatus), nullable=False, default=CustomerStatus.ACTIVE)
    connection_date = Column(Date, nullable=False, default=date.today)
    disconnection_date = Column(Date, nullable=True)
    
    # Plan Association
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=True, index=True)
    
    # Billing Information
    billing_cycle_day = Column(Integer, nullable=False, default=1)  # Day of month for billing
    security_deposit = Column(Float, nullable=False, default=0.0)
    credit_limit = Column(Float, nullable=False, default=0.0)
    
    # Installation Details
    installation_address_same = Column(Boolean, nullable=False, default=True)
    installation_address_line1 = Column(String(255), nullable=True)
    installation_address_line2 = Column(String(255), nullable=True)
    installation_city = Column(String(100), nullable=True)
    installation_state = Column(String(100), nullable=True)
    installation_postal_code = Column(String(20), nullable=True)
    installation_country = Column(String(100), nullable=True)
    
    # Technical Information
    equipment_serial_numbers = Column(Text, nullable=True)  # JSON or comma-separated
    static_ip = Column(String(45), nullable=True)
    bandwidth_limit = Column(String(50), nullable=True)
    
    # Customer Preferences
    preferred_contact_method = Column(String(20), nullable=False, default="email")
    send_email_notifications = Column(Boolean, nullable=False, default=True)
    send_sms_notifications = Column(Boolean, nullable=False, default=False)
    
    # Company Association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Audit Information
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Additional Information
    notes = Column(Text, nullable=True)
    tags = Column(Text, nullable=True)  # Comma-separated tags
    
    # Relationships
    company = relationship("Company", back_populates="customers")
    plan = relationship("Plan", back_populates="customers")
    payments = relationship("Payment", back_populates="customer", cascade="all, delete-orphan")
    invoices = relationship("Invoice", back_populates="customer", cascade="all, delete-orphan")
    created_by = relationship("User", foreign_keys=[created_by_id])
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return [
            'first_name', 'last_name', 'email', 'phone', 
            'address_line1', 'city', 'state', 'postal_code'
        ]
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return [
            'status', 'service_type', 'plan_id', 'city', 'state', 
            'connection_date', 'billing_cycle_day', 'company_id'
        ]
    
    @property
    def full_name(self):
        """Get customer's full name"""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def display_name(self):
        """Get display name (full name or email)"""
        return self.full_name or self.email
    
    def get_full_address(self):
        """Get formatted full address"""
        address_parts = [self.address_line1]
        
        if self.address_line2:
            address_parts.append(self.address_line2)
        
        address_parts.append(f"{self.city}, {self.state} {self.postal_code}")
        
        if self.country != "United States":
            address_parts.append(self.country)
        
        return "\n".join(address_parts)
    
    def get_installation_address(self):
        """Get installation address (if different from billing)"""
        if self.installation_address_same or not self.installation_address_line1:
            return self.get_full_address()
        
        address_parts = [self.installation_address_line1]
        
        if self.installation_address_line2:
            address_parts.append(self.installation_address_line2)
        
        address_parts.append(
            f"{self.installation_city}, {self.installation_state} {self.installation_postal_code}"
        )
        
        if self.installation_country and self.installation_country != "United States":
            address_parts.append(self.installation_country)
        
        return "\n".join(address_parts)
    
    def is_active(self) -> bool:
        """Check if customer is active"""
        return self.status == CustomerStatus.ACTIVE
    
    def activate(self):
        """Activate customer"""
        self.status = CustomerStatus.ACTIVE
        self.disconnection_date = None
    
    def deactivate(self, reason: str = None):
        """Deactivate customer"""
        self.status = CustomerStatus.INACTIVE
        self.disconnection_date = date.today()
        if reason:
            self.add_note(f"Deactivated: {reason}")
    
    def suspend(self, reason: str = None):
        """Suspend customer"""
        self.status = CustomerStatus.SUSPENDED
        if reason:
            self.add_note(f"Suspended: {reason}")
    
    def terminate(self, reason: str = None):
        """Terminate customer"""
        self.status = CustomerStatus.TERMINATED
        self.disconnection_date = date.today()
        if reason:
            self.add_note(f"Terminated: {reason}")
    
    def add_note(self, note: str):
        """Add a note to customer"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_note = f"[{timestamp}] {note}"
        
        if self.notes:
            self.notes += f"\n{new_note}"
        else:
            self.notes = new_note
    
    def get_tags_list(self) -> list:
        """Get tags as a list"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]
    
    def add_tag(self, tag: str):
        """Add a tag to customer"""
        current_tags = self.get_tags_list()
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = ", ".join(current_tags)
    
    def remove_tag(self, tag: str):
        """Remove a tag from customer"""
        current_tags = self.get_tags_list()
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = ", ".join(current_tags) if current_tags else None
    
    def get_service_duration_days(self) -> int:
        """Get number of days since connection"""
        if self.disconnection_date:
            end_date = self.disconnection_date
        else:
            end_date = date.today()
        
        return (end_date - self.connection_date).days
    
    def get_service_duration_months(self) -> int:
        """Get approximate number of months since connection"""
        return self.get_service_duration_days() // 30
    
    def can_receive_email(self) -> bool:
        """Check if customer can receive email notifications"""
        return bool(self.email and self.send_email_notifications)
    
    def can_receive_sms(self) -> bool:
        """Check if customer can receive SMS notifications"""
        return bool(self.phone and self.send_sms_notifications)
    
    def get_preferred_contact(self) -> str:
        """Get preferred contact information"""
        if self.preferred_contact_method == "email" and self.email:
            return self.email
        elif self.preferred_contact_method == "phone" and self.phone:
            return self.phone
        elif self.preferred_contact_method == "sms" and self.phone:
            return self.phone
        else:
            return self.email or self.phone
    
    def __repr__(self):
        return f"<Customer(id={self.id}, name='{self.full_name}', status='{self.status.value}')>"
