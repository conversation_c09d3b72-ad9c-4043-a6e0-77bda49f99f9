{"ast": null, "code": "function decay({\n  /**\n   * The decay animation dynamically calculates an end of the animation\n   * based on the initial keyframe, so we only need to define a single keyframe\n   * as default.\n   */\n  keyframes = [0],\n  velocity = 0,\n  power = 0.8,\n  timeConstant = 350,\n  restDelta = 0.5,\n  modifyTarget\n}) {\n  const origin = keyframes[0];\n  /**\n   * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n   * to reduce GC during animation.\n   */\n  const state = {\n    done: false,\n    value: origin\n  };\n  let amplitude = power * velocity;\n  const ideal = origin + amplitude;\n  const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n  /**\n   * If the target has changed we need to re-calculate the amplitude, otherwise\n   * the animation will start from the wrong position.\n   */\n  if (target !== ideal) amplitude = target - origin;\n  return {\n    next: t => {\n      const delta = -amplitude * Math.exp(-t / timeConstant);\n      state.done = !(delta > restDelta || delta < -restDelta);\n      state.value = state.done ? target : target + delta;\n      return state;\n    },\n    flipTarget: () => {}\n  };\n}\nexport { decay };", "map": {"version": 3, "names": ["decay", "keyframes", "velocity", "power", "timeConstant", "restDelta", "modifyTarget", "origin", "state", "done", "value", "amplitude", "ideal", "target", "undefined", "next", "t", "delta", "Math", "exp", "flipTarget"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/legacy-popmotion/decay.mjs"], "sourcesContent": ["function decay({ \n/**\n * The decay animation dynamically calculates an end of the animation\n * based on the initial keyframe, so we only need to define a single keyframe\n * as default.\n */\nkeyframes = [0], velocity = 0, power = 0.8, timeConstant = 350, restDelta = 0.5, modifyTarget, }) {\n    const origin = keyframes[0];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    let amplitude = power * velocity;\n    const ideal = origin + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - origin;\n    return {\n        next: (t) => {\n            const delta = -amplitude * Math.exp(-t / timeConstant);\n            state.done = !(delta > restDelta || delta < -restDelta);\n            state.value = state.done ? target : target + delta;\n            return state;\n        },\n        flipTarget: () => { },\n    };\n}\n\nexport { decay };\n"], "mappings": "AAAA,SAASA,KAAKA,CAAC;EACf;AACA;AACA;AACA;AACA;EACAC,SAAS,GAAG,CAAC,CAAC,CAAC;EAAEC,QAAQ,GAAG,CAAC;EAAEC,KAAK,GAAG,GAAG;EAAEC,YAAY,GAAG,GAAG;EAAEC,SAAS,GAAG,GAAG;EAAEC;AAAc,CAAC,EAAE;EAC9F,MAAMC,MAAM,GAAGN,SAAS,CAAC,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACI,MAAMO,KAAK,GAAG;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAEH;EAAO,CAAC;EAC5C,IAAII,SAAS,GAAGR,KAAK,GAAGD,QAAQ;EAChC,MAAMU,KAAK,GAAGL,MAAM,GAAGI,SAAS;EAChC,MAAME,MAAM,GAAGP,YAAY,KAAKQ,SAAS,GAAGF,KAAK,GAAGN,YAAY,CAACM,KAAK,CAAC;EACvE;AACJ;AACA;AACA;EACI,IAAIC,MAAM,KAAKD,KAAK,EAChBD,SAAS,GAAGE,MAAM,GAAGN,MAAM;EAC/B,OAAO;IACHQ,IAAI,EAAGC,CAAC,IAAK;MACT,MAAMC,KAAK,GAAG,CAACN,SAAS,GAAGO,IAAI,CAACC,GAAG,CAAC,CAACH,CAAC,GAAGZ,YAAY,CAAC;MACtDI,KAAK,CAACC,IAAI,GAAG,EAAEQ,KAAK,GAAGZ,SAAS,IAAIY,KAAK,GAAG,CAACZ,SAAS,CAAC;MACvDG,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACC,IAAI,GAAGI,MAAM,GAAGA,MAAM,GAAGI,KAAK;MAClD,OAAOT,KAAK;IAChB,CAAC;IACDY,UAAU,EAAEA,CAAA,KAAM,CAAE;EACxB,CAAC;AACL;AAEA,SAASpB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}