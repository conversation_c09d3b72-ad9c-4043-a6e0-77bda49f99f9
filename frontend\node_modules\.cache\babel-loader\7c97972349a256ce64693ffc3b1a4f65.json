{"ast": null, "code": "const progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\nexport { progress };", "map": {"version": 3, "names": ["progress", "min", "max", "value"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/progress.es.js"], "sourcesContent": ["const progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\nexport { progress };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGD,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAACE,KAAK,GAAGF,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC;AAEvF,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}