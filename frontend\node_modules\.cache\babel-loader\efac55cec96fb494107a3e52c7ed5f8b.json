{"ast": null, "code": "import { time } from '@motionone/utils';\nimport { defaults } from './defaults.es.js';\nimport { calcDampingRatio } from './utils.es.js';\nimport { hasReachedTarget } from '../utils/has-reached-target.es.js';\nimport { calcGeneratorVelocity } from '../utils/velocity.es.js';\nconst spring = ({\n  stiffness = defaults.stiffness,\n  damping = defaults.damping,\n  mass = defaults.mass,\n  from = 0,\n  to = 1,\n  velocity = 0.0,\n  restSpeed,\n  restDistance\n} = {}) => {\n  velocity = velocity ? time.s(velocity) : 0.0;\n  const state = {\n    done: false,\n    hasReachedTarget: false,\n    current: from,\n    target: to\n  };\n  const initialDelta = to - from;\n  const undampedAngularFreq = Math.sqrt(stiffness / mass) / 1000;\n  const dampingRatio = calcDampingRatio(stiffness, damping, mass);\n  const isGranularScale = Math.abs(initialDelta) < 5;\n  restSpeed || (restSpeed = isGranularScale ? 0.01 : 2);\n  restDistance || (restDistance = isGranularScale ? 0.005 : 0.5);\n  let resolveSpring;\n  if (dampingRatio < 1) {\n    const angularFreq = undampedAngularFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n    // Underdamped spring (bouncy)\n    resolveSpring = t => to - Math.exp(-dampingRatio * undampedAngularFreq * t) * ((-velocity + dampingRatio * undampedAngularFreq * initialDelta) / angularFreq * Math.sin(angularFreq * t) + initialDelta * Math.cos(angularFreq * t));\n  } else {\n    // Critically damped spring\n    resolveSpring = t => {\n      return to - Math.exp(-undampedAngularFreq * t) * (initialDelta + (-velocity + undampedAngularFreq * initialDelta) * t);\n    };\n  }\n  return t => {\n    state.current = resolveSpring(t);\n    const currentVelocity = t === 0 ? velocity : calcGeneratorVelocity(resolveSpring, t, state.current);\n    const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n    const isBelowDisplacementThreshold = Math.abs(to - state.current) <= restDistance;\n    state.done = isBelowVelocityThreshold && isBelowDisplacementThreshold;\n    state.hasReachedTarget = hasReachedTarget(from, to, state.current);\n    return state;\n  };\n};\nexport { spring };", "map": {"version": 3, "names": ["time", "defaults", "calcDampingRatio", "hasReached<PERSON><PERSON><PERSON>", "calcGeneratorVelocity", "spring", "stiffness", "damping", "mass", "from", "to", "velocity", "restSpeed", "restDistance", "s", "state", "done", "current", "target", "initialDelta", "undampedAngularFreq", "Math", "sqrt", "dampingRatio", "isGranularScale", "abs", "resolveSpring", "angularFreq", "t", "exp", "sin", "cos", "currentVelocity", "isBelowVelocityThreshold", "isBelowDisplacementThreshold"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/generators/dist/spring/index.es.js"], "sourcesContent": ["import { time } from '@motionone/utils';\nimport { defaults } from './defaults.es.js';\nimport { calcDampingRatio } from './utils.es.js';\nimport { hasReachedTarget } from '../utils/has-reached-target.es.js';\nimport { calcGeneratorVelocity } from '../utils/velocity.es.js';\n\nconst spring = ({ stiffness = defaults.stiffness, damping = defaults.damping, mass = defaults.mass, from = 0, to = 1, velocity = 0.0, restSpeed, restDistance, } = {}) => {\n    velocity = velocity ? time.s(velocity) : 0.0;\n    const state = {\n        done: false,\n        hasReachedTarget: false,\n        current: from,\n        target: to,\n    };\n    const initialDelta = to - from;\n    const undampedAngularFreq = Math.sqrt(stiffness / mass) / 1000;\n    const dampingRatio = calcDampingRatio(stiffness, damping, mass);\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale ? 0.01 : 2);\n    restDistance || (restDistance = isGranularScale ? 0.005 : 0.5);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = undampedAngularFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n        // Underdamped spring (bouncy)\n        resolveSpring = (t) => to -\n            Math.exp(-dampingRatio * undampedAngularFreq * t) *\n                (((-velocity + dampingRatio * undampedAngularFreq * initialDelta) /\n                    angularFreq) *\n                    Math.sin(angularFreq * t) +\n                    initialDelta * Math.cos(angularFreq * t));\n    }\n    else {\n        // Critically damped spring\n        resolveSpring = (t) => {\n            return (to -\n                Math.exp(-undampedAngularFreq * t) *\n                    (initialDelta + (-velocity + undampedAngularFreq * initialDelta) * t));\n        };\n    }\n    return (t) => {\n        state.current = resolveSpring(t);\n        const currentVelocity = t === 0\n            ? velocity\n            : calcGeneratorVelocity(resolveSpring, t, state.current);\n        const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n        const isBelowDisplacementThreshold = Math.abs(to - state.current) <= restDistance;\n        state.done = isBelowVelocityThreshold && isBelowDisplacementThreshold;\n        state.hasReachedTarget = hasReachedTarget(from, to, state.current);\n        return state;\n    };\n};\n\nexport { spring };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,MAAMC,MAAM,GAAGA,CAAC;EAAEC,SAAS,GAAGL,QAAQ,CAACK,SAAS;EAAEC,OAAO,GAAGN,QAAQ,CAACM,OAAO;EAAEC,IAAI,GAAGP,QAAQ,CAACO,IAAI;EAAEC,IAAI,GAAG,CAAC;EAAEC,EAAE,GAAG,CAAC;EAAEC,QAAQ,GAAG,GAAG;EAAEC,SAAS;EAAEC;AAAc,CAAC,GAAG,CAAC,CAAC,KAAK;EACtKF,QAAQ,GAAGA,QAAQ,GAAGX,IAAI,CAACc,CAAC,CAACH,QAAQ,CAAC,GAAG,GAAG;EAC5C,MAAMI,KAAK,GAAG;IACVC,IAAI,EAAE,KAAK;IACXb,gBAAgB,EAAE,KAAK;IACvBc,OAAO,EAAER,IAAI;IACbS,MAAM,EAAER;EACZ,CAAC;EACD,MAAMS,YAAY,GAAGT,EAAE,GAAGD,IAAI;EAC9B,MAAMW,mBAAmB,GAAGC,IAAI,CAACC,IAAI,CAAChB,SAAS,GAAGE,IAAI,CAAC,GAAG,IAAI;EAC9D,MAAMe,YAAY,GAAGrB,gBAAgB,CAACI,SAAS,EAAEC,OAAO,EAAEC,IAAI,CAAC;EAC/D,MAAMgB,eAAe,GAAGH,IAAI,CAACI,GAAG,CAACN,YAAY,CAAC,GAAG,CAAC;EAClDP,SAAS,KAAKA,SAAS,GAAGY,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC;EACrDX,YAAY,KAAKA,YAAY,GAAGW,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC;EAC9D,IAAIE,aAAa;EACjB,IAAIH,YAAY,GAAG,CAAC,EAAE;IAClB,MAAMI,WAAW,GAAGP,mBAAmB,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGC,YAAY,GAAGA,YAAY,CAAC;IACpF;IACAG,aAAa,GAAIE,CAAC,IAAKlB,EAAE,GACrBW,IAAI,CAACQ,GAAG,CAAC,CAACN,YAAY,GAAGH,mBAAmB,GAAGQ,CAAC,CAAC,IAC3C,CAAC,CAACjB,QAAQ,GAAGY,YAAY,GAAGH,mBAAmB,GAAGD,YAAY,IAC5DQ,WAAW,GACXN,IAAI,CAACS,GAAG,CAACH,WAAW,GAAGC,CAAC,CAAC,GACzBT,YAAY,GAAGE,IAAI,CAACU,GAAG,CAACJ,WAAW,GAAGC,CAAC,CAAC,CAAC;EACzD,CAAC,MACI;IACD;IACAF,aAAa,GAAIE,CAAC,IAAK;MACnB,OAAQlB,EAAE,GACNW,IAAI,CAACQ,GAAG,CAAC,CAACT,mBAAmB,GAAGQ,CAAC,CAAC,IAC7BT,YAAY,GAAG,CAAC,CAACR,QAAQ,GAAGS,mBAAmB,GAAGD,YAAY,IAAIS,CAAC,CAAC;IACjF,CAAC;EACL;EACA,OAAQA,CAAC,IAAK;IACVb,KAAK,CAACE,OAAO,GAAGS,aAAa,CAACE,CAAC,CAAC;IAChC,MAAMI,eAAe,GAAGJ,CAAC,KAAK,CAAC,GACzBjB,QAAQ,GACRP,qBAAqB,CAACsB,aAAa,EAAEE,CAAC,EAAEb,KAAK,CAACE,OAAO,CAAC;IAC5D,MAAMgB,wBAAwB,GAAGZ,IAAI,CAACI,GAAG,CAACO,eAAe,CAAC,IAAIpB,SAAS;IACvE,MAAMsB,4BAA4B,GAAGb,IAAI,CAACI,GAAG,CAACf,EAAE,GAAGK,KAAK,CAACE,OAAO,CAAC,IAAIJ,YAAY;IACjFE,KAAK,CAACC,IAAI,GAAGiB,wBAAwB,IAAIC,4BAA4B;IACrEnB,KAAK,CAACZ,gBAAgB,GAAGA,gBAAgB,CAACM,IAAI,EAAEC,EAAE,EAAEK,KAAK,CAACE,OAAO,CAAC;IAClE,OAAOF,KAAK;EAChB,CAAC;AACL,CAAC;AAED,SAASV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}