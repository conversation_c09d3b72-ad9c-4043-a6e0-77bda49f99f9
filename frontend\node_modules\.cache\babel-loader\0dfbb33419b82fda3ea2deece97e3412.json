{"ast": null, "code": "function hasReachedTarget(origin, target, current) {\n  return origin < target && current >= target || origin > target && current <= target;\n}\nexport { hasReachedTarget };", "map": {"version": 3, "names": ["hasReached<PERSON><PERSON><PERSON>", "origin", "target", "current"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/generators/dist/utils/has-reached-target.es.js"], "sourcesContent": ["function hasReachedTarget(origin, target, current) {\n    return ((origin < target && current >= target) ||\n        (origin > target && current <= target));\n}\n\nexport { hasReachedTarget };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/C,OAASF,MAAM,GAAGC,MAAM,IAAIC,OAAO,IAAID,MAAM,IACxCD,MAAM,GAAGC,MAAM,IAAIC,OAAO,IAAID,MAAO;AAC9C;AAEA,SAASF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}