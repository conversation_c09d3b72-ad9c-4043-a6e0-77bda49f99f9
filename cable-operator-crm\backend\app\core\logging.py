"""
Logging configuration for the Cable Operator CRM application
"""

import logging
import logging.handlers
import sys
from pathlib import Path
import structlog
from typing import Any

from app.core.config import settings


def setup_logging():
    """Setup structured logging with rotation"""
    
    # Create logs directory
    log_dir = Path(settings.LOG_FILE).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Setup file handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        settings.LOG_FILE,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
    )
    file_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.ENVIRONMENT == "production"
            else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Add file handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)


class LoggerMixin:
    """Mixin to add logging capabilities to classes"""
    
    @property
    def logger(self):
        """Get logger for the class"""
        return structlog.get_logger(self.__class__.__name__)


def get_logger(name: str = None) -> Any:
    """Get a structured logger"""
    return structlog.get_logger(name)


# Security logger for audit trails
security_logger = structlog.get_logger("security")
audit_logger = structlog.get_logger("audit")
business_logger = structlog.get_logger("business")


def log_security_event(event_type: str, user_id: int = None, details: dict = None):
    """Log security-related events"""
    security_logger.info(
        "Security event",
        event_type=event_type,
        user_id=user_id,
        details=details or {},
    )


def log_audit_event(action: str, resource: str, user_id: int = None, details: dict = None):
    """Log audit events for compliance"""
    audit_logger.info(
        "Audit event",
        action=action,
        resource=resource,
        user_id=user_id,
        details=details or {},
    )


def log_business_event(event: str, data: dict = None):
    """Log business events for analytics"""
    business_logger.info(
        "Business event",
        event=event,
        data=data or {},
    )


# Export logging utilities
__all__ = [
    "setup_logging",
    "LoggerMixin",
    "get_logger",
    "log_security_event",
    "log_audit_event",
    "log_business_event",
    "security_logger",
    "audit_logger",
    "business_logger",
]
