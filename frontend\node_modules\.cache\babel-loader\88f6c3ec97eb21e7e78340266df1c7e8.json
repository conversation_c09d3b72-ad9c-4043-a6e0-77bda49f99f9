{"ast": null, "code": "/**\n * Browser-safe usage of process\n */\nconst defaultEnvironment = \"production\";\nconst env = typeof process === \"undefined\" || process.env === undefined ? defaultEnvironment : process.env.NODE_ENV || defaultEnvironment;\nexport { env };", "map": {"version": 3, "names": ["defaultEnvironment", "env", "process", "undefined", "NODE_ENV"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/utils/process.mjs"], "sourcesContent": ["/**\n * Browser-safe usage of process\n */\nconst defaultEnvironment = \"production\";\nconst env = typeof process === \"undefined\" || process.env === undefined\n    ? defaultEnvironment\n    : process.env.NODE_ENV || defaultEnvironment;\n\nexport { env };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,kBAAkB,GAAG,YAAY;AACvC,MAAMC,GAAG,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACD,GAAG,KAAKE,SAAS,GACjEH,kBAAkB,GAClBE,OAAO,CAACD,GAAG,CAACG,QAAQ,IAAIJ,kBAAkB;AAEhD,SAASC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}