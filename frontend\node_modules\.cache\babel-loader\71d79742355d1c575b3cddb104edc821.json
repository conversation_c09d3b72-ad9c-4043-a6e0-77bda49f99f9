{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nlet scheduled = undefined;\nfunction processScheduledAnimations() {\n  if (!scheduled) return;\n  const generators = scheduled.sort(compareByDepth).map(fireAnimateUpdates);\n  generators.forEach(fireNext);\n  generators.forEach(fireNext);\n  scheduled = undefined;\n}\nfunction scheduleAnimation(state) {\n  if (!scheduled) {\n    scheduled = [state];\n    requestAnimationFrame(processScheduledAnimations);\n  } else {\n    utils.addUniqueItem(scheduled, state);\n  }\n}\nfunction unscheduleAnimation(state) {\n  scheduled && utils.removeItem(scheduled, state);\n}\nconst compareByDepth = (a, b) => a.getDepth() - b.getDepth();\nconst fireAnimateUpdates = state => state.animateUpdates();\nconst fireNext = iterator => iterator.next();\nexports.scheduleAnimation = scheduleAnimation;\nexports.unscheduleAnimation = unscheduleAnimation;", "map": {"version": 3, "names": ["utils", "require", "scheduled", "undefined", "processScheduledAnimations", "generators", "sort", "compareByDepth", "map", "fireAnimateUpdates", "for<PERSON>ach", "fireNext", "scheduleAnimation", "state", "requestAnimationFrame", "addUniqueItem", "unscheduleAnimation", "removeItem", "a", "b", "<PERSON><PERSON><PERSON>h", "animateUpdates", "iterator", "next", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/state/utils/schedule.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\n\nlet scheduled = undefined;\nfunction processScheduledAnimations() {\n    if (!scheduled)\n        return;\n    const generators = scheduled.sort(compareByDepth).map(fireAnimateUpdates);\n    generators.forEach(fireNext);\n    generators.forEach(fireNext);\n    scheduled = undefined;\n}\nfunction scheduleAnimation(state) {\n    if (!scheduled) {\n        scheduled = [state];\n        requestAnimationFrame(processScheduledAnimations);\n    }\n    else {\n        utils.addUniqueItem(scheduled, state);\n    }\n}\nfunction unscheduleAnimation(state) {\n    scheduled && utils.removeItem(scheduled, state);\n}\nconst compareByDepth = (a, b) => a.getDepth() - b.getDepth();\nconst fireAnimateUpdates = (state) => state.animateUpdates();\nconst fireNext = (iterator) => iterator.next();\n\nexports.scheduleAnimation = scheduleAnimation;\nexports.unscheduleAnimation = unscheduleAnimation;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,IAAIC,SAAS,GAAGC,SAAS;AACzB,SAASC,0BAA0BA,CAAA,EAAG;EAClC,IAAI,CAACF,SAAS,EACV;EACJ,MAAMG,UAAU,GAAGH,SAAS,CAACI,IAAI,CAACC,cAAc,CAAC,CAACC,GAAG,CAACC,kBAAkB,CAAC;EACzEJ,UAAU,CAACK,OAAO,CAACC,QAAQ,CAAC;EAC5BN,UAAU,CAACK,OAAO,CAACC,QAAQ,CAAC;EAC5BT,SAAS,GAAGC,SAAS;AACzB;AACA,SAASS,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,IAAI,CAACX,SAAS,EAAE;IACZA,SAAS,GAAG,CAACW,KAAK,CAAC;IACnBC,qBAAqB,CAACV,0BAA0B,CAAC;EACrD,CAAC,MACI;IACDJ,KAAK,CAACe,aAAa,CAACb,SAAS,EAAEW,KAAK,CAAC;EACzC;AACJ;AACA,SAASG,mBAAmBA,CAACH,KAAK,EAAE;EAChCX,SAAS,IAAIF,KAAK,CAACiB,UAAU,CAACf,SAAS,EAAEW,KAAK,CAAC;AACnD;AACA,MAAMN,cAAc,GAAGA,CAACW,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,CAAC,CAAC,GAAGD,CAAC,CAACC,QAAQ,CAAC,CAAC;AAC5D,MAAMX,kBAAkB,GAAII,KAAK,IAAKA,KAAK,CAACQ,cAAc,CAAC,CAAC;AAC5D,MAAMV,QAAQ,GAAIW,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC;AAE9CC,OAAO,CAACZ,iBAAiB,GAAGA,iBAAiB;AAC7CY,OAAO,CAACR,mBAAmB,GAAGA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}