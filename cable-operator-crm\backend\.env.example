# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/cable_operator_db
# For development with SQLite:
# DATABASE_URL=sqlite:///./cable_operator.db

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://yourdomain.com

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Cable Operator CRM

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB

# Redis (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# Environment
ENVIRONMENT=development
DEBUG=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Company Default Settings
DEFAULT_COMPANY_NAME=Cable Operator CRM
DEFAULT_CURRENCY=USD
DEFAULT_TAX_RATE=0.18

# Backup
BACKUP_DIR=./backups
AUTO_BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30

# API Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Sentry (Error Monitoring)
SENTRY_DSN=your-sentry-dsn-here
