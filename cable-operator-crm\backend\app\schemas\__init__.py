"""
Pydantic schemas for API request/response validation
"""

from .auth import (
    LoginRequest, LoginResponse, RefreshTokenRequest, 
    UserCreate, UserUpdate, UserResponse, ChangePasswordRequest
)
from .company import (
    CompanyCreate, CompanyUpdate, CompanyResponse, 
    CompanySetup, CompanySettings
)
from .customer import (
    CustomerCreate, CustomerUpdate, CustomerResponse,
    CustomerSearch, CustomerFilter, CustomerStats
)
from .plan import (
    PlanCreate, PlanUpdate, PlanResponse,
    PlanFilter, PlanStats
)
from .payment import (
    PaymentCreate, PaymentUpdate, PaymentResponse,
    PaymentFilter, PaymentStats, PaymentSummary
)
from .invoice import (
    InvoiceCreate, InvoiceUpdate, InvoiceResponse,
    InvoiceItemCreate, InvoiceItemResponse,
    InvoiceFilter, InvoiceStats
)
from .notification import (
    NotificationCreate, NotificationUpdate, NotificationResponse,
    NotificationFilter, EmailNotificationCreate, SMSNotificationCreate
)
from .common import (
    PaginationParams, PaginatedResponse, SearchParams,
    SortParams, FilterParams, ResponseMessage, ErrorResponse
)

__all__ = [
    # Auth
    "LoginRequest",
    "LoginResponse", 
    "RefreshTokenRequest",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "ChangePasswordRequest",
    
    # Company
    "CompanyCreate",
    "CompanyUpdate",
    "CompanyResponse",
    "CompanySetup",
    "CompanySettings",
    
    # Customer
    "CustomerCreate",
    "CustomerUpdate",
    "CustomerResponse",
    "CustomerSearch",
    "CustomerFilter",
    "CustomerStats",
    
    # Plan
    "PlanCreate",
    "PlanUpdate",
    "PlanResponse",
    "PlanFilter",
    "PlanStats",
    
    # Payment
    "PaymentCreate",
    "PaymentUpdate",
    "PaymentResponse",
    "PaymentFilter",
    "PaymentStats",
    "PaymentSummary",
    
    # Invoice
    "InvoiceCreate",
    "InvoiceUpdate",
    "InvoiceResponse",
    "InvoiceItemCreate",
    "InvoiceItemResponse",
    "InvoiceFilter",
    "InvoiceStats",
    
    # Notification
    "NotificationCreate",
    "NotificationUpdate",
    "NotificationResponse",
    "NotificationFilter",
    "EmailNotificationCreate",
    "SMSNotificationCreate",
    
    # Common
    "PaginationParams",
    "PaginatedResponse",
    "SearchParams",
    "SortParams",
    "FilterParams",
    "ResponseMessage",
    "ErrorResponse",
]
