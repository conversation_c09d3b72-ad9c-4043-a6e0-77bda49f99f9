{"ast": null, "code": "import { keyframes } from './keyframes.mjs';\nimport { spring } from './spring.mjs';\nimport { decay } from './decay.mjs';\nimport { sync, cancelSync } from '../../frameloop/index.mjs';\nimport { interpolate } from '../../utils/interpolate.mjs';\nconst types = {\n  decay,\n  keyframes: keyframes,\n  tween: keyframes,\n  spring\n};\nfunction loopElapsed(elapsed, duration, delay = 0) {\n  return elapsed - duration - delay;\n}\nfunction reverseElapsed(elapsed, duration = 0, delay = 0, isForwardPlayback = true) {\n  return isForwardPlayback ? loopElapsed(duration + -elapsed, duration, delay) : duration - (elapsed - duration) + delay;\n}\nfunction hasRepeatDelayElapsed(elapsed, duration, delay, isForwardPlayback) {\n  return isForwardPlayback ? elapsed >= duration + delay : elapsed <= -delay;\n}\nconst framesync = update => {\n  const passTimestamp = ({\n    delta\n  }) => update(delta);\n  return {\n    start: () => sync.update(passTimestamp, true),\n    stop: () => cancelSync.update(passTimestamp)\n  };\n};\nfunction animate({\n  duration,\n  driver = framesync,\n  elapsed = 0,\n  repeat: repeatMax = 0,\n  repeatType = \"loop\",\n  repeatDelay = 0,\n  keyframes,\n  autoplay = true,\n  onPlay,\n  onStop,\n  onComplete,\n  onRepeat,\n  onUpdate,\n  type = \"keyframes\",\n  ...options\n}) {\n  var _a, _b;\n  let driverControls;\n  let repeatCount = 0;\n  let computedDuration = duration;\n  let latest;\n  let isComplete = false;\n  let isForwardPlayback = true;\n  let interpolateFromNumber;\n  const animator = types[keyframes.length > 2 ? \"keyframes\" : type];\n  const origin = keyframes[0];\n  const target = keyframes[keyframes.length - 1];\n  if ((_b = (_a = animator).needsInterpolation) === null || _b === void 0 ? void 0 : _b.call(_a, origin, target)) {\n    interpolateFromNumber = interpolate([0, 100], [origin, target], {\n      clamp: false\n    });\n    keyframes = [0, 100];\n  }\n  const animation = animator({\n    ...options,\n    duration,\n    keyframes\n  });\n  function repeat() {\n    repeatCount++;\n    if (repeatType === \"reverse\") {\n      isForwardPlayback = repeatCount % 2 === 0;\n      elapsed = reverseElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback);\n    } else {\n      elapsed = loopElapsed(elapsed, computedDuration, repeatDelay);\n      if (repeatType === \"mirror\") animation.flipTarget();\n    }\n    isComplete = false;\n    onRepeat && onRepeat();\n  }\n  function complete() {\n    driverControls.stop();\n    onComplete && onComplete();\n  }\n  function update(delta) {\n    if (!isForwardPlayback) delta = -delta;\n    elapsed += delta;\n    if (!isComplete) {\n      const state = animation.next(Math.max(0, elapsed));\n      latest = state.value;\n      if (interpolateFromNumber) latest = interpolateFromNumber(latest);\n      isComplete = isForwardPlayback ? state.done : elapsed <= 0;\n    }\n    onUpdate && onUpdate(latest);\n    if (isComplete) {\n      if (repeatCount === 0) {\n        computedDuration = computedDuration !== undefined ? computedDuration : elapsed;\n      }\n      if (repeatCount < repeatMax) {\n        hasRepeatDelayElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback) && repeat();\n      } else {\n        complete();\n      }\n    }\n  }\n  function play() {\n    onPlay && onPlay();\n    driverControls = driver(update);\n    driverControls.start();\n  }\n  autoplay && play();\n  return {\n    stop: () => {\n      onStop && onStop();\n      driverControls.stop();\n    },\n    sample: t => {\n      return animation.next(Math.max(0, t));\n    }\n  };\n}\nexport { animate, hasRepeatDelayElapsed, loopElapsed, reverseElapsed };", "map": {"version": 3, "names": ["keyframes", "spring", "decay", "sync", "cancelSync", "interpolate", "types", "tween", "loopElapsed", "elapsed", "duration", "delay", "reverseElapsed", "isForward<PERSON>layback", "hasRepeatDelayElapsed", "framesync", "update", "passTimestamp", "delta", "start", "stop", "animate", "driver", "repeat", "repeatMax", "repeatType", "repeatDelay", "autoplay", "onPlay", "onStop", "onComplete", "onRepeat", "onUpdate", "type", "options", "_a", "_b", "driverControls", "repeatCount", "computedDuration", "latest", "isComplete", "interpolateFromNumber", "animator", "length", "origin", "target", "needsInterpolation", "call", "clamp", "animation", "flipTarget", "complete", "state", "next", "Math", "max", "value", "done", "undefined", "play", "sample", "t"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/legacy-popmotion/index.mjs"], "sourcesContent": ["import { keyframes } from './keyframes.mjs';\nimport { spring } from './spring.mjs';\nimport { decay } from './decay.mjs';\nimport { sync, cancelSync } from '../../frameloop/index.mjs';\nimport { interpolate } from '../../utils/interpolate.mjs';\n\nconst types = {\n    decay,\n    keyframes: keyframes,\n    tween: keyframes,\n    spring,\n};\nfunction loopElapsed(elapsed, duration, delay = 0) {\n    return elapsed - duration - delay;\n}\nfunction reverseElapsed(elapsed, duration = 0, delay = 0, isForwardPlayback = true) {\n    return isForwardPlayback\n        ? loopElapsed(duration + -elapsed, duration, delay)\n        : duration - (elapsed - duration) + delay;\n}\nfunction hasRepeatDelayElapsed(elapsed, duration, delay, isForwardPlayback) {\n    return isForwardPlayback ? elapsed >= duration + delay : elapsed <= -delay;\n}\nconst framesync = (update) => {\n    const passTimestamp = ({ delta }) => update(delta);\n    return {\n        start: () => sync.update(passTimestamp, true),\n        stop: () => cancelSync.update(passTimestamp),\n    };\n};\nfunction animate({ duration, driver = framesync, elapsed = 0, repeat: repeatMax = 0, repeatType = \"loop\", repeatDelay = 0, keyframes, autoplay = true, onPlay, onStop, onComplete, onRepeat, onUpdate, type = \"keyframes\", ...options }) {\n    var _a, _b;\n    let driverControls;\n    let repeatCount = 0;\n    let computedDuration = duration;\n    let latest;\n    let isComplete = false;\n    let isForwardPlayback = true;\n    let interpolateFromNumber;\n    const animator = types[keyframes.length > 2 ? \"keyframes\" : type];\n    const origin = keyframes[0];\n    const target = keyframes[keyframes.length - 1];\n    if ((_b = (_a = animator).needsInterpolation) === null || _b === void 0 ? void 0 : _b.call(_a, origin, target)) {\n        interpolateFromNumber = interpolate([0, 100], [origin, target], {\n            clamp: false,\n        });\n        keyframes = [0, 100];\n    }\n    const animation = animator({\n        ...options,\n        duration,\n        keyframes,\n    });\n    function repeat() {\n        repeatCount++;\n        if (repeatType === \"reverse\") {\n            isForwardPlayback = repeatCount % 2 === 0;\n            elapsed = reverseElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback);\n        }\n        else {\n            elapsed = loopElapsed(elapsed, computedDuration, repeatDelay);\n            if (repeatType === \"mirror\")\n                animation.flipTarget();\n        }\n        isComplete = false;\n        onRepeat && onRepeat();\n    }\n    function complete() {\n        driverControls.stop();\n        onComplete && onComplete();\n    }\n    function update(delta) {\n        if (!isForwardPlayback)\n            delta = -delta;\n        elapsed += delta;\n        if (!isComplete) {\n            const state = animation.next(Math.max(0, elapsed));\n            latest = state.value;\n            if (interpolateFromNumber)\n                latest = interpolateFromNumber(latest);\n            isComplete = isForwardPlayback ? state.done : elapsed <= 0;\n        }\n        onUpdate && onUpdate(latest);\n        if (isComplete) {\n            if (repeatCount === 0) {\n                computedDuration =\n                    computedDuration !== undefined ? computedDuration : elapsed;\n            }\n            if (repeatCount < repeatMax) {\n                hasRepeatDelayElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback) && repeat();\n            }\n            else {\n                complete();\n            }\n        }\n    }\n    function play() {\n        onPlay && onPlay();\n        driverControls = driver(update);\n        driverControls.start();\n    }\n    autoplay && play();\n    return {\n        stop: () => {\n            onStop && onStop();\n            driverControls.stop();\n        },\n        sample: (t) => {\n            return animation.next(Math.max(0, t));\n        },\n    };\n}\n\nexport { animate, hasRepeatDelayElapsed, loopElapsed, reverseElapsed };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,IAAI,EAAEC,UAAU,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,QAAQ,6BAA6B;AAEzD,MAAMC,KAAK,GAAG;EACVJ,KAAK;EACLF,SAAS,EAAEA,SAAS;EACpBO,KAAK,EAAEP,SAAS;EAChBC;AACJ,CAAC;AACD,SAASO,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC/C,OAAOF,OAAO,GAAGC,QAAQ,GAAGC,KAAK;AACrC;AACA,SAASC,cAAcA,CAACH,OAAO,EAAEC,QAAQ,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEE,iBAAiB,GAAG,IAAI,EAAE;EAChF,OAAOA,iBAAiB,GAClBL,WAAW,CAACE,QAAQ,GAAG,CAACD,OAAO,EAAEC,QAAQ,EAAEC,KAAK,CAAC,GACjDD,QAAQ,IAAID,OAAO,GAAGC,QAAQ,CAAC,GAAGC,KAAK;AACjD;AACA,SAASG,qBAAqBA,CAACL,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEE,iBAAiB,EAAE;EACxE,OAAOA,iBAAiB,GAAGJ,OAAO,IAAIC,QAAQ,GAAGC,KAAK,GAAGF,OAAO,IAAI,CAACE,KAAK;AAC9E;AACA,MAAMI,SAAS,GAAIC,MAAM,IAAK;EAC1B,MAAMC,aAAa,GAAGA,CAAC;IAAEC;EAAM,CAAC,KAAKF,MAAM,CAACE,KAAK,CAAC;EAClD,OAAO;IACHC,KAAK,EAAEA,CAAA,KAAMhB,IAAI,CAACa,MAAM,CAACC,aAAa,EAAE,IAAI,CAAC;IAC7CG,IAAI,EAAEA,CAAA,KAAMhB,UAAU,CAACY,MAAM,CAACC,aAAa;EAC/C,CAAC;AACL,CAAC;AACD,SAASI,OAAOA,CAAC;EAAEX,QAAQ;EAAEY,MAAM,GAAGP,SAAS;EAAEN,OAAO,GAAG,CAAC;EAAEc,MAAM,EAAEC,SAAS,GAAG,CAAC;EAAEC,UAAU,GAAG,MAAM;EAAEC,WAAW,GAAG,CAAC;EAAE1B,SAAS;EAAE2B,QAAQ,GAAG,IAAI;EAAEC,MAAM;EAAEC,MAAM;EAAEC,UAAU;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,IAAI,GAAG,WAAW;EAAE,GAAGC;AAAQ,CAAC,EAAE;EACrO,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,cAAc;EAClB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,gBAAgB,GAAG7B,QAAQ;EAC/B,IAAI8B,MAAM;EACV,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAI5B,iBAAiB,GAAG,IAAI;EAC5B,IAAI6B,qBAAqB;EACzB,MAAMC,QAAQ,GAAGrC,KAAK,CAACN,SAAS,CAAC4C,MAAM,GAAG,CAAC,GAAG,WAAW,GAAGX,IAAI,CAAC;EACjE,MAAMY,MAAM,GAAG7C,SAAS,CAAC,CAAC,CAAC;EAC3B,MAAM8C,MAAM,GAAG9C,SAAS,CAACA,SAAS,CAAC4C,MAAM,GAAG,CAAC,CAAC;EAC9C,IAAI,CAACR,EAAE,GAAG,CAACD,EAAE,GAAGQ,QAAQ,EAAEI,kBAAkB,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,IAAI,CAACb,EAAE,EAAEU,MAAM,EAAEC,MAAM,CAAC,EAAE;IAC5GJ,qBAAqB,GAAGrC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAACwC,MAAM,EAAEC,MAAM,CAAC,EAAE;MAC5DG,KAAK,EAAE;IACX,CAAC,CAAC;IACFjD,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EACxB;EACA,MAAMkD,SAAS,GAAGP,QAAQ,CAAC;IACvB,GAAGT,OAAO;IACVxB,QAAQ;IACRV;EACJ,CAAC,CAAC;EACF,SAASuB,MAAMA,CAAA,EAAG;IACde,WAAW,EAAE;IACb,IAAIb,UAAU,KAAK,SAAS,EAAE;MAC1BZ,iBAAiB,GAAGyB,WAAW,GAAG,CAAC,KAAK,CAAC;MACzC7B,OAAO,GAAGG,cAAc,CAACH,OAAO,EAAE8B,gBAAgB,EAAEb,WAAW,EAAEb,iBAAiB,CAAC;IACvF,CAAC,MACI;MACDJ,OAAO,GAAGD,WAAW,CAACC,OAAO,EAAE8B,gBAAgB,EAAEb,WAAW,CAAC;MAC7D,IAAID,UAAU,KAAK,QAAQ,EACvByB,SAAS,CAACC,UAAU,CAAC,CAAC;IAC9B;IACAV,UAAU,GAAG,KAAK;IAClBV,QAAQ,IAAIA,QAAQ,CAAC,CAAC;EAC1B;EACA,SAASqB,QAAQA,CAAA,EAAG;IAChBf,cAAc,CAACjB,IAAI,CAAC,CAAC;IACrBU,UAAU,IAAIA,UAAU,CAAC,CAAC;EAC9B;EACA,SAASd,MAAMA,CAACE,KAAK,EAAE;IACnB,IAAI,CAACL,iBAAiB,EAClBK,KAAK,GAAG,CAACA,KAAK;IAClBT,OAAO,IAAIS,KAAK;IAChB,IAAI,CAACuB,UAAU,EAAE;MACb,MAAMY,KAAK,GAAGH,SAAS,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/C,OAAO,CAAC,CAAC;MAClD+B,MAAM,GAAGa,KAAK,CAACI,KAAK;MACpB,IAAIf,qBAAqB,EACrBF,MAAM,GAAGE,qBAAqB,CAACF,MAAM,CAAC;MAC1CC,UAAU,GAAG5B,iBAAiB,GAAGwC,KAAK,CAACK,IAAI,GAAGjD,OAAO,IAAI,CAAC;IAC9D;IACAuB,QAAQ,IAAIA,QAAQ,CAACQ,MAAM,CAAC;IAC5B,IAAIC,UAAU,EAAE;MACZ,IAAIH,WAAW,KAAK,CAAC,EAAE;QACnBC,gBAAgB,GACZA,gBAAgB,KAAKoB,SAAS,GAAGpB,gBAAgB,GAAG9B,OAAO;MACnE;MACA,IAAI6B,WAAW,GAAGd,SAAS,EAAE;QACzBV,qBAAqB,CAACL,OAAO,EAAE8B,gBAAgB,EAAEb,WAAW,EAAEb,iBAAiB,CAAC,IAAIU,MAAM,CAAC,CAAC;MAChG,CAAC,MACI;QACD6B,QAAQ,CAAC,CAAC;MACd;IACJ;EACJ;EACA,SAASQ,IAAIA,CAAA,EAAG;IACZhC,MAAM,IAAIA,MAAM,CAAC,CAAC;IAClBS,cAAc,GAAGf,MAAM,CAACN,MAAM,CAAC;IAC/BqB,cAAc,CAAClB,KAAK,CAAC,CAAC;EAC1B;EACAQ,QAAQ,IAAIiC,IAAI,CAAC,CAAC;EAClB,OAAO;IACHxC,IAAI,EAAEA,CAAA,KAAM;MACRS,MAAM,IAAIA,MAAM,CAAC,CAAC;MAClBQ,cAAc,CAACjB,IAAI,CAAC,CAAC;IACzB,CAAC;IACDyC,MAAM,EAAGC,CAAC,IAAK;MACX,OAAOZ,SAAS,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEM,CAAC,CAAC,CAAC;IACzC;EACJ,CAAC;AACL;AAEA,SAASzC,OAAO,EAAEP,qBAAqB,EAAEN,WAAW,EAAEI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}