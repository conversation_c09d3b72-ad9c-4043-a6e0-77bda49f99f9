{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\nconst namedEdges = {\n  start: 0,\n  center: 0.5,\n  end: 1\n};\nfunction resolveEdge(edge, length, inset = 0) {\n  let delta = 0;\n  /**\n   * If we have this edge defined as a preset, replace the definition\n   * with the numerical value.\n   */\n  if (namedEdges[edge] !== undefined) {\n    edge = namedEdges[edge];\n  }\n  /**\n   * Handle unit values\n   */\n  if (utils.isString(edge)) {\n    const asNumber = parseFloat(edge);\n    if (edge.endsWith(\"px\")) {\n      delta = asNumber;\n    } else if (edge.endsWith(\"%\")) {\n      edge = asNumber / 100;\n    } else if (edge.endsWith(\"vw\")) {\n      delta = asNumber / 100 * document.documentElement.clientWidth;\n    } else if (edge.endsWith(\"vh\")) {\n      delta = asNumber / 100 * document.documentElement.clientHeight;\n    } else {\n      edge = asNumber;\n    }\n  }\n  /**\n   * If the edge is defined as a number, handle as a progress value.\n   */\n  if (utils.isNumber(edge)) {\n    delta = length * edge;\n  }\n  return inset + delta;\n}\nexports.namedEdges = namedEdges;\nexports.resolveEdge = resolveEdge;", "map": {"version": 3, "names": ["utils", "require", "<PERSON><PERSON><PERSON>", "start", "center", "end", "resolveEdge", "edge", "length", "inset", "delta", "undefined", "isString", "asNumber", "parseFloat", "endsWith", "document", "documentElement", "clientWidth", "clientHeight", "isNumber", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/offsets/edge.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\n\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (namedEdges[edge] !== undefined) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (utils.isString(edge)) {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (utils.isNumber(edge)) {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\nexports.namedEdges = namedEdges;\nexports.resolveEdge = resolveEdge;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEvC,MAAMC,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE;AACT,CAAC;AACD,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC1C,IAAIC,KAAK,GAAG,CAAC;EACb;AACJ;AACA;AACA;EACI,IAAIR,UAAU,CAACK,IAAI,CAAC,KAAKI,SAAS,EAAE;IAChCJ,IAAI,GAAGL,UAAU,CAACK,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;EACI,IAAIP,KAAK,CAACY,QAAQ,CAACL,IAAI,CAAC,EAAE;IACtB,MAAMM,QAAQ,GAAGC,UAAU,CAACP,IAAI,CAAC;IACjC,IAAIA,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrBL,KAAK,GAAGG,QAAQ;IACpB,CAAC,MACI,IAAIN,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzBR,IAAI,GAAGM,QAAQ,GAAG,GAAG;IACzB,CAAC,MACI,IAAIN,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BL,KAAK,GAAIG,QAAQ,GAAG,GAAG,GAAIG,QAAQ,CAACC,eAAe,CAACC,WAAW;IACnE,CAAC,MACI,IAAIX,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BL,KAAK,GAAIG,QAAQ,GAAG,GAAG,GAAIG,QAAQ,CAACC,eAAe,CAACE,YAAY;IACpE,CAAC,MACI;MACDZ,IAAI,GAAGM,QAAQ;IACnB;EACJ;EACA;AACJ;AACA;EACI,IAAIb,KAAK,CAACoB,QAAQ,CAACb,IAAI,CAAC,EAAE;IACtBG,KAAK,GAAGF,MAAM,GAAGD,IAAI;EACzB;EACA,OAAOE,KAAK,GAAGC,KAAK;AACxB;AAEAW,OAAO,CAACnB,UAAU,GAAGA,UAAU;AAC/BmB,OAAO,CAACf,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}