"""
User model with role-based access control
"""

import enum
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Date<PERSON><PERSON>, En<PERSON>, <PERSON>SO<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import BaseModel


class UserRole(enum.Enum):
    """User roles with hierarchical permissions"""
    SUPER_ADMIN = "super_admin"  # System administrator
    OWNER = "owner"              # Company owner
    ADMIN = "admin"              # Company administrator
    STAFF = "staff"              # Regular staff member
    VIEWER = "viewer"            # Read-only access


class UserPermission(enum.Enum):
    """Granular permissions for different operations"""
    # Customer permissions
    CUSTOMER_READ = "customer:read"
    CUSTOMER_WRITE = "customer:write"
    CUSTOMER_DELETE = "customer:delete"
    
    # Plan permissions
    PLAN_READ = "plan:read"
    PLAN_WRITE = "plan:write"
    PLAN_DELETE = "plan:delete"
    
    # Payment permissions
    PAYMENT_READ = "payment:read"
    PAYMENT_WRITE = "payment:write"
    PAYMENT_DELETE = "payment:delete"
    
    # Invoice permissions
    INVOICE_READ = "invoice:read"
    INVOICE_WRITE = "invoice:write"
    INVOICE_DELETE = "invoice:delete"
    
    # Report permissions
    REPORT_VIEW = "report:view"
    REPORT_EXPORT = "report:export"
    
    # User management permissions
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    
    # Company settings permissions
    COMPANY_READ = "company:read"
    COMPANY_WRITE = "company:write"
    
    # System permissions
    SYSTEM_BACKUP = "system:backup"
    SYSTEM_LOGS = "system:logs"


class User(BaseModel):
    """User model with role-based access control"""
    
    __tablename__ = "users"
    
    # Basic Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), nullable=False, unique=True, index=True)
    phone = Column(String(20), nullable=True)
    
    # Authentication
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)
    is_verified = Column(Boolean, nullable=False, default=False)
    
    # Role and Permissions
    role = Column(Enum(UserRole), nullable=False, default=UserRole.STAFF)
    permissions = Column(JSON, nullable=False, default=list)
    
    # Company Association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Profile Information
    avatar_url = Column(String(500), nullable=True)
    bio = Column(String(500), nullable=True)
    timezone = Column(String(50), nullable=False, default="UTC")
    language = Column(String(10), nullable=False, default="en")
    
    # Security
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    last_login_ip = Column(String(45), nullable=True)
    failed_login_attempts = Column(Integer, nullable=False, default=0)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    
    # Password Reset
    reset_token = Column(String(255), nullable=True)
    reset_token_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Email Verification
    verification_token = Column(String(255), nullable=True)
    verification_token_expires = Column(DateTime(timezone=True), nullable=True)
    
    # API Access
    api_key = Column(String(255), nullable=True, unique=True)
    api_key_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Preferences
    preferences = Column(JSON, nullable=False, default=dict)
    
    # Relationships
    company = relationship("Company", back_populates="users")
    created_customers = relationship("Customer", foreign_keys="Customer.created_by_id")
    created_payments = relationship("Payment", foreign_keys="Payment.created_by_id")
    created_invoices = relationship("Invoice", foreign_keys="Invoice.created_by_id")
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return ['first_name', 'last_name', 'email', 'phone']
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return ['role', 'is_active', 'is_verified', 'company_id']
    
    @property
    def full_name(self):
        """Get user's full name"""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def initials(self):
        """Get user's initials"""
        return f"{self.first_name[0]}{self.last_name[0]}".upper()
    
    def has_permission(self, permission: UserPermission) -> bool:
        """Check if user has a specific permission"""
        # Super admin has all permissions
        if self.role == UserRole.SUPER_ADMIN:
            return True
        
        # Check role-based permissions
        role_permissions = self.get_role_permissions()
        if permission.value in role_permissions:
            return True
        
        # Check custom permissions
        return permission.value in (self.permissions or [])
    
    def has_any_permission(self, permissions: list) -> bool:
        """Check if user has any of the specified permissions"""
        return any(self.has_permission(perm) for perm in permissions)
    
    def has_all_permissions(self, permissions: list) -> bool:
        """Check if user has all of the specified permissions"""
        return all(self.has_permission(perm) for perm in permissions)
    
    def get_role_permissions(self) -> list:
        """Get permissions based on user role"""
        role_permissions = {
            UserRole.SUPER_ADMIN: [perm.value for perm in UserPermission],
            UserRole.OWNER: [
                UserPermission.CUSTOMER_READ.value,
                UserPermission.CUSTOMER_WRITE.value,
                UserPermission.CUSTOMER_DELETE.value,
                UserPermission.PLAN_READ.value,
                UserPermission.PLAN_WRITE.value,
                UserPermission.PLAN_DELETE.value,
                UserPermission.PAYMENT_READ.value,
                UserPermission.PAYMENT_WRITE.value,
                UserPermission.PAYMENT_DELETE.value,
                UserPermission.INVOICE_READ.value,
                UserPermission.INVOICE_WRITE.value,
                UserPermission.INVOICE_DELETE.value,
                UserPermission.REPORT_VIEW.value,
                UserPermission.REPORT_EXPORT.value,
                UserPermission.USER_READ.value,
                UserPermission.USER_WRITE.value,
                UserPermission.USER_DELETE.value,
                UserPermission.COMPANY_READ.value,
                UserPermission.COMPANY_WRITE.value,
                UserPermission.SYSTEM_BACKUP.value,
            ],
            UserRole.ADMIN: [
                UserPermission.CUSTOMER_READ.value,
                UserPermission.CUSTOMER_WRITE.value,
                UserPermission.CUSTOMER_DELETE.value,
                UserPermission.PLAN_READ.value,
                UserPermission.PLAN_WRITE.value,
                UserPermission.PAYMENT_READ.value,
                UserPermission.PAYMENT_WRITE.value,
                UserPermission.INVOICE_READ.value,
                UserPermission.INVOICE_WRITE.value,
                UserPermission.REPORT_VIEW.value,
                UserPermission.REPORT_EXPORT.value,
                UserPermission.USER_READ.value,
                UserPermission.COMPANY_READ.value,
            ],
            UserRole.STAFF: [
                UserPermission.CUSTOMER_READ.value,
                UserPermission.CUSTOMER_WRITE.value,
                UserPermission.PLAN_READ.value,
                UserPermission.PAYMENT_READ.value,
                UserPermission.PAYMENT_WRITE.value,
                UserPermission.INVOICE_READ.value,
                UserPermission.REPORT_VIEW.value,
            ],
            UserRole.VIEWER: [
                UserPermission.CUSTOMER_READ.value,
                UserPermission.PLAN_READ.value,
                UserPermission.PAYMENT_READ.value,
                UserPermission.INVOICE_READ.value,
                UserPermission.REPORT_VIEW.value,
            ],
        }
        
        return role_permissions.get(self.role, [])
    
    def add_permission(self, permission: UserPermission):
        """Add a custom permission to user"""
        if not self.permissions:
            self.permissions = []
        
        if permission.value not in self.permissions:
            self.permissions.append(permission.value)
    
    def remove_permission(self, permission: UserPermission):
        """Remove a custom permission from user"""
        if self.permissions and permission.value in self.permissions:
            self.permissions.remove(permission.value)
    
    def is_locked(self) -> bool:
        """Check if user account is locked"""
        if self.locked_until:
            return self.locked_until > func.now()
        return False
    
    def lock_account(self, duration_minutes: int = 30):
        """Lock user account for specified duration"""
        from datetime import datetime, timedelta
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
    
    def unlock_account(self):
        """Unlock user account"""
        self.locked_until = None
        self.failed_login_attempts = 0
    
    def record_login(self, ip_address: str = None):
        """Record successful login"""
        self.last_login_at = func.now()
        self.last_login_ip = ip_address
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def record_failed_login(self):
        """Record failed login attempt"""
        self.failed_login_attempts += 1
        
        # Lock account after 5 failed attempts
        if self.failed_login_attempts >= 5:
            self.lock_account(30)  # Lock for 30 minutes
    
    def get_preference(self, key: str, default=None):
        """Get user preference"""
        return self.preferences.get(key, default) if self.preferences else default
    
    def set_preference(self, key: str, value):
        """Set user preference"""
        if not self.preferences:
            self.preferences = {}
        self.preferences[key] = value
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', role='{self.role.value}')>"
