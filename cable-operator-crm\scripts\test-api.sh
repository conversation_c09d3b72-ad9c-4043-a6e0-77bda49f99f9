#!/bin/bash

# Cable Operator CRM - API Testing Script
# This script tests the backend API endpoints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:8000/api/v1"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
TOKEN=""

# Functions
log() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    local expected_status=${5:-200}
    
    log "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$TOKEN" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $TOKEN'"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd $API_BASE_URL$endpoint"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        success "$description (Status: $status_code)"
        if [ ! -z "$body" ] && [ "$body" != "null" ]; then
            echo "Response: $body" | head -c 200
            echo ""
        fi
    else
        error "$description (Expected: $expected_status, Got: $status_code)"
        echo "Response: $body"
    fi
    
    echo ""
}

# Login and get token
login() {
    log "Logging in to get authentication token..."
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}" \
        $API_BASE_URL/auth/login)
    
    TOKEN=$(echo $response | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
    
    if [ ! -z "$TOKEN" ]; then
        success "Login successful, token obtained"
    else
        error "Login failed"
        echo "Response: $response"
        exit 1
    fi
    
    echo ""
}

# Test health endpoint
test_health() {
    log "=== Testing Health Endpoints ==="
    test_endpoint "GET" "/health" "" "Health check" 200
    test_endpoint "GET" "/../health" "" "Root health check" 200
}

# Test authentication endpoints
test_auth() {
    log "=== Testing Authentication Endpoints ==="
    
    # Test login
    test_endpoint "POST" "/auth/login" \
        "{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}" \
        "Admin login" 200
    
    # Test invalid login
    test_endpoint "POST" "/auth/login" \
        "{\"email\":\"<EMAIL>\",\"password\":\"wrongpassword\"}" \
        "Invalid login" 401
    
    # Test protected endpoint without token
    TOKEN_BACKUP=$TOKEN
    TOKEN=""
    test_endpoint "GET" "/users/me" "" "Protected endpoint without token" 401
    TOKEN=$TOKEN_BACKUP
    
    # Test protected endpoint with token
    test_endpoint "GET" "/users/me" "" "Get current user" 200
}

# Test customer endpoints
test_customers() {
    log "=== Testing Customer Endpoints ==="
    
    # Get customers
    test_endpoint "GET" "/customers" "" "Get all customers" 200
    
    # Create customer
    local customer_data='{
        "first_name": "Test",
        "last_name": "Customer",
        "email": "<EMAIL>",
        "phone": "******-999-0001",
        "address_line1": "123 Test St",
        "city": "Test City",
        "state": "Test State",
        "postal_code": "12345",
        "service_type": "internet",
        "plan_id": 1
    }'
    
    test_endpoint "POST" "/customers" "$customer_data" "Create customer" 201
    
    # Get customer by ID
    test_endpoint "GET" "/customers/1" "" "Get customer by ID" 200
    
    # Search customers
    test_endpoint "GET" "/customers?search=Test" "" "Search customers" 200
}

# Test plan endpoints
test_plans() {
    log "=== Testing Plan Endpoints ==="
    
    # Get plans
    test_endpoint "GET" "/plans" "" "Get all plans" 200
    
    # Create plan
    local plan_data='{
        "name": "Test Plan",
        "description": "Test internet plan",
        "plan_type": "internet",
        "price": 49.99,
        "internet_speed_download": "50 Mbps",
        "internet_speed_upload": "10 Mbps"
    }'
    
    test_endpoint "POST" "/plans" "$plan_data" "Create plan" 201
    
    # Get plan by ID
    test_endpoint "GET" "/plans/1" "" "Get plan by ID" 200
}

# Test payment endpoints
test_payments() {
    log "=== Testing Payment Endpoints ==="
    
    # Get payments
    test_endpoint "GET" "/payments" "" "Get all payments" 200
    
    # Create payment
    local payment_data='{
        "amount": 49.99,
        "customer_id": 1,
        "plan_id": 1,
        "billing_month": 12,
        "billing_year": 2024,
        "due_date": "2024-12-01",
        "billing_period_start": "2024-12-01",
        "billing_period_end": "2024-12-31"
    }'
    
    test_endpoint "POST" "/payments" "$payment_data" "Create payment" 201
    
    # Get payment by ID
    test_endpoint "GET" "/payments/1" "" "Get payment by ID" 200
}

# Test statistics endpoints
test_stats() {
    log "=== Testing Statistics Endpoints ==="
    
    test_endpoint "GET" "/stats" "" "Get dashboard statistics" 200
    test_endpoint "GET" "/stats/customers" "" "Get customer statistics" 200
    test_endpoint "GET" "/stats/revenue" "" "Get revenue statistics" 200
}

# Test error handling
test_errors() {
    log "=== Testing Error Handling ==="
    
    # Test 404
    test_endpoint "GET" "/customers/99999" "" "Non-existent customer" 404
    
    # Test invalid data
    test_endpoint "POST" "/customers" '{"invalid": "data"}' "Invalid customer data" 422
    
    # Test method not allowed
    test_endpoint "DELETE" "/auth/login" "" "Method not allowed" 405
}

# Run all tests
run_all_tests() {
    log "🚀 Starting API Tests for Cable Operator CRM"
    echo ""
    
    # Check if backend is running
    if ! curl -s http://localhost:8000/health > /dev/null; then
        error "Backend server is not running on http://localhost:8000"
        echo "Please start the backend server first:"
        echo "  cd backend && uvicorn app.main:app --reload"
        exit 1
    fi
    
    # Login to get token
    login
    
    # Run test suites
    test_health
    test_auth
    test_customers
    test_plans
    test_payments
    test_stats
    test_errors
    
    success "🎉 All API tests completed!"
}

# Show API documentation
show_docs() {
    log "📚 API Documentation URLs:"
    echo "  Interactive Docs: http://localhost:8000/docs"
    echo "  ReDoc: http://localhost:8000/redoc"
    echo "  OpenAPI JSON: http://localhost:8000/openapi.json"
    echo ""
    log "🔑 Default Credentials:"
    echo "  Email: $ADMIN_EMAIL"
    echo "  Password: $ADMIN_PASSWORD"
}

# Test specific endpoint
test_specific() {
    local endpoint=$1
    if [ -z "$endpoint" ]; then
        error "Please specify an endpoint to test"
        echo "Usage: $0 test /customers"
        exit 1
    fi
    
    login
    test_endpoint "GET" "$endpoint" "" "Custom endpoint test" 200
}

# Main execution
case "$1" in
    "all")
        run_all_tests
        ;;
    "health")
        test_health
        ;;
    "auth")
        login
        test_auth
        ;;
    "customers")
        login
        test_customers
        ;;
    "plans")
        login
        test_plans
        ;;
    "payments")
        login
        test_payments
        ;;
    "stats")
        login
        test_stats
        ;;
    "errors")
        login
        test_errors
        ;;
    "docs")
        show_docs
        ;;
    "test")
        test_specific "$2"
        ;;
    *)
        log "Cable Operator CRM - API Testing Script"
        echo ""
        echo "Usage: $0 {all|health|auth|customers|plans|payments|stats|errors|docs|test}"
        echo ""
        echo "Commands:"
        echo "  all        - Run all API tests"
        echo "  health     - Test health endpoints"
        echo "  auth       - Test authentication"
        echo "  customers  - Test customer endpoints"
        echo "  plans      - Test plan endpoints"
        echo "  payments   - Test payment endpoints"
        echo "  stats      - Test statistics endpoints"
        echo "  errors     - Test error handling"
        echo "  docs       - Show API documentation URLs"
        echo "  test <endpoint> - Test specific endpoint"
        echo ""
        echo "Examples:"
        echo "  $0 all"
        echo "  $0 customers"
        echo "  $0 test /customers/1"
        echo ""
        echo "Prerequisites:"
        echo "  - Backend server running on http://localhost:8000"
        echo "  - Admin user created with credentials: $ADMIN_EMAIL / $ADMIN_PASSWORD"
        ;;
esac
