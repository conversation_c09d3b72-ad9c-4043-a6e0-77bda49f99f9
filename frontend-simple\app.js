// API Configuration
const API_BASE_URL = 'http://localhost:5000/api';
let authToken = localStorage.getItem('token');

// API Helper Functions
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        },
        ...options
    };

    try {
        const response = await fetch(url, config);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        let data;
        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = { error: 'Server returned non-JSON response' };
        }

        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        return data;
    } catch (error) {
        console.error('API Error:', error);
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('Cannot connect to server. Please ensure the backend is running on http://localhost:5000');
        }
        throw error;
    }
}

// Authentication Functions
async function login(username, password) {
    try {
        const data = await apiCall('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        });
        
        authToken = data.access_token;
        localStorage.setItem('token', authToken);
        return true;
    } catch (error) {
        throw error;
    }
}

function logout() {
    authToken = null;
    localStorage.removeItem('token');
    showLoginSection();
}

// UI Functions
function showLoginSection() {
    document.getElementById('loginSection').classList.remove('hidden');
    document.getElementById('dashboardSection').classList.add('hidden');
}

function showDashboardSection() {
    document.getElementById('loginSection').classList.add('hidden');
    document.getElementById('dashboardSection').classList.remove('hidden');
    showSection('stats');
}

function showSection(sectionName) {
    // Hide all sections
    document.getElementById('statsSection').classList.add('hidden');
    document.getElementById('customersSection').classList.add('hidden');
    document.getElementById('plansSection').classList.add('hidden');
    document.getElementById('paymentsSection').classList.add('hidden');
    
    // Show selected section
    document.getElementById(sectionName + 'Section').classList.remove('hidden');
    
    // Load data for the section
    switch(sectionName) {
        case 'stats':
            loadStats();
            break;
        case 'customers':
            loadCustomers();
            break;
        case 'plans':
            loadPlans();
            break;
        case 'payments':
            loadPayments();
            break;
    }
}

// Data Loading Functions
async function loadStats() {
    try {
        const stats = await apiCall('/stats');
        document.getElementById('totalCustomers').textContent = stats.customers;
        document.getElementById('totalPlans').textContent = stats.plans;
        document.getElementById('paidPayments').textContent = stats.paid;
        document.getElementById('unpaidPayments').textContent = stats.unpaid;
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

async function loadCustomers() {
    try {
        const customers = await apiCall('/customers');
        const tbody = document.getElementById('customersTableBody');
        tbody.innerHTML = '';
        
        customers.forEach(customer => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${customer.name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.phone}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.address}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.service_type.replace('ServiceTypeEnum.', '')}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.plan ? customer.plan.name : 'No Plan'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="deleteCustomer(${customer.id})" class="text-red-600 hover:text-red-900">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('Error loading customers:', error);
    }
}

async function loadPlans() {
    try {
        const plans = await apiCall('/plans');
        const grid = document.getElementById('plansGrid');
        grid.innerHTML = '';
        
        plans.forEach(plan => {
            const card = document.createElement('div');
            card.className = 'bg-white p-6 rounded-lg shadow-md';
            card.innerHTML = `
                <h3 class="text-lg font-semibold mb-2">${plan.name}</h3>
                <p class="text-gray-600 mb-2">Type: ${plan.type.replace('ServiceTypeEnum.', '')}</p>
                <p class="text-2xl font-bold text-green-600 mb-4">$${plan.price}</p>
                <button onclick="deletePlan(${plan.id})" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">Delete</button>
            `;
            grid.appendChild(card);
        });
    } catch (error) {
        console.error('Error loading plans:', error);
    }
}

async function loadPayments() {
    try {
        const payments = await apiCall('/payments');
        const tbody = document.getElementById('paymentsTableBody');
        tbody.innerHTML = '';
        
        payments.forEach(payment => {
            const row = document.createElement('tr');
            const statusClass = payment.status === 'Paid' ? 'text-green-600' : 'text-red-600';
            const paidAt = payment.paid_at ? new Date(payment.paid_at).toLocaleDateString() : '-';
            
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${payment.customer ? payment.customer.name : 'Unknown'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${payment.month_year}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm ${statusClass}">${payment.status}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${paidAt}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="togglePaymentStatus(${payment.id}, '${payment.status}')" class="text-blue-600 hover:text-blue-900 mr-2">
                        ${payment.status === 'Paid' ? 'Mark Unpaid' : 'Mark Paid'}
                    </button>
                    <button onclick="deletePayment(${payment.id})" class="text-red-600 hover:text-red-900">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('Error loading payments:', error);
    }
}

// CRUD Operations
async function deleteCustomer(id) {
    if (confirm('Are you sure you want to delete this customer?')) {
        try {
            await apiCall(`/customers/${id}`, { method: 'DELETE' });
            loadCustomers();
        } catch (error) {
            alert('Error deleting customer: ' + error.message);
        }
    }
}

async function deletePlan(id) {
    if (confirm('Are you sure you want to delete this plan?')) {
        try {
            await apiCall(`/plans/${id}`, { method: 'DELETE' });
            loadPlans();
        } catch (error) {
            alert('Error deleting plan: ' + error.message);
        }
    }
}

async function deletePayment(id) {
    if (confirm('Are you sure you want to delete this payment?')) {
        try {
            await apiCall(`/payments/${id}`, { method: 'DELETE' });
            loadPayments();
        } catch (error) {
            alert('Error deleting payment: ' + error.message);
        }
    }
}

async function togglePaymentStatus(id, currentStatus) {
    const newStatus = currentStatus === 'Paid' ? 'Unpaid' : 'Paid';
    try {
        await apiCall(`/payments/${id}`, {
            method: 'PUT',
            body: JSON.stringify({ status: newStatus })
        });
        loadPayments();
    } catch (error) {
        alert('Error updating payment status: ' + error.message);
    }
}

// Form Functions (simplified for demo)
function showAddCustomerForm() {
    alert('Add Customer form would open here. This is a demo - use the React frontend for full functionality.');
}

function showAddPlanForm() {
    alert('Add Plan form would open here. This is a demo - use the React frontend for full functionality.');
}

function showAddPaymentForm() {
    alert('Add Payment form would open here. This is a demo - use the React frontend for full functionality.');
}

// Event Listeners
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');
    
    try {
        await login(username, password);
        showDashboardSection();
        errorDiv.classList.add('hidden');
    } catch (error) {
        errorDiv.textContent = error.message;
        errorDiv.classList.remove('hidden');
    }
});

// Test backend connection
async function testConnection() {
    try {
        const response = await fetch(`${API_BASE_URL}/stats`);
        console.log('Backend connection test:', response.status);
        return response.status === 401; // 401 is expected without auth token
    } catch (error) {
        console.error('Backend connection failed:', error);
        return false;
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', async () => {
    // Test backend connection
    const backendAvailable = await testConnection();
    if (!backendAvailable) {
        const errorDiv = document.getElementById('loginError');
        errorDiv.textContent = 'Cannot connect to backend server. Please ensure it is running on http://localhost:5000';
        errorDiv.classList.remove('hidden');
    }

    if (authToken) {
        showDashboardSection();
    } else {
        showLoginSection();
    }
});
