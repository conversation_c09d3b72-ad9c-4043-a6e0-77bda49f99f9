{"ast": null, "code": "import { isNumber } from './is-number.es.js';\nconst isCubicBezier = easing => Array.isArray(easing) && isNumber(easing[0]);\nexport { isCubicBezier };", "map": {"version": 3, "names": ["isNumber", "isCubicBezier", "easing", "Array", "isArray"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/utils/dist/is-cubic-bezier.es.js"], "sourcesContent": ["import { isNumber } from './is-number.es.js';\n\nconst isCubicBezier = (easing) => Array.isArray(easing) && isNumber(easing[0]);\n\nexport { isCubicBezier };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAE5C,MAAMC,aAAa,GAAIC,MAAM,IAAKC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIF,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;AAE9E,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}