{"ast": null, "code": "'use strict';\n\nconst testAnimation = (keyframes, options) => document.createElement(\"div\").animate(keyframes, options);\nconst featureTests = {\n  cssRegisterProperty: () => typeof CSS !== \"undefined\" && Object.hasOwnProperty.call(CSS, \"registerProperty\"),\n  waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\"),\n  partialKeyframes: () => {\n    try {\n      testAnimation({\n        opacity: [1]\n      });\n    } catch (e) {\n      return false;\n    }\n    return true;\n  },\n  finished: () => Bo<PERSON>an(testAnimation({\n    opacity: [0, 1]\n  }, {\n    duration: 0.001\n  }).finished),\n  linearEasing: () => {\n    try {\n      testAnimation({\n        opacity: 0\n      }, {\n        easing: \"linear(0, 1)\"\n      });\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n};\nconst results = {};\nconst supports = {};\nfor (const key in featureTests) {\n  supports[key] = () => {\n    if (results[key] === undefined) results[key] = featureTests[key]();\n    return results[key];\n  };\n}\nexports.supports = supports;", "map": {"version": 3, "names": ["testAnimation", "keyframes", "options", "document", "createElement", "animate", "featureTests", "cssRegisterProperty", "CSS", "Object", "hasOwnProperty", "call", "waapi", "Element", "prototype", "partialKeyframes", "opacity", "e", "finished", "Boolean", "duration", "linearEasing", "easing", "results", "supports", "key", "undefined", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/utils/feature-detection.cjs.js"], "sourcesContent": ["'use strict';\n\nconst testAnimation = (keyframes, options) => document.createElement(\"div\").animate(keyframes, options);\nconst featureTests = {\n    cssRegisterProperty: () => typeof CSS !== \"undefined\" &&\n        Object.hasOwnProperty.call(CSS, \"registerProperty\"),\n    waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\"),\n    partialKeyframes: () => {\n        try {\n            testAnimation({ opacity: [1] });\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    },\n    finished: () => Bo<PERSON>an(testAnimation({ opacity: [0, 1] }, { duration: 0.001 }).finished),\n    linearEasing: () => {\n        try {\n            testAnimation({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    },\n};\nconst results = {};\nconst supports = {};\nfor (const key in featureTests) {\n    supports[key] = () => {\n        if (results[key] === undefined)\n            results[key] =\n                featureTests[key]();\n        return results[key];\n    };\n}\n\nexports.supports = supports;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,aAAa,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAKC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAACC,OAAO,CAACJ,SAAS,EAAEC,OAAO,CAAC;AACvG,MAAMI,YAAY,GAAG;EACjBC,mBAAmB,EAAEA,CAAA,KAAM,OAAOC,GAAG,KAAK,WAAW,IACjDC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACH,GAAG,EAAE,kBAAkB,CAAC;EACvDI,KAAK,EAAEA,CAAA,KAAMH,MAAM,CAACC,cAAc,CAACC,IAAI,CAACE,OAAO,CAACC,SAAS,EAAE,SAAS,CAAC;EACrEC,gBAAgB,EAAEA,CAAA,KAAM;IACpB,IAAI;MACAf,aAAa,CAAC;QAAEgB,OAAO,EAAE,CAAC,CAAC;MAAE,CAAC,CAAC;IACnC,CAAC,CACD,OAAOC,CAAC,EAAE;MACN,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EACDC,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACnB,aAAa,CAAC;IAAEgB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE;IAAEI,QAAQ,EAAE;EAAM,CAAC,CAAC,CAACF,QAAQ,CAAC;EACzFG,YAAY,EAAEA,CAAA,KAAM;IAChB,IAAI;MACArB,aAAa,CAAC;QAAEgB,OAAO,EAAE;MAAE,CAAC,EAAE;QAAEM,MAAM,EAAE;MAAe,CAAC,CAAC;IAC7D,CAAC,CACD,OAAOL,CAAC,EAAE;MACN,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;AACJ,CAAC;AACD,MAAMM,OAAO,GAAG,CAAC,CAAC;AAClB,MAAMC,QAAQ,GAAG,CAAC,CAAC;AACnB,KAAK,MAAMC,GAAG,IAAInB,YAAY,EAAE;EAC5BkB,QAAQ,CAACC,GAAG,CAAC,GAAG,MAAM;IAClB,IAAIF,OAAO,CAACE,GAAG,CAAC,KAAKC,SAAS,EAC1BH,OAAO,CAACE,GAAG,CAAC,GACRnB,YAAY,CAACmB,GAAG,CAAC,CAAC,CAAC;IAC3B,OAAOF,OAAO,CAACE,GAAG,CAAC;EACvB,CAAC;AACL;AAEAE,OAAO,CAACH,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}