{"ast": null, "code": "'use strict';\n\nvar animation = require('@motionone/animation');\nvar createAnimate = require('./create-animate.cjs.js');\nconst animate = createAnimate.createAnimate(animation.Animation);\nexports.animate = animate;", "map": {"version": 3, "names": ["animation", "require", "createAnimate", "animate", "Animation", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar animation = require('@motionone/animation');\nvar createAnimate = require('./create-animate.cjs.js');\n\nconst animate = createAnimate.createAnimate(animation.Animation);\n\nexports.animate = animate;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC/C,IAAIC,aAAa,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAEtD,MAAME,OAAO,GAAGD,aAAa,CAACA,aAAa,CAACF,SAAS,CAACI,SAAS,CAAC;AAEhEC,OAAO,CAACF,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script"}