"""
Plan model for managing service plans
"""

import enum
from sqlalchemy import <PERSON>umn, String, Boolean, ForeignKey, Integer, Float, Text, Enum, JSON
from sqlalchemy.orm import relationship

from .base import BaseModel


class PlanType(enum.Enum):
    """Plan type options"""
    CABLE = "cable"
    INTERNET = "internet"
    BUNDLE = "bundle"


class Plan(BaseModel):
    """Service plan model"""
    
    __tablename__ = "plans"
    
    # Basic Information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    plan_type = Column(Enum(PlanType), nullable=False, index=True)
    
    # Pricing
    price = Column(Float, nullable=False)
    setup_fee = Column(Float, nullable=False, default=0.0)
    currency = Column(String(3), nullable=False, default="USD")
    
    # Billing
    billing_cycle = Column(String(20), nullable=False, default="monthly")  # monthly, quarterly, yearly
    billing_cycle_months = Column(Integer, nullable=False, default=1)
    
    # Service Specifications
    internet_speed_download = Column(String(50), nullable=True)  # e.g., "100 Mbps"
    internet_speed_upload = Column(String(50), nullable=True)    # e.g., "10 Mbps"
    data_limit = Column(String(50), nullable=True)              # e.g., "Unlimited", "500 GB"
    
    # Cable Specifications
    channel_count = Column(Integer, nullable=True)
    hd_channels = Column(Integer, nullable=True)
    premium_channels = Column(Text, nullable=True)  # Comma-separated list
    
    # Features
    features = Column(JSON, nullable=False, default=list)  # List of features
    equipment_included = Column(JSON, nullable=False, default=list)  # List of equipment
    
    # Contract Terms
    contract_length_months = Column(Integer, nullable=False, default=0)  # 0 = no contract
    early_termination_fee = Column(Float, nullable=False, default=0.0)
    
    # Availability
    is_active = Column(Boolean, nullable=False, default=True)
    is_featured = Column(Boolean, nullable=False, default=False)
    is_promotional = Column(Boolean, nullable=False, default=False)
    
    # Promotional Pricing
    promotional_price = Column(Float, nullable=True)
    promotional_months = Column(Integer, nullable=True)
    promotional_description = Column(String(255), nullable=True)
    
    # Limits and Restrictions
    max_customers = Column(Integer, nullable=True)  # Maximum number of customers
    geographic_restrictions = Column(Text, nullable=True)
    
    # Company Association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Display Order
    sort_order = Column(Integer, nullable=False, default=0)
    
    # Additional Information
    terms_and_conditions = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Relationships
    company = relationship("Company", back_populates="plans")
    customers = relationship("Customer", back_populates="plan")
    
    @classmethod
    def get_searchable_columns(cls):
        """Columns that can be searched"""
        return ['name', 'description', 'internet_speed_download', 'premium_channels']
    
    @classmethod
    def get_filterable_columns(cls):
        """Columns that can be filtered"""
        return [
            'plan_type', 'is_active', 'is_featured', 'is_promotional',
            'billing_cycle', 'contract_length_months', 'company_id'
        ]
    
    @property
    def display_price(self):
        """Get formatted price for display"""
        if self.is_promotional and self.promotional_price is not None:
            return f"${self.promotional_price:.2f}"
        return f"${self.price:.2f}"
    
    @property
    def regular_price_display(self):
        """Get regular price for display (when promotional)"""
        return f"${self.price:.2f}"
    
    @property
    def effective_price(self):
        """Get the effective price (promotional or regular)"""
        if self.is_promotional and self.promotional_price is not None:
            return self.promotional_price
        return self.price
    
    def get_features_list(self) -> list:
        """Get features as a list"""
        return self.features or []
    
    def add_feature(self, feature: str):
        """Add a feature to the plan"""
        if not self.features:
            self.features = []
        if feature not in self.features:
            self.features.append(feature)
    
    def remove_feature(self, feature: str):
        """Remove a feature from the plan"""
        if self.features and feature in self.features:
            self.features.remove(feature)
    
    def get_equipment_list(self) -> list:
        """Get equipment as a list"""
        return self.equipment_included or []
    
    def add_equipment(self, equipment: str):
        """Add equipment to the plan"""
        if not self.equipment_included:
            self.equipment_included = []
        if equipment not in self.equipment_included:
            self.equipment_included.append(equipment)
    
    def remove_equipment(self, equipment: str):
        """Remove equipment from the plan"""
        if self.equipment_included and equipment in self.equipment_included:
            self.equipment_included.remove(equipment)
    
    def get_premium_channels_list(self) -> list:
        """Get premium channels as a list"""
        if not self.premium_channels:
            return []
        return [channel.strip() for channel in self.premium_channels.split(",") if channel.strip()]
    
    def set_premium_channels(self, channels: list):
        """Set premium channels from a list"""
        self.premium_channels = ", ".join(channels) if channels else None
    
    def is_internet_plan(self) -> bool:
        """Check if this is an internet plan"""
        return self.plan_type in [PlanType.INTERNET, PlanType.BUNDLE]
    
    def is_cable_plan(self) -> bool:
        """Check if this is a cable plan"""
        return self.plan_type in [PlanType.CABLE, PlanType.BUNDLE]
    
    def is_bundle_plan(self) -> bool:
        """Check if this is a bundle plan"""
        return self.plan_type == PlanType.BUNDLE
    
    def has_contract(self) -> bool:
        """Check if plan has a contract"""
        return self.contract_length_months > 0
    
    def get_billing_frequency(self) -> str:
        """Get billing frequency description"""
        frequency_map = {
            "monthly": "Monthly",
            "quarterly": "Quarterly",
            "yearly": "Yearly",
            "annual": "Annual",
        }
        return frequency_map.get(self.billing_cycle, self.billing_cycle.title())
    
    def get_contract_description(self) -> str:
        """Get contract description"""
        if not self.has_contract():
            return "No Contract"
        
        if self.contract_length_months == 12:
            return "1 Year Contract"
        elif self.contract_length_months == 24:
            return "2 Year Contract"
        else:
            years = self.contract_length_months // 12
            months = self.contract_length_months % 12
            
            if years > 0 and months > 0:
                return f"{years} Year {months} Month Contract"
            elif years > 0:
                return f"{years} Year Contract"
            else:
                return f"{months} Month Contract"
    
    def get_speed_description(self) -> str:
        """Get internet speed description"""
        if not self.is_internet_plan():
            return ""
        
        parts = []
        if self.internet_speed_download:
            parts.append(f"↓{self.internet_speed_download}")
        if self.internet_speed_upload:
            parts.append(f"↑{self.internet_speed_upload}")
        
        return " / ".join(parts)
    
    def get_channel_description(self) -> str:
        """Get cable channel description"""
        if not self.is_cable_plan():
            return ""
        
        parts = []
        if self.channel_count:
            parts.append(f"{self.channel_count} Channels")
        if self.hd_channels:
            parts.append(f"{self.hd_channels} HD")
        
        return " • ".join(parts)
    
    def calculate_total_cost(self, months: int = 1) -> float:
        """Calculate total cost for specified months"""
        monthly_cost = self.effective_price
        total = 0.0
        
        # Add setup fee
        total += self.setup_fee
        
        # Calculate monthly costs
        if self.is_promotional and self.promotional_months:
            # Apply promotional pricing for specified months
            promo_months = min(months, self.promotional_months)
            regular_months = months - promo_months
            
            total += promo_months * self.promotional_price
            total += regular_months * self.price
        else:
            total += months * monthly_cost
        
        return total
    
    def get_customer_count(self) -> int:
        """Get number of customers using this plan"""
        return len(self.customers) if self.customers else 0
    
    def can_accept_new_customers(self) -> bool:
        """Check if plan can accept new customers"""
        if not self.is_active:
            return False
        
        if self.max_customers is not None:
            return self.get_customer_count() < self.max_customers
        
        return True
    
    def __repr__(self):
        return f"<Plan(id={self.id}, name='{self.name}', type='{self.plan_type.value}', price={self.price})>"
