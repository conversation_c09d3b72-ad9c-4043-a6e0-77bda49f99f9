{"ast": null, "code": "'use strict';\n\nvar generators = require('@motionone/generators');\nvar createGeneratorEasing = require('../create-generator-easing.cjs.js');\nconst spring = createGeneratorEasing.createGeneratorEasing(generators.spring);\nexports.spring = spring;", "map": {"version": 3, "names": ["generators", "require", "createGeneratorEasing", "spring", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/easing/spring/index.cjs.js"], "sourcesContent": ["'use strict';\n\nvar generators = require('@motionone/generators');\nvar createGeneratorEasing = require('../create-generator-easing.cjs.js');\n\nconst spring = createGeneratorEasing.createGeneratorEasing(generators.spring);\n\nexports.spring = spring;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AACjD,IAAIC,qBAAqB,GAAGD,OAAO,CAAC,mCAAmC,CAAC;AAExE,MAAME,MAAM,GAAGD,qBAAqB,CAACA,qBAAqB,CAACF,UAAU,CAACG,MAAM,CAAC;AAE7EC,OAAO,CAACD,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}