{"ast": null, "code": "import { velocityPerSecond } from '@motionone/utils';\nconst sampleT = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n  const prevT = Math.max(t - sampleT, 0);\n  return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\nexport { calcGeneratorVelocity };", "map": {"version": 3, "names": ["velocityPerSecond", "sampleT", "calcGeneratorVelocity", "resolveValue", "t", "current", "prevT", "Math", "max"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/generators/dist/utils/velocity.es.js"], "sourcesContent": ["import { velocityPerSecond } from '@motionone/utils';\n\nconst sampleT = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - sampleT, 0);\n    return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\n\nexport { calcGeneratorVelocity };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kBAAkB;AAEpD,MAAMC,OAAO,GAAG,CAAC,CAAC,CAAC;AACnB,SAASC,qBAAqBA,CAACC,YAAY,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACrD,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGH,OAAO,EAAE,CAAC,CAAC;EACtC,OAAOD,iBAAiB,CAACK,OAAO,GAAGF,YAAY,CAACG,KAAK,CAAC,EAAEF,CAAC,GAAGE,KAAK,CAAC;AACtE;AAEA,SAASJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}