{"ast": null, "code": "import { clamp } from '@motionone/utils';\nconst steps = (steps, direction = \"end\") => progress => {\n  progress = direction === \"end\" ? Math.min(progress, 0.999) : Math.max(progress, 0.001);\n  const expanded = progress * steps;\n  const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n  return clamp(0, 1, rounded / steps);\n};\nexport { steps };", "map": {"version": 3, "names": ["clamp", "steps", "direction", "progress", "Math", "min", "max", "expanded", "rounded", "floor", "ceil"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/easing/dist/steps.es.js"], "sourcesContent": ["import { clamp } from '@motionone/utils';\n\nconst steps = (steps, direction = \"end\") => (progress) => {\n    progress =\n        direction === \"end\"\n            ? Math.min(progress, 0.999)\n            : Math.max(progress, 0.001);\n    const expanded = progress * steps;\n    const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n    return clamp(0, 1, rounded / steps);\n};\n\nexport { steps };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,kBAAkB;AAExC,MAAMC,KAAK,GAAGA,CAACA,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAMC,QAAQ,IAAK;EACtDA,QAAQ,GACJD,SAAS,KAAK,KAAK,GACbE,IAAI,CAACC,GAAG,CAACF,QAAQ,EAAE,KAAK,CAAC,GACzBC,IAAI,CAACE,GAAG,CAACH,QAAQ,EAAE,KAAK,CAAC;EACnC,MAAMI,QAAQ,GAAGJ,QAAQ,GAAGF,KAAK;EACjC,MAAMO,OAAO,GAAGN,SAAS,KAAK,KAAK,GAAGE,IAAI,CAACK,KAAK,CAACF,QAAQ,CAAC,GAAGH,IAAI,CAACM,IAAI,CAACH,QAAQ,CAAC;EAChF,OAAOP,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEQ,OAAO,GAAGP,KAAK,CAAC;AACvC,CAAC;AAED,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}