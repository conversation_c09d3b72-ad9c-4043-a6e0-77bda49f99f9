# 🚀 Cable Operator CRM - Production Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the Cable Operator CRM system in a production environment using Docker and Docker Compose.

## 🔧 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB free space
- **CPU**: 2+ cores recommended

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl (for health checks)

### Installation Commands

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes to take effect
```

#### CentOS/RHEL
```bash
# Install Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 📦 Quick Deployment

### 1. Clone the Repository
```bash
git clone <repository-url>
cd cable-operator-crm
```

### 2. Make Deployment Script Executable
```bash
chmod +x scripts/deploy.sh
```

### 3. Run Automated Deployment
```bash
./scripts/deploy.sh
```

The script will:
- ✅ Check prerequisites
- ✅ Create necessary directories
- ✅ Setup environment files
- ✅ Generate SSL certificates
- ✅ Build and start all services
- ✅ Run database migrations
- ✅ Create default admin user
- ✅ Perform health checks

## 🔧 Manual Deployment

### 1. Environment Configuration

#### Backend Configuration
```bash
cp backend/.env.example backend/.env
```

Edit `backend/.env`:
```env
# Database
DATABASE_URL=*********************************************************/cable_operator_db

# Security
SECRET_KEY=your-super-secret-production-key-change-this
ENVIRONMENT=production
DEBUG=false

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Email (Gmail example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
```

#### Frontend Configuration
```bash
cp frontend/.env.example frontend/.env
```

Edit `frontend/.env`:
```env
REACT_APP_API_URL=https://yourdomain.com/api/v1
REACT_APP_ENVIRONMENT=production
REACT_APP_COMPANY_NAME=Your Company Name
```

### 2. SSL Certificates

#### Option A: Self-Signed (Development/Testing)
```bash
mkdir -p docker/ssl
openssl req -x509 -newkey rsa:4096 -keyout docker/ssl/key.pem -out docker/ssl/cert.pem -days 365 -nodes \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=yourdomain.com"
```

#### Option B: Let's Encrypt (Production)
```bash
# Install certbot
sudo apt install certbot

# Generate certificates
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem docker/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem docker/ssl/key.pem
sudo chown $USER:$USER docker/ssl/*.pem
```

### 3. Build and Start Services
```bash
# Build images
docker-compose build

# Start services
docker-compose up -d

# Check status
docker-compose ps
```

### 4. Database Setup
```bash
# Run migrations
docker-compose exec backend alembic upgrade head

# Create admin user (optional - done automatically)
docker-compose exec backend python scripts/create_admin.py
```

## 🌐 Domain Configuration

### DNS Settings
Point your domain to your server's IP address:
```
A    yourdomain.com        -> YOUR_SERVER_IP
A    www.yourdomain.com    -> YOUR_SERVER_IP
```

### Nginx Configuration
The system includes a reverse proxy configuration that handles:
- SSL termination
- HTTP to HTTPS redirect
- API routing
- Static file serving
- Security headers

## 🔒 Security Configuration

### 1. Change Default Passwords
```bash
# Database passwords in docker-compose.yml
# Admin user password (login and change via UI)
# Redis password in docker-compose.yml
```

### 2. Firewall Setup
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. Security Headers
The Nginx configuration includes security headers:
- X-Frame-Options
- X-XSS-Protection
- X-Content-Type-Options
- Content-Security-Policy

## 📊 Monitoring Setup

### Enable Monitoring Stack
```bash
# Start with monitoring
docker-compose --profile monitoring up -d

# Access monitoring
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001 (admin/admin123)
```

### Log Management
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Log rotation is handled automatically
```

## 🔄 Backup and Restore

### Automated Backup
```bash
# Create backup
./scripts/deploy.sh backup

# Backups are stored in ./backups/
```

### Manual Backup
```bash
# Database backup
docker-compose exec postgres pg_dump -U cable_user cable_operator_db > backup.sql

# File backup
tar -czf uploads_backup.tar.gz docker/uploads/
```

### Restore
```bash
# Restore database
docker-compose exec -T postgres psql -U cable_user cable_operator_db < backup.sql

# Restore files
tar -xzf uploads_backup.tar.gz
```

## 🔧 Maintenance

### Update System
```bash
# Pull latest changes
git pull origin main

# Redeploy
./scripts/deploy.sh --backup
```

### Scale Services
```bash
# Scale backend workers
docker-compose up -d --scale worker=3

# Scale specific services
docker-compose up -d --scale backend=2
```

### Health Checks
```bash
# Check service health
./scripts/deploy.sh health

# Manual health check
curl -f http://localhost:8000/health
```

## 🚨 Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check logs
docker-compose logs

# Check disk space
df -h

# Check memory
free -h
```

#### Database Connection Issues
```bash
# Check database status
docker-compose exec postgres pg_isready -U cable_user

# Reset database
docker-compose down
docker volume rm cable-operator-crm_postgres_data
docker-compose up -d
```

#### Permission Issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/deploy.sh
```

### Performance Optimization

#### Database Optimization
```sql
-- Connect to database
docker-compose exec postgres psql -U cable_user cable_operator_db

-- Check slow queries
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

-- Analyze tables
ANALYZE;
```

#### Application Optimization
```bash
# Monitor resource usage
docker stats

# Optimize images
docker system prune -a
```

## 📞 Support

### Default Credentials
- **Admin Email**: <EMAIL>
- **Admin Password**: admin123 (⚠️ Change immediately)

### Access URLs
- **Frontend**: https://yourdomain.com
- **Backend API**: https://yourdomain.com/api/v1
- **API Documentation**: https://yourdomain.com/api/v1/docs

### Useful Commands
```bash
# View system status
./scripts/deploy.sh status

# View logs
./scripts/deploy.sh logs

# Stop services
./scripts/deploy.sh stop

# Restart services
./scripts/deploy.sh restart
```

## 🎯 Production Checklist

- [ ] Change all default passwords
- [ ] Configure proper SSL certificates
- [ ] Set up domain and DNS
- [ ] Configure firewall
- [ ] Set up monitoring
- [ ] Configure automated backups
- [ ] Test disaster recovery
- [ ] Update environment variables
- [ ] Configure email settings
- [ ] Test all functionality
- [ ] Set up log rotation
- [ ] Configure resource limits

## 📈 Scaling for Production

### High Availability Setup
```bash
# Use external database
DATABASE_URL=***************************************/db

# Use external Redis
REDIS_URL=redis://external-redis:6379/0

# Load balancer configuration
# Use multiple backend instances
```

### Performance Tuning
```yaml
# docker-compose.override.yml
version: '3.8'
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
```

This deployment guide ensures a robust, secure, and scalable production deployment of the Cable Operator CRM system.
