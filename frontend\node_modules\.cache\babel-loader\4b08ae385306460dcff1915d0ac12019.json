{"ast": null, "code": "const makeRenderlessComponent = hook => props => {\n  hook(props);\n  return null;\n};\nexport { makeRenderlessComponent };", "map": {"version": 3, "names": ["makeRenderlessComponent", "hook", "props"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/utils/make-renderless-component.mjs"], "sourcesContent": ["const makeRenderlessComponent = (hook) => (props) => {\n    hook(props);\n    return null;\n};\n\nexport { makeRenderlessComponent };\n"], "mappings": "AAAA,MAAMA,uBAAuB,GAAIC,IAAI,IAAMC,KAAK,IAAK;EACjDD,IAAI,CAACC,KAAK,CAAC;EACX,OAAO,IAAI;AACf,CAAC;AAED,SAASF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}