{"ast": null, "code": "import { sync } from '../../frameloop/index.mjs';\nimport { animate } from '../legacy-popmotion/index.mjs';\nimport { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nfunction createAcceleratedAnimation(value, valueName, {\n  onUpdate,\n  onComplete,\n  ...options\n}) {\n  let {\n    keyframes,\n    duration = 0.3,\n    elapsed = 0,\n    ease\n  } = options;\n  /**\n   * If this animation needs pre-generated keyframes then generate.\n   */\n  if (options.type === \"spring\" || !isWaapiSupportedEasing(options.ease)) {\n    const sampleAnimation = animate(options);\n    let state = {\n      done: false,\n      value: keyframes[0]\n    };\n    const pregeneratedKeyframes = [];\n    let t = 0;\n    while (!state.done) {\n      state = sampleAnimation.sample(t);\n      pregeneratedKeyframes.push(state.value);\n      t += sampleDelta;\n    }\n    keyframes = pregeneratedKeyframes;\n    duration = t - sampleDelta;\n    ease = \"linear\";\n  }\n  const animation = animateStyle(value.owner.current, valueName, keyframes, {\n    ...options,\n    delay: -elapsed,\n    duration,\n    /**\n     * This function is currently not called if ease is provided\n     * as a function so the cast is safe.\n     *\n     * However it would be possible for a future refinement to port\n     * in easing pregeneration from Motion One for browsers that\n     * support the upcoming `linear()` easing function.\n     */\n    ease: ease\n  });\n  /**\n   * Prefer the `onfinish` prop as it's more widely supported than\n   * the `finished` promise.\n   *\n   * Here, we synchronously set the provided MotionValue to the end\n   * keyframe. If we didn't, when the WAAPI animation is finished it would\n   * be removed from the element which would then revert to its old styles.\n   */\n  animation.onfinish = () => {\n    value.set(keyframes[keyframes.length - 1]);\n    onComplete && onComplete();\n  };\n  /**\n   * Animation interrupt callback.\n   */\n  return () => {\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * Rather than read commited styles back out of the DOM, we can\n     * create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to calculate velocity for any subsequent animation.\n     */\n    const {\n      currentTime\n    } = animation;\n    if (currentTime) {\n      const sampleAnimation = animate(options);\n      value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n    }\n    sync.update(() => animation.cancel());\n  };\n}\nexport { createAcceleratedAnimation };", "map": {"version": 3, "names": ["sync", "animate", "animateStyle", "isWaapiSupportedEasing", "sampleDelta", "createAcceleratedAnimation", "value", "valueName", "onUpdate", "onComplete", "options", "keyframes", "duration", "elapsed", "ease", "type", "sampleAnimation", "state", "done", "pregeneratedKeyframes", "t", "sample", "push", "animation", "owner", "current", "delay", "onfinish", "set", "length", "currentTime", "setWithVelocity", "update", "cancel"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/animation/waapi/create-accelerated-animation.mjs"], "sourcesContent": ["import { sync } from '../../frameloop/index.mjs';\nimport { animate } from '../legacy-popmotion/index.mjs';\nimport { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nfunction createAcceleratedAnimation(value, valueName, { onUpdate, onComplete, ...options }) {\n    let { keyframes, duration = 0.3, elapsed = 0, ease } = options;\n    /**\n     * If this animation needs pre-generated keyframes then generate.\n     */\n    if (options.type === \"spring\" || !isWaapiSupportedEasing(options.ease)) {\n        const sampleAnimation = animate(options);\n        let state = { done: false, value: keyframes[0] };\n        const pregeneratedKeyframes = [];\n        let t = 0;\n        while (!state.done) {\n            state = sampleAnimation.sample(t);\n            pregeneratedKeyframes.push(state.value);\n            t += sampleDelta;\n        }\n        keyframes = pregeneratedKeyframes;\n        duration = t - sampleDelta;\n        ease = \"linear\";\n    }\n    const animation = animateStyle(value.owner.current, valueName, keyframes, {\n        ...options,\n        delay: -elapsed,\n        duration,\n        /**\n         * This function is currently not called if ease is provided\n         * as a function so the cast is safe.\n         *\n         * However it would be possible for a future refinement to port\n         * in easing pregeneration from Motion One for browsers that\n         * support the upcoming `linear()` easing function.\n         */\n        ease: ease,\n    });\n    /**\n     * Prefer the `onfinish` prop as it's more widely supported than\n     * the `finished` promise.\n     *\n     * Here, we synchronously set the provided MotionValue to the end\n     * keyframe. If we didn't, when the WAAPI animation is finished it would\n     * be removed from the element which would then revert to its old styles.\n     */\n    animation.onfinish = () => {\n        value.set(keyframes[keyframes.length - 1]);\n        onComplete && onComplete();\n    };\n    /**\n     * Animation interrupt callback.\n     */\n    return () => {\n        /**\n         * WAAPI doesn't natively have any interruption capabilities.\n         *\n         * Rather than read commited styles back out of the DOM, we can\n         * create a renderless JS animation and sample it twice to calculate\n         * its current value, \"previous\" value, and therefore allow\n         * Motion to calculate velocity for any subsequent animation.\n         */\n        const { currentTime } = animation;\n        if (currentTime) {\n            const sampleAnimation = animate(options);\n            value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n        }\n        sync.update(() => animation.cancel());\n    };\n}\n\nexport { createAcceleratedAnimation };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,2BAA2B;AAChD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,sBAAsB,QAAQ,cAAc;;AAErD;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;AACxB,SAASC,0BAA0BA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAAEC,QAAQ;EAAEC,UAAU;EAAE,GAAGC;AAAQ,CAAC,EAAE;EACxF,IAAI;IAAEC,SAAS;IAAEC,QAAQ,GAAG,GAAG;IAAEC,OAAO,GAAG,CAAC;IAAEC;EAAK,CAAC,GAAGJ,OAAO;EAC9D;AACJ;AACA;EACI,IAAIA,OAAO,CAACK,IAAI,KAAK,QAAQ,IAAI,CAACZ,sBAAsB,CAACO,OAAO,CAACI,IAAI,CAAC,EAAE;IACpE,MAAME,eAAe,GAAGf,OAAO,CAACS,OAAO,CAAC;IACxC,IAAIO,KAAK,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEZ,KAAK,EAAEK,SAAS,CAAC,CAAC;IAAE,CAAC;IAChD,MAAMQ,qBAAqB,GAAG,EAAE;IAChC,IAAIC,CAAC,GAAG,CAAC;IACT,OAAO,CAACH,KAAK,CAACC,IAAI,EAAE;MAChBD,KAAK,GAAGD,eAAe,CAACK,MAAM,CAACD,CAAC,CAAC;MACjCD,qBAAqB,CAACG,IAAI,CAACL,KAAK,CAACX,KAAK,CAAC;MACvCc,CAAC,IAAIhB,WAAW;IACpB;IACAO,SAAS,GAAGQ,qBAAqB;IACjCP,QAAQ,GAAGQ,CAAC,GAAGhB,WAAW;IAC1BU,IAAI,GAAG,QAAQ;EACnB;EACA,MAAMS,SAAS,GAAGrB,YAAY,CAACI,KAAK,CAACkB,KAAK,CAACC,OAAO,EAAElB,SAAS,EAAEI,SAAS,EAAE;IACtE,GAAGD,OAAO;IACVgB,KAAK,EAAE,CAACb,OAAO;IACfD,QAAQ;IACR;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQE,IAAI,EAAEA;EACV,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,SAAS,CAACI,QAAQ,GAAG,MAAM;IACvBrB,KAAK,CAACsB,GAAG,CAACjB,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1CpB,UAAU,IAAIA,UAAU,CAAC,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;EACI,OAAO,MAAM;IACT;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAEqB;IAAY,CAAC,GAAGP,SAAS;IACjC,IAAIO,WAAW,EAAE;MACb,MAAMd,eAAe,GAAGf,OAAO,CAACS,OAAO,CAAC;MACxCJ,KAAK,CAACyB,eAAe,CAACf,eAAe,CAACK,MAAM,CAACS,WAAW,GAAG1B,WAAW,CAAC,CAACE,KAAK,EAAEU,eAAe,CAACK,MAAM,CAACS,WAAW,CAAC,CAACxB,KAAK,EAAEF,WAAW,CAAC;IAC1I;IACAJ,IAAI,CAACgC,MAAM,CAAC,MAAMT,SAAS,CAACU,MAAM,CAAC,CAAC,CAAC;EACzC,CAAC;AACL;AAEA,SAAS5B,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}