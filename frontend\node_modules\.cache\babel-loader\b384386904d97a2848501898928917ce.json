{"ast": null, "code": "import React__default from 'react';\nclass VisualElementHandler extends React__default.Component {\n  /**\n   * Update visual element props as soon as we know this update is going to be commited.\n   */\n  getSnapshotBeforeUpdate() {\n    const {\n      visualElement,\n      props\n    } = this.props;\n    if (visualElement) visualElement.setProps(props);\n    return null;\n  }\n  componentDidUpdate() {}\n  render() {\n    return this.props.children;\n  }\n}\nexport { VisualElementHandler };", "map": {"version": 3, "names": ["React__default", "VisualElementHandler", "Component", "getSnapshotBeforeUpdate", "visualElement", "props", "setProps", "componentDidUpdate", "render", "children"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/motion/utils/VisualElementHandler.mjs"], "sourcesContent": ["import React__default from 'react';\n\nclass VisualElementHandler extends React__default.Component {\n    /**\n     * Update visual element props as soon as we know this update is going to be commited.\n     */\n    getSnapshotBeforeUpdate() {\n        const { visualElement, props } = this.props;\n        if (visualElement)\n            visualElement.setProps(props);\n        return null;\n    }\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\n\nexport { VisualElementHandler };\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,OAAO;AAElC,MAAMC,oBAAoB,SAASD,cAAc,CAACE,SAAS,CAAC;EACxD;AACJ;AACA;EACIC,uBAAuBA,CAAA,EAAG;IACtB,MAAM;MAAEC,aAAa;MAAEC;IAAM,CAAC,GAAG,IAAI,CAACA,KAAK;IAC3C,IAAID,aAAa,EACbA,aAAa,CAACE,QAAQ,CAACD,KAAK,CAAC;IACjC,OAAO,IAAI;EACf;EACAE,kBAAkBA,CAAA,EAAG,CAAE;EACvBC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;EAC9B;AACJ;AAEA,SAASR,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}