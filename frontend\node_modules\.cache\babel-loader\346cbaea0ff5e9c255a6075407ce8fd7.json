{"ast": null, "code": "import { useEffect } from 'react';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * A hook that allows an element to be dragged.\n *\n * @internal\n */\nfunction useDrag(props) {\n  const {\n    dragControls: groupDragControls,\n    visualElement\n  } = props;\n  const dragControls = useConstant(() => new VisualElementDragControls(visualElement));\n  // If we've been provided a DragControls for manual control over the drag gesture,\n  // subscribe this component to it on mount.\n  useEffect(() => groupDragControls && groupDragControls.subscribe(dragControls), [dragControls, groupDragControls]);\n  // Apply the event listeners to the element\n  useEffect(() => dragControls.addListeners(), [dragControls]);\n}\nexport { useDrag };", "map": {"version": 3, "names": ["useEffect", "VisualElementDragControls", "useConstant", "useDrag", "props", "dragControls", "groupDragControls", "visualElement", "subscribe", "addListeners"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/gestures/drag/use-drag.mjs"], "sourcesContent": ["import { useEffect } from 'react';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * A hook that allows an element to be dragged.\n *\n * @internal\n */\nfunction useDrag(props) {\n    const { dragControls: groupDragControls, visualElement } = props;\n    const dragControls = useConstant(() => new VisualElementDragControls(visualElement));\n    // If we've been provided a DragControls for manual control over the drag gesture,\n    // subscribe this component to it on mount.\n    useEffect(() => groupDragControls && groupDragControls.subscribe(dragControls), [dragControls, groupDragControls]);\n    // Apply the event listeners to the element\n    useEffect(() => dragControls.addListeners(), [dragControls]);\n}\n\nexport { useDrag };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,WAAW,QAAQ,8BAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACpB,MAAM;IAAEC,YAAY,EAAEC,iBAAiB;IAAEC;EAAc,CAAC,GAAGH,KAAK;EAChE,MAAMC,YAAY,GAAGH,WAAW,CAAC,MAAM,IAAID,yBAAyB,CAACM,aAAa,CAAC,CAAC;EACpF;EACA;EACAP,SAAS,CAAC,MAAMM,iBAAiB,IAAIA,iBAAiB,CAACE,SAAS,CAACH,YAAY,CAAC,EAAE,CAACA,YAAY,EAAEC,iBAAiB,CAAC,CAAC;EAClH;EACAN,SAAS,CAAC,MAAMK,YAAY,CAACI,YAAY,CAAC,CAAC,EAAE,CAACJ,YAAY,CAAC,CAAC;AAChE;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}