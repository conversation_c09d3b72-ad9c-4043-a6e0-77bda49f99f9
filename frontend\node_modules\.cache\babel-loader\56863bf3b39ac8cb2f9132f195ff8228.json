{"ast": null, "code": "'use strict';\n\n/**\n * A `Cancel` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction Cancel(message) {\n  this.message = message;\n}\nCancel.prototype.toString = function toString() {\n  return 'Cancel' + (this.message ? ': ' + this.message : '');\n};\nCancel.prototype.__CANCEL__ = true;\nmodule.exports = Cancel;", "map": {"version": 3, "names": ["Cancel", "message", "prototype", "toString", "__CANCEL__", "module", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/axios/lib/cancel/Cancel.js"], "sourcesContent": ["'use strict';\n\n/**\n * A `Cancel` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction Cancel(message) {\n  this.message = message;\n}\n\nCancel.prototype.toString = function toString() {\n  return 'Cancel' + (this.message ? ': ' + this.message : '');\n};\n\nCancel.prototype.__CANCEL__ = true;\n\nmodule.exports = Cancel;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,OAAO,EAAE;EACvB,IAAI,CAACA,OAAO,GAAGA,OAAO;AACxB;AAEAD,MAAM,CAACE,SAAS,CAACC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EAC9C,OAAO,QAAQ,IAAI,IAAI,CAACF,OAAO,GAAG,IAAI,GAAG,IAAI,CAACA,OAAO,GAAG,EAAE,CAAC;AAC7D,CAAC;AAEDD,MAAM,CAACE,SAAS,CAACE,UAAU,GAAG,IAAI;AAElCC,MAAM,CAACC,OAAO,GAAGN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}