{"ast": null, "code": "'use strict';\n\nvar utils = require('@motionone/utils');\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n  current: 0,\n  offset: [],\n  progress: 0,\n  scrollLength: 0,\n  targetOffset: 0,\n  targetLength: 0,\n  containerLength: 0,\n  velocity: 0\n});\nconst createScrollInfo = () => ({\n  time: 0,\n  x: createAxisInfo(),\n  y: createAxisInfo()\n});\nconst keys = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n  const axis = info[axisName];\n  const {\n    length,\n    position\n  } = keys[axisName];\n  const prev = axis.current;\n  const prevTime = info.time;\n  axis.current = element[`scroll${position}`];\n  axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n  axis.offset.length = 0;\n  axis.offset[0] = 0;\n  axis.offset[1] = axis.scrollLength;\n  axis.progress = utils.progress(0, axis.scrollLength, axis.current);\n  const elapsed = time - prevTime;\n  axis.velocity = elapsed > maxElapsed ? 0 : utils.velocityPerSecond(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n  updateAxisInfo(element, \"x\", info, time);\n  updateAxisInfo(element, \"y\", info, time);\n  info.time = time;\n}\nexports.createScrollInfo = createScrollInfo;\nexports.updateScrollInfo = updateScrollInfo;", "map": {"version": 3, "names": ["utils", "require", "maxElapsed", "createAxisInfo", "current", "offset", "progress", "<PERSON><PERSON><PERSON><PERSON>", "targetOffset", "targetLength", "containerLength", "velocity", "createScrollInfo", "time", "x", "y", "keys", "length", "position", "updateAxisInfo", "element", "axisName", "info", "axis", "prev", "prevTime", "elapsed", "velocityPerSecond", "updateScrollInfo", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/gestures/scroll/info.cjs.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('@motionone/utils');\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = utils.progress(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed ? 0 : utils.velocityPerSecond(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\nexports.createScrollInfo = createScrollInfo;\nexports.updateScrollInfo = updateScrollInfo;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;;AAEvC;AACA;AACA;AACA,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,cAAc,GAAGA,CAAA,MAAO;EAC1BC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,EAAE;EACVC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,eAAe,EAAE,CAAC;EAClBC,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAGA,CAAA,MAAO;EAC5BC,IAAI,EAAE,CAAC;EACPC,CAAC,EAAEX,cAAc,CAAC,CAAC;EACnBY,CAAC,EAAEZ,cAAc,CAAC;AACtB,CAAC,CAAC;AACF,MAAMa,IAAI,GAAG;EACTF,CAAC,EAAE;IACCG,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACd,CAAC;EACDH,CAAC,EAAE;IACCE,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACd;AACJ,CAAC;AACD,SAASC,cAAcA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAET,IAAI,EAAE;EACnD,MAAMU,IAAI,GAAGD,IAAI,CAACD,QAAQ,CAAC;EAC3B,MAAM;IAAEJ,MAAM;IAAEC;EAAS,CAAC,GAAGF,IAAI,CAACK,QAAQ,CAAC;EAC3C,MAAMG,IAAI,GAAGD,IAAI,CAACnB,OAAO;EACzB,MAAMqB,QAAQ,GAAGH,IAAI,CAACT,IAAI;EAC1BU,IAAI,CAACnB,OAAO,GAAGgB,OAAO,CAAC,SAASF,QAAQ,EAAE,CAAC;EAC3CK,IAAI,CAAChB,YAAY,GAAGa,OAAO,CAAC,SAASH,MAAM,EAAE,CAAC,GAAGG,OAAO,CAAC,SAASH,MAAM,EAAE,CAAC;EAC3EM,IAAI,CAAClB,MAAM,CAACY,MAAM,GAAG,CAAC;EACtBM,IAAI,CAAClB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;EAClBkB,IAAI,CAAClB,MAAM,CAAC,CAAC,CAAC,GAAGkB,IAAI,CAAChB,YAAY;EAClCgB,IAAI,CAACjB,QAAQ,GAAGN,KAAK,CAACM,QAAQ,CAAC,CAAC,EAAEiB,IAAI,CAAChB,YAAY,EAAEgB,IAAI,CAACnB,OAAO,CAAC;EAClE,MAAMsB,OAAO,GAAGb,IAAI,GAAGY,QAAQ;EAC/BF,IAAI,CAACZ,QAAQ,GACTe,OAAO,GAAGxB,UAAU,GAAG,CAAC,GAAGF,KAAK,CAAC2B,iBAAiB,CAACJ,IAAI,CAACnB,OAAO,GAAGoB,IAAI,EAAEE,OAAO,CAAC;AACxF;AACA,SAASE,gBAAgBA,CAACR,OAAO,EAAEE,IAAI,EAAET,IAAI,EAAE;EAC3CM,cAAc,CAACC,OAAO,EAAE,GAAG,EAAEE,IAAI,EAAET,IAAI,CAAC;EACxCM,cAAc,CAACC,OAAO,EAAE,GAAG,EAAEE,IAAI,EAAET,IAAI,CAAC;EACxCS,IAAI,CAACT,IAAI,GAAGA,IAAI;AACpB;AAEAgB,OAAO,CAACjB,gBAAgB,GAAGA,gBAAgB;AAC3CiB,OAAO,CAACD,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}