{"ast": null, "code": "import { cancelSync, flushSync, sync } from '../../frameloop/index.mjs';\nimport { animate } from '../../animation/animate.mjs';\nimport { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEquals, isDelta<PERSON>ero, aspectRatio } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction createProjectionNode({\n  attachResizeListener,\n  defaultParent,\n  measureScroll,\n  checkIsScrollRoot,\n  resetTransform\n}) {\n  return class ProjectionNode {\n    constructor(elementId, latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      this.isTransformDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this or any\n       * child might need recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      // Note: Currently only running on root node\n      this.potentialNodes = new Map();\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n      };\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.elementId = elementId;\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      elementId && this.root.registerPotentialNode(elementId, this);\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name, ...args) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      subscriptionManager === null || subscriptionManager === void 0 ? void 0 : subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    registerPotentialNode(elementId, node) {\n      this.potentialNodes.set(elementId, node);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance, isLayoutDirty = false) {\n      var _a;\n      if (this.instance) return;\n      this.isSVG = instance instanceof SVGElement && instance.tagName !== \"svg\";\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      (_a = this.parent) === null || _a === void 0 ? void 0 : _a.children.add(this);\n      this.elementId && this.root.potentialNodes.delete(this.elementId);\n      if (isLayoutDirty && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", ({\n          delta,\n          hasLayoutChanged,\n          hasRelativeTargetChanged,\n          layout: newLayout\n        }) => {\n          var _a, _b, _c, _d, _e;\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = (_b = (_a = this.options.transition) !== null && _a !== void 0 ? _a : visualElement.getDefaultTransition()) !== null && _b !== void 0 ? _b : defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const targetChanged = !this.targetLayout || !boxEquals(this.targetLayout, newLayout) || hasRelativeTargetChanged;\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n          if (((_c = this.resumeFrom) === null || _c === void 0 ? void 0 : _c.instance) || hasOnlyRelativeTargetChanged || hasLayoutChanged && (targetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n            const animationOptions = {\n              ...getValueTransition(layoutTransition, \"layout\"),\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            };\n            if (visualElement.shouldReduceMotion) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged && this.animationProgress === 0) {\n              finishAnimation(this);\n            }\n            this.isLead() && ((_e = (_d = this.options).onExitComplete) === null || _e === void 0 ? void 0 : _e.call(_d));\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      var _a, _b;\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.remove(this);\n      (_b = this.parent) === null || _b === void 0 ? void 0 : _b.children.delete(this);\n      this.instance = undefined;\n      cancelSync.preRender(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      var _a;\n      return this.isAnimationBlocked || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimationBlocked()) || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      var _a;\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.forEach(resetRotation);\n      this.animationId++;\n    }\n    willUpdate(shouldNotifyListeners = true) {\n      var _a, _b, _c;\n      if (this.root.isUpdateBlocked()) {\n        (_b = (_a = this.options).onExitComplete) === null || _b === void 0 ? void 0 : _b.call(_a);\n        return;\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = (_c = this.options.visualElement) === null || _c === void 0 ? void 0 : _c.getProps().transformTemplate;\n      this.prevTransformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    // Note: Currently only running on root node\n    didUpdate() {\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) return;\n      this.isUpdating = false;\n      /**\n       * Search for and mount newly-added projection elements.\n       *\n       * TODO: Every time a new component is rendered we could search up the tree for\n       * the closest mounted node and query from there rather than document.\n       */\n      if (this.potentialNodes.size) {\n        this.potentialNodes.forEach(mountNodeEarly);\n        this.potentialNodes.clear();\n      }\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      // Flush any scheduled updates\n      flushSync.update();\n      flushSync.preRender();\n      flushSync.render();\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      sync.preRender(this.updateProjection, false, true);\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      sync.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n    }\n    updateLayout() {\n      var _a;\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout === null || prevLayout === void 0 ? void 0 : prevLayout.layoutBox);\n    }\n    updateScroll(phase = \"measure\") {\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement) {\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot: checkIsScrollRoot(this.instance),\n          offset: measureScroll(this.instance)\n        };\n      }\n    }\n    resetTransform() {\n      var _a;\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n      const transformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure(removeTransform = true) {\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      // Remove viewport scroll to give page-relative coordinates\n      const {\n        scroll\n      } = this.root;\n      if (scroll) {\n        translateAxis(box.x, scroll.offset.x);\n        translateAxis(box.y, scroll.offset.y);\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.isRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n            const {\n              scroll: rootScroll\n            } = this.root;\n            /**\n             * Undo the application of page scroll that was originally added\n             * to the measured bounding box.\n             */\n            if (rootScroll) {\n              translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n              translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n            }\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box, transformOnly = false) {\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      var _a;\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, (_a = node.snapshot) === null || _a === void 0 ? void 0 : _a.layoutBox, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    /**\n     *\n     */\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.isProjectionDirty = true;\n      this.root.scheduleUpdateProjection();\n    }\n    setOptions(options) {\n      this.options = {\n        ...this.options,\n        ...options,\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      };\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    /**\n     * Frame calculations\n     */\n    resolveTargetDelta() {\n      var _a;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      if (!this.isProjectionDirty && !this.attemptToResolveRelativeTarget) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      // TODO If this is unsuccessful this currently happens every frame\n      if (!this.targetDelta && !this.relativeTarget) {\n        // TODO: This is a semi-repetition of further down this function, make DRY\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout) {\n          this.relativeParent = relativeParent;\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.target)) {\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target) {\n          this.relativeParent = relativeParent;\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) return undefined;\n      if ((this.parent.relativeTarget || this.parent.targetDelta) && this.parent.layout) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    calcProjection() {\n      var _a;\n      const {\n        isProjectionDirty,\n        isTransformDirty\n      } = this;\n      this.isProjectionDirty = this.isTransformDirty = false;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      if (isProjectionDirty) canSkip = false;\n      if (isShared && isTransformDirty) canSkip = false;\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimating) || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      const {\n        target\n      } = lead;\n      if (!target) return;\n      if (!this.projectionDelta) {\n        this.projectionDelta = createDelta();\n        this.projectionDeltaWithTransform = createDelta();\n      }\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      const prevProjectionTransform = this.projectionTransform;\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n      if (this.projectionTransform !== prevProjectionTransform || this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender(notifyAll = true) {\n      var _a, _b, _c;\n      (_b = (_a = this.options).scheduleRender) === null || _b === void 0 ? void 0 : _b.call(_a);\n      notifyAll && ((_c = this.getStack()) === null || _c === void 0 ? void 0 : _c.scheduleRender());\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n      var _a, _b;\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.latestValues) || {};\n      const mixedValues = {\n        ...this.latestValues\n      };\n      const targetDelta = createDelta();\n      this.relativeTarget = this.relativeTargetOrigin = undefined;\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const isSharedLayoutAnimation = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.source) !== ((_a = this.layout) === null || _a === void 0 ? void 0 : _a.source);\n      const isOnlyMember = (((_b = this.getStack()) === null || _b === void 0 ? void 0 : _b.members.length) || 0) <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      this.mixTargetDelta = latest => {\n        var _a;\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.layout)) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(0);\n    }\n    startAnimation(options) {\n      var _a, _b;\n      this.notifyListeners(\"animationStart\");\n      (_a = this.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      if (this.resumingFrom) {\n        (_b = this.resumingFrom.currentAnimation) === null || _b === void 0 ? void 0 : _b.stop();\n      }\n      if (this.pendingAnimation) {\n        cancelSync.update(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = sync.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        this.currentAnimation = animate(0, animationTarget, {\n          ...options,\n          onUpdate: latest => {\n            var _a;\n            this.mixTargetDelta(latest);\n            (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, latest);\n          },\n          onComplete: () => {\n            var _a;\n            (_a = options.onComplete) === null || _a === void 0 ? void 0 : _a.call(options);\n            this.completeAnimation();\n          }\n        });\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      var _a;\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      var _a;\n      if (this.currentAnimation) {\n        (_a = this.mixTargetDelta) === null || _a === void 0 ? void 0 : _a.call(this, animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      var _a, _b, _c;\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      node.promote({\n        transition: (_a = node.options.initialPromotionConfig) === null || _a === void 0 ? void 0 : _a.transition,\n        preserveFollowOpacity: (_c = (_b = node.options.initialPromotionConfig) === null || _b === void 0 ? void 0 : _b.shouldPreserveFollowOpacity) === null || _c === void 0 ? void 0 : _c.call(_b, node)\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n    }\n    getPrevLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote({\n      needsReset,\n      transition,\n      preserveFollowOpacity\n    } = {}) {\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected rotation values, we can early return without a forced render.\n      let hasRotate = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ) {\n        hasRotate = true;\n      }\n      // If there's no rotation values, we don't need to do any more.\n      if (!hasRotate) return;\n      const resetValues = {};\n      // Check the rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        const key = \"rotate\" + transformAxes[i];\n        // Record the rotation and then temporarily set it to 0\n        if (latestValues[key]) {\n          resetValues[key] = latestValues[key];\n          visualElement.setStaticValue(key, 0);\n        }\n      }\n      // Force a render of this element to apply the transform with all rotations\n      // set to 0.\n      visualElement === null || visualElement === void 0 ? void 0 : visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp = {}) {\n      var _a, _b, _c;\n      // TODO: Return lifecycle-persistent object\n      const styles = {};\n      if (!this.instance || this.isSVG) return styles;\n      if (!this.isVisible) {\n        return {\n          visibility: \"hidden\"\n        };\n      } else {\n        styles.visibility = \"\";\n      }\n      const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_c = (_b = valuesToRender.opacity) !== null && _b !== void 0 ? _b : this.latestValues.opacity) !== null && _c !== void 0 ? _c : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo\n        } = scaleCorrectors[key];\n        const corrected = correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          styles[key] = corrected;\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _a;\n        return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _a, _b, _c;\n  const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeTargetChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEquals(relativeSnapshot, relativeLayout)) {\n            hasRelativeTargetChanged = true;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeTargetChanged\n    });\n  } else if (node.isLead()) {\n    (_c = (_b = node.options).onExitComplete) === null || _c === void 0 ? void 0 : _c.call(_b);\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Propagate isProjectionDirty. Nodes are ordered by depth, so if the parent here\n   * is dirty we can simply pass this forward.\n   */\n  node.isProjectionDirty || (node.isProjectionDirty = Boolean(node.parent && node.parent.isProjectionDirty));\n  /**\n   * Propagate isTransformDirty.\n   */\n  node.isTransformDirty || (node.isTransformDirty = Boolean(node.parent && node.parent.isTransformDirty));\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement === null || visualElement === void 0 ? void 0 : visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetRotation(node) {\n  node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mix(delta.translate, 0, p);\n  output.scale = mix(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mix(from.min, to.min, p);\n  output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nfunction mountNodeEarly(node, elementId) {\n  /**\n   * Rather than searching the DOM from document we can search the\n   * path for the deepest mounted ancestor and search from there\n   */\n  let searchNode = node.root;\n  for (let i = node.path.length - 1; i >= 0; i--) {\n    if (Boolean(node.path[i].instance)) {\n      searchNode = node.path[i];\n      break;\n    }\n  }\n  const searchElement = searchNode && searchNode !== node.root ? searchNode.instance : document;\n  const element = searchElement.querySelector(`[data-projection-id=\"${elementId}\"]`);\n  if (element) node.mount(element, true);\n}\nfunction roundAxis(axis) {\n  axis.min = Math.round(axis.min);\n  axis.max = Math.round(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nexport { createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["cancelSync", "flushSync", "sync", "animate", "SubscriptionManager", "mixValues", "copyBoxInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "calcLength", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "getValueTransition", "boxEquals", "isDeltaZero", "aspectRatio", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "FlatTree", "resolveMotionValue", "globalProjectionState", "delay", "mix", "transformAxes", "animationTarget", "id", "createProjectionNode", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "elementId", "latestValues", "parent", "animationId", "children", "Set", "options", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isTransformDirty", "isProjectionDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "potentialNodes", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "nodes", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "root", "path", "depth", "registerPotentialNode", "i", "length", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "args", "subscriptionManager", "notify", "hasListeners", "node", "mount", "instance", "_a", "SVGElement", "tagName", "layoutId", "layout", "visualElement", "current", "delete", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "delta", "hasLayoutChanged", "hasRelativeTargetChanged", "newLayout", "_b", "_c", "_d", "_e", "isTreeAnimationBlocked", "target", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "targetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "resumeFrom", "currentAnimation", "resumingFrom", "setAnimationOrigin", "animationOptions", "onPlay", "onComplete", "shouldReduceMotion", "type", "startAnimation", "isLead", "onExitComplete", "call", "unmount", "willUpdate", "remove", "getStack", "preRender", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetRotation", "shouldNotifyListeners", "updateScroll", "transformTemplate", "prevTransformTemplateValue", "updateSnapshot", "didUpdate", "updateWasBlocked", "clearMeasurements", "size", "mount<PERSON>ode<PERSON>arly", "clear", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "update", "render", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "measuredBox", "source", "box", "measureViewportBox", "boxWithoutScroll", "rootScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "lead", "getLead", "attemptToResolveRelativeTarget", "relativeParent", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isShared", "canSkip", "pendingAnimation", "projectionDeltaWithTransform", "prevTreeScaleX", "prevTreeScaleY", "prevProjectionTransform", "projectionTransform", "hide", "show", "notifyAll", "snapshotLatestValues", "mixedValues", "relativeLayout", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "some", "hasOpacityCrossfade", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "stop", "onUpdate", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "stack", "promote", "initialPromotionConfig", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "getPrevLead", "prevLead", "relegate", "hasRotate", "rotate", "rotateX", "rotateY", "rotateZ", "resetValues", "key", "setStaticValue", "getProjectionStyles", "styleProp", "styles", "visibility", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "origin", "opacityExit", "correct", "applyTo", "corrected", "num", "resetTree", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "searchNode", "searchElement", "document", "element", "querySelector", "roundAxis", "Math", "round"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { cancelSync, flushSync, sync } from '../../frameloop/index.mjs';\nimport { animate } from '../../animation/animate.mjs';\nimport { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEquals, isDelta<PERSON>ero, aspectRatio } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(elementId, latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            this.isTransformDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this or any\n             * child might need recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            // Note: Currently only running on root node\n            this.potentialNodes = new Map();\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n            };\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.elementId = elementId;\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            elementId && this.root.registerPotentialNode(elementId, this);\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager === null || subscriptionManager === void 0 ? void 0 : subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        registerPotentialNode(elementId, node) {\n            this.potentialNodes.set(elementId, node);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance, isLayoutDirty = false) {\n            var _a;\n            if (this.instance)\n                return;\n            this.isSVG =\n                instance instanceof SVGElement && instance.tagName !== \"svg\";\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            (_a = this.parent) === null || _a === void 0 ? void 0 : _a.children.add(this);\n            this.elementId && this.root.potentialNodes.delete(this.elementId);\n            if (isLayoutDirty && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeTargetChanged, layout: newLayout, }) => {\n                    var _a, _b, _c, _d, _e;\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = (_b = (_a = this.options.transition) !== null && _a !== void 0 ? _a : visualElement.getDefaultTransition()) !== null && _b !== void 0 ? _b : defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const targetChanged = !this.targetLayout ||\n                        !boxEquals(this.targetLayout, newLayout) ||\n                        hasRelativeTargetChanged;\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n                    if (((_c = this.resumeFrom) === null || _c === void 0 ? void 0 : _c.instance) ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (targetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged &&\n                            this.animationProgress === 0) {\n                            finishAnimation(this);\n                        }\n                        this.isLead() && ((_e = (_d = this.options).onExitComplete) === null || _e === void 0 ? void 0 : _e.call(_d));\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            var _a, _b;\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.remove(this);\n            (_b = this.parent) === null || _b === void 0 ? void 0 : _b.children.delete(this);\n            this.instance = undefined;\n            cancelSync.preRender(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            var _a;\n            return (this.isAnimationBlocked ||\n                ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            var _a;\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.forEach(resetRotation);\n            this.animationId++;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            var _a, _b, _c;\n            if (this.root.isUpdateBlocked()) {\n                (_b = (_a = this.options).onExitComplete) === null || _b === void 0 ? void 0 : _b.call(_a);\n                return;\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = (_c = this.options.visualElement) === null || _c === void 0 ? void 0 : _c.getProps().transformTemplate;\n            this.prevTransformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        // Note: Currently only running on root node\n        didUpdate() {\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating)\n                return;\n            this.isUpdating = false;\n            /**\n             * Search for and mount newly-added projection elements.\n             *\n             * TODO: Every time a new component is rendered we could search up the tree for\n             * the closest mounted node and query from there rather than document.\n             */\n            if (this.potentialNodes.size) {\n                this.potentialNodes.forEach(mountNodeEarly);\n                this.potentialNodes.clear();\n            }\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            // Flush any scheduled updates\n            flushSync.update();\n            flushSync.preRender();\n            flushSync.render();\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            sync.preRender(this.updateProjection, false, true);\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            sync.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n        }\n        updateLayout() {\n            var _a;\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout === null || prevLayout === void 0 ? void 0 : prevLayout.layoutBox);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement) {\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot: checkIsScrollRoot(this.instance),\n                    offset: measureScroll(this.instance),\n                };\n            }\n        }\n        resetTransform() {\n            var _a;\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n            const transformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            // Remove viewport scroll to give page-relative coordinates\n            const { scroll } = this.root;\n            if (scroll) {\n                translateAxis(box.x, scroll.offset.x);\n                translateAxis(box.y, scroll.offset.y);\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.isRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                        const { scroll: rootScroll } = this.root;\n                        /**\n                         * Undo the application of page scroll that was originally added\n                         * to the measured bounding box.\n                         */\n                        if (rootScroll) {\n                            translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n                            translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n                        }\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            var _a;\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, (_a = node.snapshot) === null || _a === void 0 ? void 0 : _a.layoutBox, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        /**\n         *\n         */\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.isProjectionDirty = true;\n            this.root.scheduleUpdateProjection();\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        /**\n         * Frame calculations\n         */\n        resolveTargetDelta() {\n            var _a;\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            if (!this.isProjectionDirty && !this.attemptToResolveRelativeTarget)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            // TODO If this is unsuccessful this currently happens every frame\n            if (!this.targetDelta && !this.relativeTarget) {\n                // TODO: This is a semi-repetition of further down this function, make DRY\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent && relativeParent.layout) {\n                    this.relativeParent = relativeParent;\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.target)) {\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target) {\n                    this.relativeParent = relativeParent;\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues))\n                return undefined;\n            if ((this.parent.relativeTarget || this.parent.targetDelta) &&\n                this.parent.layout) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        calcProjection() {\n            var _a;\n            const { isProjectionDirty, isTransformDirty } = this;\n            this.isProjectionDirty = this.isTransformDirty = false;\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            if (isProjectionDirty)\n                canSkip = false;\n            if (isShared && isTransformDirty)\n                canSkip = false;\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean(((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            const { target } = lead;\n            if (!target)\n                return;\n            if (!this.projectionDelta) {\n                this.projectionDelta = createDelta();\n                this.projectionDeltaWithTransform = createDelta();\n            }\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            const prevProjectionTransform = this.projectionTransform;\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n            if (this.projectionTransform !== prevProjectionTransform ||\n                this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            var _a, _b, _c;\n            (_b = (_a = this.options).scheduleRender) === null || _b === void 0 ? void 0 : _b.call(_a);\n            notifyAll && ((_c = this.getStack()) === null || _c === void 0 ? void 0 : _c.scheduleRender());\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            var _a, _b;\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.latestValues) || {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            this.relativeTarget = this.relativeTargetOrigin = undefined;\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const isSharedLayoutAnimation = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.source) !== ((_a = this.layout) === null || _a === void 0 ? void 0 : _a.source);\n            const isOnlyMember = (((_b = this.getStack()) === null || _b === void 0 ? void 0 : _b.members.length) || 0) <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            this.mixTargetDelta = (latest) => {\n                var _a;\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.layout)) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(0);\n        }\n        startAnimation(options) {\n            var _a, _b;\n            this.notifyListeners(\"animationStart\");\n            (_a = this.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n            if (this.resumingFrom) {\n                (_b = this.resumingFrom.currentAnimation) === null || _b === void 0 ? void 0 : _b.stop();\n            }\n            if (this.pendingAnimation) {\n                cancelSync.update(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = sync.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                this.currentAnimation = animate(0, animationTarget, {\n                    ...options,\n                    onUpdate: (latest) => {\n                        var _a;\n                        this.mixTargetDelta(latest);\n                        (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, latest);\n                    },\n                    onComplete: () => {\n                        var _a;\n                        (_a = options.onComplete) === null || _a === void 0 ? void 0 : _a.call(options);\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            var _a;\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            var _a;\n            if (this.currentAnimation) {\n                (_a = this.mixTargetDelta) === null || _a === void 0 ? void 0 : _a.call(this, animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            var _a, _b, _c;\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            node.promote({\n                transition: (_a = node.options.initialPromotionConfig) === null || _a === void 0 ? void 0 : _a.transition,\n                preserveFollowOpacity: (_c = (_b = node.options.initialPromotionConfig) === null || _b === void 0 ? void 0 : _b.shouldPreserveFollowOpacity) === null || _c === void 0 ? void 0 : _c.call(_b, node),\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n        }\n        getPrevLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected rotation values, we can early return without a forced render.\n            let hasRotate = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ) {\n                hasRotate = true;\n            }\n            // If there's no rotation values, we don't need to do any more.\n            if (!hasRotate)\n                return;\n            const resetValues = {};\n            // Check the rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                const key = \"rotate\" + transformAxes[i];\n                // Record the rotation and then temporarily set it to 0\n                if (latestValues[key]) {\n                    resetValues[key] = latestValues[key];\n                    visualElement.setStaticValue(key, 0);\n                }\n            }\n            // Force a render of this element to apply the transform with all rotations\n            // set to 0.\n            visualElement === null || visualElement === void 0 ? void 0 : visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp = {}) {\n            var _a, _b, _c;\n            // TODO: Return lifecycle-persistent object\n            const styles = {};\n            if (!this.instance || this.isSVG)\n                return styles;\n            if (!this.isVisible) {\n                return { visibility: \"hidden\" };\n            }\n            else {\n                styles.visibility = \"\";\n            }\n            const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? (_c = (_b = valuesToRender.opacity) !== null && _b !== void 0 ? _b : this.latestValues.opacity) !== null && _c !== void 0 ? _c : 1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo } = scaleCorrectors[key];\n                const corrected = correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    styles[key] = corrected;\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => { var _a; return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop(); });\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    var _a, _b, _c;\n    const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeTargetChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEquals(relativeSnapshot, relativeLayout)) {\n                        hasRelativeTargetChanged = true;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n        });\n    }\n    else if (node.isLead()) {\n        (_c = (_b = node.options).onExitComplete) === null || _c === void 0 ? void 0 : _c.call(_b);\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Propagate isProjectionDirty. Nodes are ordered by depth, so if the parent here\n     * is dirty we can simply pass this forward.\n     */\n    node.isProjectionDirty || (node.isProjectionDirty = Boolean(node.parent && node.parent.isProjectionDirty));\n    /**\n     * Propagate isTransformDirty.\n     */\n    node.isTransformDirty || (node.isTransformDirty = Boolean(node.parent && node.parent.isTransformDirty));\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement === null || visualElement === void 0 ? void 0 : visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetRotation(node) {\n    node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mix(delta.translate, 0, p);\n    output.scale = mix(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mix(from.min, to.min, p);\n    output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nfunction mountNodeEarly(node, elementId) {\n    /**\n     * Rather than searching the DOM from document we can search the\n     * path for the deepest mounted ancestor and search from there\n     */\n    let searchNode = node.root;\n    for (let i = node.path.length - 1; i >= 0; i--) {\n        if (Boolean(node.path[i].instance)) {\n            searchNode = node.path[i];\n            break;\n        }\n    }\n    const searchElement = searchNode && searchNode !== node.root ? searchNode.instance : document;\n    const element = searchElement.querySelector(`[data-projection-id=\"${elementId}\"]`);\n    if (element)\n        node.mount(element, true);\n}\nfunction roundAxis(axis) {\n    axis.min = Math.round(axis.min);\n    axis.max = Math.round(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\n\nexport { createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,IAAI,QAAQ,2BAA2B;AACvE,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,uBAAuB;AAC3E,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,GAAG,QAAQ,qBAAqB;AAEzC,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV,SAASC,oBAAoBA,CAAC;EAAEC,oBAAoB;EAAEC,aAAa;EAAEC,aAAa;EAAEC,iBAAiB;EAAEC;AAAgB,CAAC,EAAE;EACtH,OAAO,MAAMC,cAAc,CAAC;IACxBC,WAAWA,CAACC,SAAS,EAAEC,YAAY,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAGR,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,EAAE;MAC9H;AACZ;AACA;MACY,IAAI,CAACH,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAACY,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B;MACA,IAAI,CAACC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;MAC/B,IAAI,CAACE,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACV,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACW,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAACH,KAAK,CAACC,OAAO,CAACG,cAAc,CAAC;MACtC,CAAC;MACD,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIb,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACtB,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACmC,IAAI,GAAGlC,MAAM,GAAGA,MAAM,CAACkC,IAAI,IAAIlC,MAAM,GAAG,IAAI;MACjD,IAAI,CAACmC,IAAI,GAAGnC,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACmC,IAAI,EAAEnC,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACoC,KAAK,GAAGpC,MAAM,GAAGA,MAAM,CAACoC,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1CtC,SAAS,IAAI,IAAI,CAACoC,IAAI,CAACG,qBAAqB,CAACvC,SAAS,EAAE,IAAI,CAAC;MAC7D,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAI,CAACH,IAAI,CAACG,CAAC,CAAC,CAACvB,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAACmB,IAAI,KAAK,IAAI,EAClB,IAAI,CAACT,KAAK,GAAG,IAAI3C,QAAQ,CAAC,CAAC;IACnC;IACA0D,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACvB,aAAa,CAACwB,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACtB,aAAa,CAACyB,GAAG,CAACH,IAAI,EAAE,IAAIrF,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAAC+D,aAAa,CAAC0B,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAE,GAAGO,IAAI,EAAE;MAC3B,MAAMC,mBAAmB,GAAG,IAAI,CAAC9B,aAAa,CAAC0B,GAAG,CAACJ,IAAI,CAAC;MACxDQ,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,MAAM,CAAC,GAAGF,IAAI,CAAC;IACjH;IACAG,YAAYA,CAACV,IAAI,EAAE;MACf,OAAO,IAAI,CAACtB,aAAa,CAACwB,GAAG,CAACF,IAAI,CAAC;IACvC;IACAJ,qBAAqBA,CAACvC,SAAS,EAAEsD,IAAI,EAAE;MACnC,IAAI,CAAC/B,cAAc,CAACuB,GAAG,CAAC9C,SAAS,EAAEsD,IAAI,CAAC;IAC5C;IACA;AACR;AACA;IACQC,KAAKA,CAACC,QAAQ,EAAE/C,aAAa,GAAG,KAAK,EAAE;MACnC,IAAIgD,EAAE;MACN,IAAI,IAAI,CAACD,QAAQ,EACb;MACJ,IAAI,CAACzC,KAAK,GACNyC,QAAQ,YAAYE,UAAU,IAAIF,QAAQ,CAACG,OAAO,KAAK,KAAK;MAChE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAEI,QAAQ;QAAEC,MAAM;QAAEC;MAAc,CAAC,GAAG,IAAI,CAACxD,OAAO;MACxD,IAAIwD,aAAa,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;QACzCD,aAAa,CAACP,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAACpB,IAAI,CAACT,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC;MACzB,CAACS,EAAE,GAAG,IAAI,CAACvD,MAAM,MAAM,IAAI,IAAIuD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrD,QAAQ,CAAC4C,GAAG,CAAC,IAAI,CAAC;MAC7E,IAAI,CAAChD,SAAS,IAAI,IAAI,CAACoC,IAAI,CAACb,cAAc,CAACyC,MAAM,CAAC,IAAI,CAAChE,SAAS,CAAC;MACjE,IAAIS,aAAa,KAAKoD,MAAM,IAAID,QAAQ,CAAC,EAAE;QACvC,IAAI,CAACnD,aAAa,GAAG,IAAI;MAC7B;MACA,IAAIhB,oBAAoB,EAAE;QACtB,IAAIwE,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAAC9B,IAAI,CAACvB,qBAAqB,GAAG,KAAM;QAC3EpB,oBAAoB,CAAC+D,QAAQ,EAAE,MAAM;UACjC,IAAI,CAACpB,IAAI,CAACvB,qBAAqB,GAAG,IAAI;UACtCoD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAG9E,KAAK,CAAC+E,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAIhF,qBAAqB,CAACiF,sBAAsB,EAAE;YAC9CjF,qBAAqB,CAACiF,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAACxC,KAAK,CAACC,OAAO,CAACwC,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAIR,QAAQ,EAAE;QACV,IAAI,CAACxB,IAAI,CAACiC,kBAAkB,CAACT,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACtD,OAAO,CAACjD,OAAO,KAAK,KAAK,IAC9ByG,aAAa,KACZF,QAAQ,IAAIC,MAAM,CAAC,EAAE;QACtB,IAAI,CAACnB,gBAAgB,CAAC,WAAW,EAAE,CAAC;UAAE4B,KAAK;UAAEC,gBAAgB;UAAEC,wBAAwB;UAAEX,MAAM,EAAEY;QAAW,CAAC,KAAK;UAC9G,IAAIhB,EAAE,EAAEiB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;UACtB,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAGC,SAAS;YACvB,IAAI,CAACC,cAAc,GAAGD,SAAS;YAC/B;UACJ;UACA;UACA,MAAME,gBAAgB,GAAG,CAACR,EAAE,GAAG,CAACjB,EAAE,GAAG,IAAI,CAACnD,OAAO,CAAC6E,UAAU,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGK,aAAa,CAACsB,oBAAoB,CAAC,CAAC,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGW,uBAAuB;UAC7L,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAGzB,aAAa,CAAC0B,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACC,YAAY,IACpC,CAACpH,SAAS,CAAC,IAAI,CAACoH,YAAY,EAAEjB,SAAS,CAAC,IACxCD,wBAAwB;UAC5B;AACpB;AACA;AACA;AACA;UACoB,MAAMmB,4BAA4B,GAAG,CAACpB,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,CAAC,CAACG,EAAE,GAAG,IAAI,CAACiB,UAAU,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnB,QAAQ,KACxEmC,4BAA4B,IAC3BpB,gBAAgB,KACZkB,aAAa,IAAI,CAAC,IAAI,CAACI,gBAAgB,CAAE,EAAE;YAChD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAGd,SAAS;YAC9C;YACA,IAAI,CAACe,kBAAkB,CAACzB,KAAK,EAAEqB,4BAA4B,CAAC;YAC5D,MAAMK,gBAAgB,GAAG;cACrB,GAAG3H,kBAAkB,CAAC6G,gBAAgB,EAAE,QAAQ,CAAC;cACjDe,MAAM,EAAEX,sBAAsB;cAC9BY,UAAU,EAAEX;YAChB,CAAC;YACD,IAAIzB,aAAa,CAACqC,kBAAkB,EAAE;cAClCH,gBAAgB,CAAC7G,KAAK,GAAG,CAAC;cAC1B6G,gBAAgB,CAACI,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAACC,cAAc,CAACL,gBAAgB,CAAC;UACzC,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAACzB,gBAAgB,IACjB,IAAI,CAACrC,iBAAiB,KAAK,CAAC,EAAE;cAC9BkC,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,CAACkC,MAAM,CAAC,CAAC,KAAK,CAACzB,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACtE,OAAO,EAAEiG,cAAc,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,IAAI,CAAC5B,EAAE,CAAC,CAAC;UACjH;UACA,IAAI,CAACc,YAAY,GAAGjB,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACAgC,OAAOA,CAAA,EAAG;MACN,IAAIhD,EAAE,EAAEiB,EAAE;MACV,IAAI,CAACpE,OAAO,CAACsD,QAAQ,IAAI,IAAI,CAAC8C,UAAU,CAAC,CAAC;MAC1C,IAAI,CAACtE,IAAI,CAACT,KAAK,CAACgF,MAAM,CAAC,IAAI,CAAC;MAC5B,CAAClD,EAAE,GAAG,IAAI,CAACmD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkD,MAAM,CAAC,IAAI,CAAC;MAC3E,CAACjC,EAAE,GAAG,IAAI,CAACxE,MAAM,MAAM,IAAI,IAAIwE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACtE,QAAQ,CAAC4D,MAAM,CAAC,IAAI,CAAC;MAChF,IAAI,CAACR,QAAQ,GAAGwB,SAAS;MACzB9H,UAAU,CAAC2J,SAAS,CAAC,IAAI,CAACnF,gBAAgB,CAAC;IAC/C;IACA;IACAoF,WAAWA,CAAA,EAAG;MACV,IAAI,CAAClG,qBAAqB,GAAG,IAAI;IACrC;IACAmG,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACnG,qBAAqB,GAAG,KAAK;IACtC;IACAoG,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACpG,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACAiE,sBAAsBA,CAAA,EAAG;MACrB,IAAIrB,EAAE;MACN,OAAQ,IAAI,CAACjD,kBAAkB,KAC1B,CAACiD,EAAE,GAAG,IAAI,CAACvD,MAAM,MAAM,IAAI,IAAIuD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqB,sBAAsB,CAAC,CAAC,CAAC,IACrF,KAAK;IACb;IACA;IACAmC,WAAWA,CAAA,EAAG;MACV,IAAIxD,EAAE;MACN,IAAI,IAAI,CAACuD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAAClG,UAAU,GAAG,IAAI;MACtB,CAAC2C,EAAE,GAAG,IAAI,CAAC9B,KAAK,MAAM,IAAI,IAAI8B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7B,OAAO,CAACsF,aAAa,CAAC;MAChF,IAAI,CAAC/G,WAAW,EAAE;IACtB;IACAuG,UAAUA,CAACS,qBAAqB,GAAG,IAAI,EAAE;MACrC,IAAI1D,EAAE,EAAEiB,EAAE,EAAEC,EAAE;MACd,IAAI,IAAI,CAACvC,IAAI,CAAC4E,eAAe,CAAC,CAAC,EAAE;QAC7B,CAACtC,EAAE,GAAG,CAACjB,EAAE,GAAG,IAAI,CAACnD,OAAO,EAAEiG,cAAc,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,IAAI,CAAC/C,EAAE,CAAC;QAC1F;MACJ;MACA,CAAC,IAAI,CAACrB,IAAI,CAACtB,UAAU,IAAI,IAAI,CAACsB,IAAI,CAAC6E,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAACxG,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMc,IAAI,GAAG,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC;QACzBc,IAAI,CAACrC,oBAAoB,GAAG,IAAI;QAChCqC,IAAI,CAAC8D,YAAY,CAAC,UAAU,CAAC;MACjC;MACA,MAAM;QAAExD,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACvD,OAAO;MACzC,IAAIsD,QAAQ,KAAKoB,SAAS,IAAI,CAACnB,MAAM,EACjC;MACJ,MAAMwD,iBAAiB,GAAG,CAAC1C,EAAE,GAAG,IAAI,CAACrE,OAAO,CAACwD,aAAa,MAAM,IAAI,IAAIa,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,QAAQ,CAAC,CAAC,CAAC6B,iBAAiB;MAChI,IAAI,CAACC,0BAA0B,GAAGD,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,IAAI,CAACpH,YAAY,EAAE,EAAE,CAAC;MAChJ,IAAI,CAACsH,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAAClE,eAAe,CAAC,YAAY,CAAC;IAC/D;IACA;IACAuE,SAASA,CAAA,EAAG;MACR,MAAMC,gBAAgB,GAAG,IAAI,CAACT,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIS,gBAAgB,EAAE;QAClB,IAAI,CAACV,aAAa,CAAC,CAAC;QACpB,IAAI,CAACtF,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAAC8F,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAAC5G,UAAU,EAChB;MACJ,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACS,cAAc,CAACoG,IAAI,EAAE;QAC1B,IAAI,CAACpG,cAAc,CAACK,OAAO,CAACgG,cAAc,CAAC;QAC3C,IAAI,CAACrG,cAAc,CAACsG,KAAK,CAAC,CAAC;MAC/B;MACA;AACZ;AACA;MACY,IAAI,CAAClG,KAAK,CAACC,OAAO,CAACkG,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAACnG,KAAK,CAACC,OAAO,CAACmG,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAACpG,KAAK,CAACC,OAAO,CAACoG,kBAAkB,CAAC;MACtC,IAAI,CAACvG,iBAAiB,CAAC,CAAC;MACxB;MACAtE,SAAS,CAAC8K,MAAM,CAAC,CAAC;MAClB9K,SAAS,CAAC0J,SAAS,CAAC,CAAC;MACrB1J,SAAS,CAAC+K,MAAM,CAAC,CAAC;IACtB;IACAzG,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACuG,aAAa,CAAC;MACjC,IAAI,CAAChG,WAAW,CAACP,OAAO,CAACwG,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvBjL,IAAI,CAACyJ,SAAS,CAAC,IAAI,CAACnF,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;IACtD;IACA4G,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACYlL,IAAI,CAACmL,UAAU,CAAC,MAAM;QAClB,IAAI,IAAI,CAAC9H,aAAa,EAAE;UACpB,IAAI,CAAC2B,IAAI,CAACoF,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAACpF,IAAI,CAACZ,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQ+F,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACiB,QAAQ,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAC/B;MACJ,IAAI,CAACgF,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAClC;IACAV,YAAYA,CAAA,EAAG;MACX,IAAItE,EAAE;MACN,IAAI,CAAC,IAAI,CAACD,QAAQ,EACd;MACJ;MACA,IAAI,CAAC4D,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAAC9G,OAAO,CAACoI,mBAAmB,IAAI,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAAC7F,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACmF,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACpC,QAAQ,EAAE;QAC9C,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,MAAMc,IAAI,GAAG,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC;UACzBc,IAAI,CAAC8D,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAMuB,UAAU,GAAG,IAAI,CAAC9E,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC4E,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACG,eAAe,GAAGzK,SAAS,CAAC,CAAC;MAClC,IAAI,CAACsC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACoI,eAAe,GAAG7D,SAAS;MAChC,IAAI,CAAC/B,eAAe,CAAC,SAAS,EAAE,IAAI,CAACY,MAAM,CAACiF,SAAS,CAAC;MACtD,CAACrF,EAAE,GAAG,IAAI,CAACnD,OAAO,CAACwD,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACL,MAAM,CAAC,eAAe,EAAE,IAAI,CAACS,MAAM,CAACiF,SAAS,EAAEH,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,SAAS,CAAC;IAC1M;IACA1B,YAAYA,CAAC2B,KAAK,GAAG,SAAS,EAAE;MAC5B,IAAIC,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAAC3I,OAAO,CAAC4I,YAAY,IAAI,IAAI,CAAC1F,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAAC2F,MAAM,IACX,IAAI,CAACA,MAAM,CAAChJ,WAAW,KAAK,IAAI,CAACiC,IAAI,CAACjC,WAAW,IACjD,IAAI,CAACgJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,EAAE;QAClB,IAAI,CAACG,MAAM,GAAG;UACVhJ,WAAW,EAAE,IAAI,CAACiC,IAAI,CAACjC,WAAW;UAClC4I,KAAK;UACLK,MAAM,EAAExJ,iBAAiB,CAAC,IAAI,CAAC4D,QAAQ,CAAC;UACxC6F,MAAM,EAAE1J,aAAa,CAAC,IAAI,CAAC6D,QAAQ;QACvC,CAAC;MACL;IACJ;IACA3D,cAAcA,CAAA,EAAG;MACb,IAAI4D,EAAE;MACN,IAAI,CAAC5D,cAAc,EACf;MACJ,MAAMyJ,gBAAgB,GAAG,IAAI,CAAC7I,aAAa,IAAI,IAAI,CAACQ,oBAAoB;MACxE,MAAMsI,aAAa,GAAG,IAAI,CAACV,eAAe,IAAI,CAACtK,WAAW,CAAC,IAAI,CAACsK,eAAe,CAAC;MAChF,MAAMxB,iBAAiB,GAAG,CAAC5D,EAAE,GAAG,IAAI,CAACnD,OAAO,CAACwD,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,QAAQ,CAAC,CAAC,CAAC6B,iBAAiB;MAChI,MAAMmC,sBAAsB,GAAGnC,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,IAAI,CAACpH,YAAY,EAAE,EAAE,CAAC;MAC7I,MAAMwJ,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAAClC,0BAA0B;MAC9F,IAAIgC,gBAAgB,KACfC,aAAa,IACV1K,YAAY,CAAC,IAAI,CAACoB,YAAY,CAAC,IAC/BwJ,2BAA2B,CAAC,EAAE;QAClC5J,cAAc,CAAC,IAAI,CAAC2D,QAAQ,EAAEgG,sBAAsB,CAAC;QACrD,IAAI,CAACvI,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACyI,cAAc,CAAC,CAAC;MACzB;IACJ;IACAjB,OAAOA,CAACkB,eAAe,GAAG,IAAI,EAAE;MAC5B,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIf,SAAS,GAAG,IAAI,CAACgB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBb,SAAS,GAAG,IAAI,CAACa,eAAe,CAACb,SAAS,CAAC;MAC/C;MACAiB,QAAQ,CAACjB,SAAS,CAAC;MACnB,OAAO;QACH3I,WAAW,EAAE,IAAI,CAACiC,IAAI,CAACjC,WAAW;QAClC6J,WAAW,EAAEJ,OAAO;QACpBd,SAAS;QACT7I,YAAY,EAAE,CAAC,CAAC;QAChBgK,MAAM,EAAE,IAAI,CAAC1K;MACjB,CAAC;IACL;IACAsK,cAAcA,CAAA,EAAG;MACb,MAAM;QAAE/F;MAAc,CAAC,GAAG,IAAI,CAACxD,OAAO;MACtC,IAAI,CAACwD,aAAa,EACd,OAAO3F,SAAS,CAAC,CAAC;MACtB,MAAM+L,GAAG,GAAGpG,aAAa,CAACqG,kBAAkB,CAAC,CAAC;MAC9C;MACA,MAAM;QAAEhB;MAAO,CAAC,GAAG,IAAI,CAAC/G,IAAI;MAC5B,IAAI+G,MAAM,EAAE;QACR1L,aAAa,CAACyM,GAAG,CAAC/I,CAAC,EAAEgI,MAAM,CAACE,MAAM,CAAClI,CAAC,CAAC;QACrC1D,aAAa,CAACyM,GAAG,CAAC9I,CAAC,EAAE+H,MAAM,CAACE,MAAM,CAACjI,CAAC,CAAC;MACzC;MACA,OAAO8I,GAAG;IACd;IACAJ,mBAAmBA,CAACI,GAAG,EAAE;MACrB,MAAME,gBAAgB,GAAGjM,SAAS,CAAC,CAAC;MACpCX,WAAW,CAAC4M,gBAAgB,EAAEF,GAAG,CAAC;MAClC;AACZ;AACA;AACA;MACY,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMc,IAAI,GAAG,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC;QACzB,MAAM;UAAE2G,MAAM;UAAE7I;QAAQ,CAAC,GAAGgD,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAAClB,IAAI,IAAI+G,MAAM,IAAI7I,OAAO,CAAC4I,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACC,MAAM,EAAE;YACf5L,WAAW,CAAC4M,gBAAgB,EAAEF,GAAG,CAAC;YAClC,MAAM;cAAEf,MAAM,EAAEkB;YAAW,CAAC,GAAG,IAAI,CAACjI,IAAI;YACxC;AACxB;AACA;AACA;YACwB,IAAIiI,UAAU,EAAE;cACZ5M,aAAa,CAAC2M,gBAAgB,CAACjJ,CAAC,EAAE,CAACkJ,UAAU,CAAChB,MAAM,CAAClI,CAAC,CAAC;cACvD1D,aAAa,CAAC2M,gBAAgB,CAAChJ,CAAC,EAAE,CAACiJ,UAAU,CAAChB,MAAM,CAACjI,CAAC,CAAC;YAC3D;UACJ;UACA3D,aAAa,CAAC2M,gBAAgB,CAACjJ,CAAC,EAAEgI,MAAM,CAACE,MAAM,CAAClI,CAAC,CAAC;UAClD1D,aAAa,CAAC2M,gBAAgB,CAAChJ,CAAC,EAAE+H,MAAM,CAACE,MAAM,CAACjI,CAAC,CAAC;QACtD;MACJ;MACA,OAAOgJ,gBAAgB;IAC3B;IACAE,cAAcA,CAACJ,GAAG,EAAEK,aAAa,GAAG,KAAK,EAAE;MACvC,MAAMC,cAAc,GAAGrM,SAAS,CAAC,CAAC;MAClCX,WAAW,CAACgN,cAAc,EAAEN,GAAG,CAAC;MAChC,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMc,IAAI,GAAG,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC;QACzB,IAAI,CAAC+H,aAAa,IACdjH,IAAI,CAAChD,OAAO,CAAC4I,YAAY,IACzB5F,IAAI,CAAC6F,MAAM,IACX7F,IAAI,KAAKA,IAAI,CAAClB,IAAI,EAAE;UACpB1E,YAAY,CAAC8M,cAAc,EAAE;YACzBrJ,CAAC,EAAE,CAACmC,IAAI,CAAC6F,MAAM,CAACE,MAAM,CAAClI,CAAC;YACxBC,CAAC,EAAE,CAACkC,IAAI,CAAC6F,MAAM,CAACE,MAAM,CAACjI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAACvC,YAAY,CAACyE,IAAI,CAACrD,YAAY,CAAC,EAChC;QACJvC,YAAY,CAAC8M,cAAc,EAAElH,IAAI,CAACrD,YAAY,CAAC;MACnD;MACA,IAAIpB,YAAY,CAAC,IAAI,CAACoB,YAAY,CAAC,EAAE;QACjCvC,YAAY,CAAC8M,cAAc,EAAE,IAAI,CAACvK,YAAY,CAAC;MACnD;MACA,OAAOuK,cAAc;IACzB;IACAb,eAAeA,CAACO,GAAG,EAAE;MACjB,IAAIzG,EAAE;MACN,MAAMgH,mBAAmB,GAAGtM,SAAS,CAAC,CAAC;MACvCX,WAAW,CAACiN,mBAAmB,EAAEP,GAAG,CAAC;MACrC,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMc,IAAI,GAAG,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC;QACzB,IAAI,CAACc,IAAI,CAACE,QAAQ,EACd;QACJ,IAAI,CAAC3E,YAAY,CAACyE,IAAI,CAACrD,YAAY,CAAC,EAChC;QACJnB,QAAQ,CAACwE,IAAI,CAACrD,YAAY,CAAC,IAAIqD,IAAI,CAACiE,cAAc,CAAC,CAAC;QACpD,MAAMmD,SAAS,GAAGvM,SAAS,CAAC,CAAC;QAC7B,MAAMwM,OAAO,GAAGrH,IAAI,CAACuG,cAAc,CAAC,CAAC;QACrCrM,WAAW,CAACkN,SAAS,EAAEC,OAAO,CAAC;QAC/BzM,mBAAmB,CAACuM,mBAAmB,EAAEnH,IAAI,CAACrD,YAAY,EAAE,CAACwD,EAAE,GAAGH,IAAI,CAACkF,QAAQ,MAAM,IAAI,IAAI/E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqF,SAAS,EAAE4B,SAAS,CAAC;MAClJ;MACA,IAAI7L,YAAY,CAAC,IAAI,CAACoB,YAAY,CAAC,EAAE;QACjC/B,mBAAmB,CAACuM,mBAAmB,EAAE,IAAI,CAACxK,YAAY,CAAC;MAC/D;MACA,OAAOwK,mBAAmB;IAC9B;IACA;AACR;AACA;IACQG,cAAcA,CAACtG,KAAK,EAAE;MAClB,IAAI,CAACuG,WAAW,GAAGvG,KAAK;MACxB,IAAI,CAAC3D,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACyB,IAAI,CAACiG,wBAAwB,CAAC,CAAC;IACxC;IACAyC,UAAUA,CAACxK,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAG;QACX,GAAG,IAAI,CAACA,OAAO;QACf,GAAGA,OAAO;QACVyK,SAAS,EAAEzK,OAAO,CAACyK,SAAS,KAAK/F,SAAS,GAAG1E,OAAO,CAACyK,SAAS,GAAG;MACrE,CAAC;IACL;IACArD,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACyB,MAAM,GAAGnE,SAAS;MACvB,IAAI,CAACnB,MAAM,GAAGmB,SAAS;MACvB,IAAI,CAACwD,QAAQ,GAAGxD,SAAS;MACzB,IAAI,CAACsC,0BAA0B,GAAGtC,SAAS;MAC3C,IAAI,CAAC6F,WAAW,GAAG7F,SAAS;MAC5B,IAAI,CAACD,MAAM,GAAGC,SAAS;MACvB,IAAI,CAACvE,aAAa,GAAG,KAAK;IAC9B;IACA;AACR;AACA;IACQqB,kBAAkBA,CAAA,EAAG;MACjB,IAAI2B,EAAE;MACN;AACZ;AACA;AACA;AACA;MACY,MAAMuH,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACtK,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAGqK,IAAI,CAACrK,iBAAiB,CAAC;MAC3E,IAAI,CAACD,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAGsK,IAAI,CAACtK,gBAAgB,CAAC;MACxE;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACC,iBAAiB,IAAI,CAAC,IAAI,CAACuK,8BAA8B,EAC/D;MACJ,MAAM;QAAErH,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACtD,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACuD,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;AACA;MACY;MACA,IAAI,CAAC,IAAI,CAACiH,WAAW,IAAI,CAAC,IAAI,CAAC5F,cAAc,EAAE;QAC3C;QACA,MAAMkG,cAAc,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACxD,IAAID,cAAc,IAAIA,cAAc,CAACtH,MAAM,EAAE;UACzC,IAAI,CAACsH,cAAc,GAAGA,cAAc;UACpC,IAAI,CAAClG,cAAc,GAAG9G,SAAS,CAAC,CAAC;UACjC,IAAI,CAACkN,oBAAoB,GAAGlN,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACwN,oBAAoB,EAAE,IAAI,CAACxH,MAAM,CAACiF,SAAS,EAAEqC,cAAc,CAACtH,MAAM,CAACiF,SAAS,CAAC;UACvGtL,WAAW,CAAC,IAAI,CAACyH,cAAc,EAAE,IAAI,CAACoG,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACF,cAAc,GAAG,IAAI,CAAClG,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACC,cAAc,IAAI,CAAC,IAAI,CAAC4F,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAAC9F,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAG5G,SAAS,CAAC,CAAC;QACzB,IAAI,CAACmN,oBAAoB,GAAGnN,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC8G,cAAc,IACnB,IAAI,CAACoG,oBAAoB,KACxB,CAAC5H,EAAE,GAAG,IAAI,CAAC0H,cAAc,MAAM,IAAI,IAAI1H,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,MAAM,CAAC,EAAE;QAC7EjH,eAAe,CAAC,IAAI,CAACiH,MAAM,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACkG,cAAc,CAACpG,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAAC8F,WAAW,EAAE;QACvB,IAAI5B,OAAO,CAAC,IAAI,CAACnD,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAACf,MAAM,GAAG,IAAI,CAACuF,cAAc,CAAC,IAAI,CAACzG,MAAM,CAACiF,SAAS,CAAC;QAC5D,CAAC,MACI;UACDtL,WAAW,CAAC,IAAI,CAACuH,MAAM,EAAE,IAAI,CAAClB,MAAM,CAACiF,SAAS,CAAC;QACnD;QACAnL,aAAa,CAAC,IAAI,CAACoH,MAAM,EAAE,IAAI,CAAC8F,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgBrN,WAAW,CAAC,IAAI,CAACuH,MAAM,EAAE,IAAI,CAAClB,MAAM,CAACiF,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACoC,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMC,cAAc,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACxD,IAAID,cAAc,IACdlC,OAAO,CAACkC,cAAc,CAACrF,YAAY,CAAC,KAChCmD,OAAO,CAAC,IAAI,CAACnD,YAAY,CAAC,IAC9B,CAACqF,cAAc,CAAC7K,OAAO,CAAC4I,YAAY,IACpCiC,cAAc,CAACpG,MAAM,EAAE;UACvB,IAAI,CAACoG,cAAc,GAAGA,cAAc;UACpC,IAAI,CAAClG,cAAc,GAAG9G,SAAS,CAAC,CAAC;UACjC,IAAI,CAACkN,oBAAoB,GAAGlN,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACwN,oBAAoB,EAAE,IAAI,CAACtG,MAAM,EAAEoG,cAAc,CAACpG,MAAM,CAAC;UACnFvH,WAAW,CAAC,IAAI,CAACyH,cAAc,EAAE,IAAI,CAACoG,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACF,cAAc,GAAG,IAAI,CAAClG,cAAc,GAAGD,SAAS;QACzD;MACJ;IACJ;IACAoG,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAClL,MAAM,IACZpB,QAAQ,CAAC,IAAI,CAACoB,MAAM,CAACD,YAAY,CAAC,IAClClB,cAAc,CAAC,IAAI,CAACmB,MAAM,CAACD,YAAY,CAAC,EACxC,OAAO+E,SAAS;MACpB,IAAI,CAAC,IAAI,CAAC9E,MAAM,CAAC+E,cAAc,IAAI,IAAI,CAAC/E,MAAM,CAAC2K,WAAW,KACtD,IAAI,CAAC3K,MAAM,CAAC2D,MAAM,EAAE;QACpB,OAAO,IAAI,CAAC3D,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAACkL,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACArJ,cAAcA,CAAA,EAAG;MACb,IAAI0B,EAAE;MACN,MAAM;QAAE9C,iBAAiB;QAAED;MAAiB,CAAC,GAAG,IAAI;MACpD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACD,gBAAgB,GAAG,KAAK;MACtD,MAAMsK,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMM,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAACnD,YAAY,CAAC,IAAI,IAAI,KAAKkF,IAAI;MAC5D,IAAIQ,OAAO,GAAG,IAAI;MAClB,IAAI7K,iBAAiB,EACjB6K,OAAO,GAAG,KAAK;MACnB,IAAID,QAAQ,IAAI7K,gBAAgB,EAC5B8K,OAAO,GAAG,KAAK;MACnB,IAAIA,OAAO,EACP;MACJ,MAAM;QAAE3H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACtD,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG0I,OAAO,CAAC,CAAC,CAACxF,EAAE,GAAG,IAAI,CAACvD,MAAM,MAAM,IAAI,IAAIuD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAClD,eAAe,KACtG,IAAI,CAACsF,gBAAgB,IACrB,IAAI,CAAC4F,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAAClL,eAAe,EAAE;QACvB,IAAI,CAACsK,WAAW,GAAG,IAAI,CAAC5F,cAAc,GAAGD,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAACnB,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACYpG,WAAW,CAAC,IAAI,CAACoL,eAAe,EAAE,IAAI,CAAC/E,MAAM,CAACiF,SAAS,CAAC;MACxD;AACZ;AACA;AACA;MACYlL,eAAe,CAAC,IAAI,CAACgL,eAAe,EAAE,IAAI,CAAC1H,SAAS,EAAE,IAAI,CAACmB,IAAI,EAAEkJ,QAAQ,CAAC;MAC1E,MAAM;QAAExG;MAAO,CAAC,GAAGiG,IAAI;MACvB,IAAI,CAACjG,MAAM,EACP;MACJ,IAAI,CAAC,IAAI,CAAC8D,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAGzK,WAAW,CAAC,CAAC;QACpC,IAAI,CAACsN,4BAA4B,GAAGtN,WAAW,CAAC,CAAC;MACrD;MACA,MAAMuN,cAAc,GAAG,IAAI,CAACzK,SAAS,CAACC,CAAC;MACvC,MAAMyK,cAAc,GAAG,IAAI,CAAC1K,SAAS,CAACE,CAAC;MACvC,MAAMyK,uBAAuB,GAAG,IAAI,CAACC,mBAAmB;MACxD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY/N,YAAY,CAAC,IAAI,CAAC8K,eAAe,EAAE,IAAI,CAACD,eAAe,EAAE7D,MAAM,EAAE,IAAI,CAAC9E,YAAY,CAAC;MACnF,IAAI,CAAC6L,mBAAmB,GAAGnN,wBAAwB,CAAC,IAAI,CAACkK,eAAe,EAAE,IAAI,CAAC3H,SAAS,CAAC;MACzF,IAAI,IAAI,CAAC4K,mBAAmB,KAAKD,uBAAuB,IACpD,IAAI,CAAC3K,SAAS,CAACC,CAAC,KAAKwK,cAAc,IACnC,IAAI,CAACzK,SAAS,CAACE,CAAC,KAAKwK,cAAc,EAAE;QACrC,IAAI,CAAC5J,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC0H,cAAc,CAAC,CAAC;QACrB,IAAI,CAACzG,eAAe,CAAC,kBAAkB,EAAE8B,MAAM,CAAC;MACpD;IACJ;IACAgH,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC9J,SAAS,GAAG,KAAK;MACtB;IACJ;IACA+J,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC/J,SAAS,GAAG,IAAI;MACrB;IACJ;IACAyH,cAAcA,CAACuC,SAAS,GAAG,IAAI,EAAE;MAC7B,IAAIxI,EAAE,EAAEiB,EAAE,EAAEC,EAAE;MACd,CAACD,EAAE,GAAG,CAACjB,EAAE,GAAG,IAAI,CAACnD,OAAO,EAAEoJ,cAAc,MAAM,IAAI,IAAIhF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,IAAI,CAAC/C,EAAE,CAAC;MAC1FwI,SAAS,KAAK,CAACtH,EAAE,GAAG,IAAI,CAACiC,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+E,cAAc,CAAC,CAAC,CAAC;MAC9F,IAAI,IAAI,CAAC5D,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACtC,QAAQ,EAAE;QAClD,IAAI,CAACsC,YAAY,GAAGd,SAAS;MACjC;IACJ;IACAe,kBAAkBA,CAACzB,KAAK,EAAEqB,4BAA4B,GAAG,KAAK,EAAE;MAC5D,IAAIlC,EAAE,EAAEiB,EAAE;MACV,MAAM8D,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAM0D,oBAAoB,GAAG,CAAC1D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvI,YAAY,KAAK,CAAC,CAAC;MAC9G,MAAMkM,WAAW,GAAG;QAAE,GAAG,IAAI,CAAClM;MAAa,CAAC;MAC5C,MAAM4K,WAAW,GAAGzM,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC6G,cAAc,GAAG,IAAI,CAACoG,oBAAoB,GAAGrG,SAAS;MAC3D,IAAI,CAACkG,8BAA8B,GAAG,CAACvF,4BAA4B;MACnE,MAAMyG,cAAc,GAAGjO,SAAS,CAAC,CAAC;MAClC,MAAMkO,uBAAuB,GAAG,CAAC7D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,MAAM,OAAO,CAACxG,EAAE,GAAG,IAAI,CAACI,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwG,MAAM,CAAC;MAC7K,MAAMqC,YAAY,GAAG,CAAC,CAAC,CAAC5H,EAAE,GAAG,IAAI,CAACkC,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6H,OAAO,CAAC9J,MAAM,KAAK,CAAC,KAAK,CAAC;MAChH,MAAM+J,sBAAsB,GAAGvD,OAAO,CAACoD,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAAChM,OAAO,CAACyK,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAAC1I,IAAI,CAACoK,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAACxK,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACyK,cAAc,GAAIC,MAAM,IAAK;QAC9B,IAAInJ,EAAE;QACN,MAAMoJ,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACjC,WAAW,CAAC1J,CAAC,EAAEmD,KAAK,CAACnD,CAAC,EAAE0L,QAAQ,CAAC;QAC9CC,YAAY,CAACjC,WAAW,CAACzJ,CAAC,EAAEkD,KAAK,CAAClD,CAAC,EAAEyL,QAAQ,CAAC;QAC9C,IAAI,CAACjC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAAC5F,cAAc,IACnB,IAAI,CAACoG,oBAAoB,IACzB,IAAI,CAACxH,MAAM,KACV,CAACJ,EAAE,GAAG,IAAI,CAAC0H,cAAc,MAAM,IAAI,IAAI1H,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM,CAAC,EAAE;UAC7EhG,oBAAoB,CAACuO,cAAc,EAAE,IAAI,CAACvI,MAAM,CAACiF,SAAS,EAAE,IAAI,CAACqC,cAAc,CAACtH,MAAM,CAACiF,SAAS,CAAC;UACjGiE,MAAM,CAAC,IAAI,CAAC9H,cAAc,EAAE,IAAI,CAACoG,oBAAoB,EAAEe,cAAc,EAAES,QAAQ,CAAC;QACpF;QACA,IAAIR,uBAAuB,EAAE;UACzB,IAAI,CAACW,eAAe,GAAGb,WAAW;UAClC5O,SAAS,CAAC4O,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAACjM,YAAY,EAAE4M,QAAQ,EAAEL,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAAClK,IAAI,CAACiG,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACqB,cAAc,CAAC,CAAC;QACrB,IAAI,CAACxH,iBAAiB,GAAG2K,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,CAAC,CAAC;IAC1B;IACAtG,cAAcA,CAAC/F,OAAO,EAAE;MACpB,IAAImD,EAAE,EAAEiB,EAAE;MACV,IAAI,CAACzB,eAAe,CAAC,gBAAgB,CAAC;MACtC,CAACQ,EAAE,GAAG,IAAI,CAACoC,gBAAgB,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwJ,IAAI,CAAC,CAAC;MAC3E,IAAI,IAAI,CAACnH,YAAY,EAAE;QACnB,CAACpB,EAAE,GAAG,IAAI,CAACoB,YAAY,CAACD,gBAAgB,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuI,IAAI,CAAC,CAAC;MAC5F;MACA,IAAI,IAAI,CAACxB,gBAAgB,EAAE;QACvBvO,UAAU,CAAC+K,MAAM,CAAC,IAAI,CAACwD,gBAAgB,CAAC;QACxC,IAAI,CAACA,gBAAgB,GAAGzG,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACyG,gBAAgB,GAAGrO,IAAI,CAAC6K,MAAM,CAAC,MAAM;QACtC/I,qBAAqB,CAACiF,sBAAsB,GAAG,IAAI;QACnD,IAAI,CAAC0B,gBAAgB,GAAGxI,OAAO,CAAC,CAAC,EAAEiC,eAAe,EAAE;UAChD,GAAGgB,OAAO;UACV4M,QAAQ,EAAGN,MAAM,IAAK;YAClB,IAAInJ,EAAE;YACN,IAAI,CAACkJ,cAAc,CAACC,MAAM,CAAC;YAC3B,CAACnJ,EAAE,GAAGnD,OAAO,CAAC4M,QAAQ,MAAM,IAAI,IAAIzJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+C,IAAI,CAAClG,OAAO,EAAEsM,MAAM,CAAC;UACzF,CAAC;UACD1G,UAAU,EAAEA,CAAA,KAAM;YACd,IAAIzC,EAAE;YACN,CAACA,EAAE,GAAGnD,OAAO,CAAC4F,UAAU,MAAM,IAAI,IAAIzC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+C,IAAI,CAAClG,OAAO,CAAC;YAC/E,IAAI,CAAC6M,iBAAiB,CAAC,CAAC;UAC5B;QACJ,CAAC,CAAC;QACF,IAAI,IAAI,CAACrH,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAAC4F,gBAAgB,GAAGzG,SAAS;MACrC,CAAC,CAAC;IACN;IACAmI,iBAAiBA,CAAA,EAAG;MAChB,IAAI1J,EAAE;MACN,IAAI,IAAI,CAACqC,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAGb,SAAS;QAC9C,IAAI,CAACc,YAAY,CAACsH,eAAe,GAAGpI,SAAS;MACjD;MACA,CAACvB,EAAE,GAAG,IAAI,CAACmD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4J,qBAAqB,CAAC,CAAC;MACtF,IAAI,CAACvH,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACmH,eAAe,GAChBhI,SAAS;MACrB,IAAI,CAAC/B,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAmB,eAAeA,CAAA,EAAG;MACd,IAAIX,EAAE;MACN,IAAI,IAAI,CAACoC,gBAAgB,EAAE;QACvB,CAACpC,EAAE,GAAG,IAAI,CAACkJ,cAAc,MAAM,IAAI,IAAIlJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+C,IAAI,CAAC,IAAI,EAAElH,eAAe,CAAC;QAC9F,IAAI,CAACuG,gBAAgB,CAACoH,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAMtC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEK,oBAAoB;QAAEvG,MAAM;QAAElB,MAAM;QAAE5D;MAAa,CAAC,GAAG+K,IAAI;MACjE,IAAI,CAACM,oBAAoB,IAAI,CAACvG,MAAM,IAAI,CAAClB,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAKmH,IAAI,IACb,IAAI,CAACnH,MAAM,IACXA,MAAM,IACN0J,yBAAyB,CAAC,IAAI,CAACjN,OAAO,CAACkN,aAAa,EAAE,IAAI,CAAC3J,MAAM,CAACiF,SAAS,EAAEjF,MAAM,CAACiF,SAAS,CAAC,EAAE;QAChG/D,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI5G,SAAS,CAAC,CAAC;QACnC,MAAMsP,OAAO,GAAGzP,UAAU,CAAC,IAAI,CAAC6F,MAAM,CAACiF,SAAS,CAAC3H,CAAC,CAAC;QACnD4D,MAAM,CAAC5D,CAAC,CAACuM,GAAG,GAAG1C,IAAI,CAACjG,MAAM,CAAC5D,CAAC,CAACuM,GAAG;QAChC3I,MAAM,CAAC5D,CAAC,CAACwM,GAAG,GAAG5I,MAAM,CAAC5D,CAAC,CAACuM,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAG5P,UAAU,CAAC,IAAI,CAAC6F,MAAM,CAACiF,SAAS,CAAC1H,CAAC,CAAC;QACnD2D,MAAM,CAAC3D,CAAC,CAACsM,GAAG,GAAG1C,IAAI,CAACjG,MAAM,CAAC3D,CAAC,CAACsM,GAAG;QAChC3I,MAAM,CAAC3D,CAAC,CAACuM,GAAG,GAAG5I,MAAM,CAAC3D,CAAC,CAACsM,GAAG,GAAGE,OAAO;MACzC;MACApQ,WAAW,CAAC8N,oBAAoB,EAAEvG,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACYrH,YAAY,CAAC4N,oBAAoB,EAAErL,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYlC,YAAY,CAAC,IAAI,CAAC2N,4BAA4B,EAAE,IAAI,CAAC9C,eAAe,EAAE0C,oBAAoB,EAAErL,YAAY,CAAC;IAC7G;IACAoE,kBAAkBA,CAACT,QAAQ,EAAEN,IAAI,EAAE;MAC/B,IAAIG,EAAE,EAAEiB,EAAE,EAAEC,EAAE;MACd,IAAI,CAAC,IAAI,CAACxC,WAAW,CAACU,GAAG,CAACe,QAAQ,CAAC,EAAE;QACjC,IAAI,CAACzB,WAAW,CAACW,GAAG,CAACc,QAAQ,EAAE,IAAInF,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAMoP,KAAK,GAAG,IAAI,CAAC1L,WAAW,CAACY,GAAG,CAACa,QAAQ,CAAC;MAC5CiK,KAAK,CAAC7K,GAAG,CAACM,IAAI,CAAC;MACfA,IAAI,CAACwK,OAAO,CAAC;QACT3I,UAAU,EAAE,CAAC1B,EAAE,GAAGH,IAAI,CAAChD,OAAO,CAACyN,sBAAsB,MAAM,IAAI,IAAItK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,UAAU;QACzG6I,qBAAqB,EAAE,CAACrJ,EAAE,GAAG,CAACD,EAAE,GAAGpB,IAAI,CAAChD,OAAO,CAACyN,sBAAsB,MAAM,IAAI,IAAIrJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuJ,2BAA2B,MAAM,IAAI,IAAItJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,IAAI,CAAC9B,EAAE,EAAEpB,IAAI;MACtM,CAAC,CAAC;IACN;IACAgD,MAAMA,CAAA,EAAG;MACL,MAAMuH,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC;MAC7B,OAAOiH,KAAK,GAAGA,KAAK,CAAC7C,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MACN,IAAIxH,EAAE;MACN,MAAM;QAAEG;MAAS,CAAC,GAAG,IAAI,CAACtD,OAAO;MACjC,OAAOsD,QAAQ,GAAG,CAAC,CAACH,EAAE,GAAG,IAAI,CAACmD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH,IAAI,KAAK,IAAI,GAAG,IAAI;IAC1G;IACAkD,WAAWA,CAAA,EAAG;MACV,IAAIzK,EAAE;MACN,MAAM;QAAEG;MAAS,CAAC,GAAG,IAAI,CAACtD,OAAO;MACjC,OAAOsD,QAAQ,GAAG,CAACH,EAAE,GAAG,IAAI,CAACmD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0K,QAAQ,GAAGnJ,SAAS;IACzG;IACA4B,QAAQA,CAAA,EAAG;MACP,MAAM;QAAEhD;MAAS,CAAC,GAAG,IAAI,CAACtD,OAAO;MACjC,IAAIsD,QAAQ,EACR,OAAO,IAAI,CAACxB,IAAI,CAACD,WAAW,CAACY,GAAG,CAACa,QAAQ,CAAC;IAClD;IACAkK,OAAOA,CAAC;MAAE9M,UAAU;MAAEmE,UAAU;MAAE6I;IAAuB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7D,MAAMH,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC;MAC7B,IAAIiH,KAAK,EACLA,KAAK,CAACC,OAAO,CAAC,IAAI,EAAEE,qBAAqB,CAAC;MAC9C,IAAIhN,UAAU,EAAE;QACZ,IAAI,CAAC6H,eAAe,GAAG7D,SAAS;QAChC,IAAI,CAAChE,UAAU,GAAG,IAAI;MAC1B;MACA,IAAImE,UAAU,EACV,IAAI,CAAC2F,UAAU,CAAC;QAAE3F;MAAW,CAAC,CAAC;IACvC;IACAiJ,QAAQA,CAAA,EAAG;MACP,MAAMP,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC;MAC7B,IAAIiH,KAAK,EAAE;QACP,OAAOA,KAAK,CAACO,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACAlH,aAAaA,CAAA,EAAG;MACZ,MAAM;QAAEpD;MAAc,CAAC,GAAG,IAAI,CAACxD,OAAO;MACtC,IAAI,CAACwD,aAAa,EACd;MACJ;MACA,IAAIuK,SAAS,GAAG,KAAK;MACrB;AACZ;AACA;AACA;MACY,MAAM;QAAEpO;MAAa,CAAC,GAAG6D,aAAa;MACtC,IAAI7D,YAAY,CAACqO,MAAM,IACnBrO,YAAY,CAACsO,OAAO,IACpBtO,YAAY,CAACuO,OAAO,IACpBvO,YAAY,CAACwO,OAAO,EAAE;QACtBJ,SAAS,GAAG,IAAI;MACpB;MACA;MACA,IAAI,CAACA,SAAS,EACV;MACJ,MAAMK,WAAW,GAAG,CAAC,CAAC;MACtB;MACA,KAAK,IAAIlM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,aAAa,CAACoD,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,MAAMmM,GAAG,GAAG,QAAQ,GAAGtP,aAAa,CAACmD,CAAC,CAAC;QACvC;QACA,IAAIvC,YAAY,CAAC0O,GAAG,CAAC,EAAE;UACnBD,WAAW,CAACC,GAAG,CAAC,GAAG1O,YAAY,CAAC0O,GAAG,CAAC;UACpC7K,aAAa,CAAC8K,cAAc,CAACD,GAAG,EAAE,CAAC,CAAC;QACxC;MACJ;MACA;MACA;MACA7K,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACoE,MAAM,CAAC,CAAC;MACpF;MACA,KAAK,MAAMyG,GAAG,IAAID,WAAW,EAAE;QAC3B5K,aAAa,CAAC8K,cAAc,CAACD,GAAG,EAAED,WAAW,CAACC,GAAG,CAAC,CAAC;MACvD;MACA;MACA;MACA7K,aAAa,CAAC4F,cAAc,CAAC,CAAC;IAClC;IACAmF,mBAAmBA,CAACC,SAAS,GAAG,CAAC,CAAC,EAAE;MAChC,IAAIrL,EAAE,EAAEiB,EAAE,EAAEC,EAAE;MACd;MACA,MAAMoK,MAAM,GAAG,CAAC,CAAC;MACjB,IAAI,CAAC,IAAI,CAACvL,QAAQ,IAAI,IAAI,CAACzC,KAAK,EAC5B,OAAOgO,MAAM;MACjB,IAAI,CAAC,IAAI,CAAC9M,SAAS,EAAE;QACjB,OAAO;UAAE+M,UAAU,EAAE;QAAS,CAAC;MACnC,CAAC,MACI;QACDD,MAAM,CAACC,UAAU,GAAG,EAAE;MAC1B;MACA,MAAM3H,iBAAiB,GAAG,CAAC5D,EAAE,GAAG,IAAI,CAACnD,OAAO,CAACwD,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,QAAQ,CAAC,CAAC,CAAC6B,iBAAiB;MAChI,IAAI,IAAI,CAACrG,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB+N,MAAM,CAACE,OAAO,GAAG,EAAE;QACnBF,MAAM,CAACG,aAAa,GAChBjQ,kBAAkB,CAAC6P,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QACrDH,MAAM,CAACI,SAAS,GAAG9H,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAACpH,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAO8O,MAAM;MACjB;MACA,MAAM/D,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAACpC,eAAe,IAAI,CAAC,IAAI,CAAChF,MAAM,IAAI,CAACmH,IAAI,CAACjG,MAAM,EAAE;QACvD,MAAMqK,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC9O,OAAO,CAACsD,QAAQ,EAAE;UACvBwL,WAAW,CAACH,OAAO,GACf,IAAI,CAAChP,YAAY,CAACgP,OAAO,KAAKjK,SAAS,GACjC,IAAI,CAAC/E,YAAY,CAACgP,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrBjQ,kBAAkB,CAAC6P,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QACzD;QACA,IAAI,IAAI,CAAClN,YAAY,IAAI,CAACnD,YAAY,CAAC,IAAI,CAACoB,YAAY,CAAC,EAAE;UACvDmP,WAAW,CAACD,SAAS,GAAG9H,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAACrF,YAAY,GAAG,KAAK;QAC7B;QACA,OAAOoN,WAAW;MACtB;MACA,MAAMC,cAAc,GAAGrE,IAAI,CAACgC,eAAe,IAAIhC,IAAI,CAAC/K,YAAY;MAChE,IAAI,CAACqN,uBAAuB,CAAC,CAAC;MAC9ByB,MAAM,CAACI,SAAS,GAAGxQ,wBAAwB,CAAC,IAAI,CAAC+M,4BAA4B,EAAE,IAAI,CAACxK,SAAS,EAAEmO,cAAc,CAAC;MAC9G,IAAIhI,iBAAiB,EAAE;QACnB0H,MAAM,CAACI,SAAS,GAAG9H,iBAAiB,CAACgI,cAAc,EAAEN,MAAM,CAACI,SAAS,CAAC;MAC1E;MACA,MAAM;QAAEhO,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAACyH,eAAe;MACrCkG,MAAM,CAACO,eAAe,GAAG,GAAGnO,CAAC,CAACoO,MAAM,GAAG,GAAG,KAAKnO,CAAC,CAACmO,MAAM,GAAG,GAAG,KAAK;MAClE,IAAIvE,IAAI,CAACgC,eAAe,EAAE;QACtB;AAChB;AACA;AACA;QACgB+B,MAAM,CAACE,OAAO,GACVjE,IAAI,KAAK,IAAI,GACP,CAACrG,EAAE,GAAG,CAACD,EAAE,GAAG2K,cAAc,CAACJ,OAAO,MAAM,IAAI,IAAIvK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACzE,YAAY,CAACgP,OAAO,MAAM,IAAI,IAAItK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,GAClI,IAAI,CAACyI,eAAe,GAChB,IAAI,CAACnN,YAAY,CAACgP,OAAO,GACzBI,cAAc,CAACG,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBT,MAAM,CAACE,OAAO,GACVjE,IAAI,KAAK,IAAI,GACPqE,cAAc,CAACJ,OAAO,KAAKjK,SAAS,GAChCqK,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACG,WAAW,KAAKxK,SAAS,GACpCqK,cAAc,CAACG,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAMb,GAAG,IAAIjQ,eAAe,EAAE;QAC/B,IAAI2Q,cAAc,CAACV,GAAG,CAAC,KAAK3J,SAAS,EACjC;QACJ,MAAM;UAAEyK,OAAO;UAAEC;QAAQ,CAAC,GAAGhR,eAAe,CAACiQ,GAAG,CAAC;QACjD,MAAMgB,SAAS,GAAGF,OAAO,CAACJ,cAAc,CAACV,GAAG,CAAC,EAAE3D,IAAI,CAAC;QACpD,IAAI0E,OAAO,EAAE;UACT,MAAME,GAAG,GAAGF,OAAO,CAACjN,MAAM;UAC1B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoN,GAAG,EAAEpN,CAAC,EAAE,EAAE;YAC1BuM,MAAM,CAACW,OAAO,CAAClN,CAAC,CAAC,CAAC,GAAGmN,SAAS;UAClC;QACJ,CAAC,MACI;UACDZ,MAAM,CAACJ,GAAG,CAAC,GAAGgB,SAAS;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACrP,OAAO,CAACsD,QAAQ,EAAE;QACvBmL,MAAM,CAACG,aAAa,GAChBlE,IAAI,KAAK,IAAI,GACP/L,kBAAkB,CAAC6P,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE,GACjD,MAAM;MACpB;MACA,OAAOH,MAAM;IACjB;IACA5G,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACvC,UAAU,GAAG,IAAI,CAAC4C,QAAQ,GAAGxD,SAAS;IAC/C;IACA;IACA6K,SAASA,CAAA,EAAG;MACR,IAAI,CAACzN,IAAI,CAACT,KAAK,CAACC,OAAO,CAAE0B,IAAI,IAAK;QAAE,IAAIG,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGH,IAAI,CAACuC,gBAAgB,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwJ,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MAClI,IAAI,CAAC7K,IAAI,CAACT,KAAK,CAACC,OAAO,CAAC8F,iBAAiB,CAAC;MAC1C,IAAI,CAACtF,IAAI,CAACD,WAAW,CAAC0F,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAASE,YAAYA,CAACzE,IAAI,EAAE;EACxBA,IAAI,CAACyE,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAAC1E,IAAI,EAAE;EAC9B,IAAIG,EAAE,EAAEiB,EAAE,EAAEC,EAAE;EACd,MAAM6D,QAAQ,GAAG,CAAC,CAAC/E,EAAE,GAAGH,IAAI,CAACsC,UAAU,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+E,QAAQ,KAAKlF,IAAI,CAACkF,QAAQ;EAC3G,IAAIlF,IAAI,CAACgD,MAAM,CAAC,CAAC,IACbhD,IAAI,CAACO,MAAM,IACX2E,QAAQ,IACRlF,IAAI,CAACD,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAEyF,SAAS,EAAEjF,MAAM;MAAEmG,WAAW,EAAE8F;IAAe,CAAC,GAAGxM,IAAI,CAACO,MAAM;IACtE,MAAM;MAAE2J;IAAc,CAAC,GAAGlK,IAAI,CAAChD,OAAO;IACtC,MAAMiL,QAAQ,GAAG/C,QAAQ,CAACyB,MAAM,KAAK3G,IAAI,CAACO,MAAM,CAACoG,MAAM;IACvD;IACA;IACA,IAAIuD,aAAa,KAAK,MAAM,EAAE;MAC1B5O,QAAQ,CAAEmR,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGzE,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAAC+F,IAAI,CAAC,GAC1BvH,QAAQ,CAACM,SAAS,CAACiH,IAAI,CAAC;QAC9B,MAAMtN,MAAM,GAAGzE,UAAU,CAACgS,YAAY,CAAC;QACvCA,YAAY,CAACtC,GAAG,GAAG7J,MAAM,CAACkM,IAAI,CAAC,CAACrC,GAAG;QACnCsC,YAAY,CAACrC,GAAG,GAAGqC,YAAY,CAACtC,GAAG,GAAGjL,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAI8K,yBAAyB,CAACC,aAAa,EAAEhF,QAAQ,CAACM,SAAS,EAAEjF,MAAM,CAAC,EAAE;MAC3EjF,QAAQ,CAAEmR,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGzE,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAAC+F,IAAI,CAAC,GAC1BvH,QAAQ,CAACM,SAAS,CAACiH,IAAI,CAAC;QAC9B,MAAMtN,MAAM,GAAGzE,UAAU,CAAC6F,MAAM,CAACkM,IAAI,CAAC,CAAC;QACvCC,YAAY,CAACrC,GAAG,GAAGqC,YAAY,CAACtC,GAAG,GAAGjL,MAAM;MAChD,CAAC,CAAC;IACN;IACA,MAAMwN,WAAW,GAAG7R,WAAW,CAAC,CAAC;IACjCL,YAAY,CAACkS,WAAW,EAAEpM,MAAM,EAAE2E,QAAQ,CAACM,SAAS,CAAC;IACrD,MAAMoH,WAAW,GAAG9R,WAAW,CAAC,CAAC;IACjC,IAAImN,QAAQ,EAAE;MACVxN,YAAY,CAACmS,WAAW,EAAE5M,IAAI,CAACgH,cAAc,CAACwF,cAAc,EAAE,IAAI,CAAC,EAAEtH,QAAQ,CAACwB,WAAW,CAAC;IAC9F,CAAC,MACI;MACDjM,YAAY,CAACmS,WAAW,EAAErM,MAAM,EAAE2E,QAAQ,CAACM,SAAS,CAAC;IACzD;IACA,MAAMvE,gBAAgB,GAAG,CAAChG,WAAW,CAAC0R,WAAW,CAAC;IAClD,IAAIzL,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAAClB,IAAI,CAACsC,UAAU,EAAE;MAClB,MAAMuF,cAAc,GAAG7H,IAAI,CAAC8H,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAID,cAAc,IAAI,CAACA,cAAc,CAACvF,UAAU,EAAE;QAC9C,MAAM;UAAE4C,QAAQ,EAAE2H,cAAc;UAAEtM,MAAM,EAAEuM;QAAa,CAAC,GAAGjF,cAAc;QACzE,IAAIgF,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAGlS,SAAS,CAAC,CAAC;UACpCN,oBAAoB,CAACwS,gBAAgB,EAAE7H,QAAQ,CAACM,SAAS,EAAEqH,cAAc,CAACrH,SAAS,CAAC;UACpF,MAAMsD,cAAc,GAAGjO,SAAS,CAAC,CAAC;UAClCN,oBAAoB,CAACuO,cAAc,EAAEvI,MAAM,EAAEuM,YAAY,CAACtH,SAAS,CAAC;UACpE,IAAI,CAACxK,SAAS,CAAC+R,gBAAgB,EAAEjE,cAAc,CAAC,EAAE;YAC9C5H,wBAAwB,GAAG,IAAI;UACnC;QACJ;MACJ;IACJ;IACAlB,IAAI,CAACL,eAAe,CAAC,WAAW,EAAE;MAC9BY,MAAM;MACN2E,QAAQ;MACRlE,KAAK,EAAE4L,WAAW;MAClBD,WAAW;MACX1L,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAIlB,IAAI,CAACgD,MAAM,CAAC,CAAC,EAAE;IACpB,CAAC3B,EAAE,GAAG,CAACD,EAAE,GAAGpB,IAAI,CAAChD,OAAO,EAAEiG,cAAc,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,IAAI,CAAC9B,EAAE,CAAC;EAC9F;EACA;AACJ;AACA;AACA;AACA;EACIpB,IAAI,CAAChD,OAAO,CAAC6E,UAAU,GAAGH,SAAS;AACvC;AACA,SAASnD,mBAAmBA,CAACyB,IAAI,EAAE;EAC/B;AACJ;AACA;AACA;EACIA,IAAI,CAAC3C,iBAAiB,KAAK2C,IAAI,CAAC3C,iBAAiB,GAAGsI,OAAO,CAAC3F,IAAI,CAACpD,MAAM,IAAIoD,IAAI,CAACpD,MAAM,CAACS,iBAAiB,CAAC,CAAC;EAC1G;AACJ;AACA;EACI2C,IAAI,CAAC5C,gBAAgB,KAAK4C,IAAI,CAAC5C,gBAAgB,GAAGuI,OAAO,CAAC3F,IAAI,CAACpD,MAAM,IAAIoD,IAAI,CAACpD,MAAM,CAACQ,gBAAgB,CAAC,CAAC;AAC3G;AACA,SAASyH,aAAaA,CAAC7E,IAAI,EAAE;EACzBA,IAAI,CAAC6E,aAAa,CAAC,CAAC;AACxB;AACA,SAAST,iBAAiBA,CAACpE,IAAI,EAAE;EAC7BA,IAAI,CAACoE,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASI,mBAAmBA,CAACxE,IAAI,EAAE;EAC/B,MAAM;IAAEQ;EAAc,CAAC,GAAGR,IAAI,CAAChD,OAAO;EACtC,IAAIwD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC0B,QAAQ,CAAC,CAAC,CAAC8K,qBAAqB,EAAE;IAC9GxM,aAAa,CAACV,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACAE,IAAI,CAACzD,cAAc,CAAC,CAAC;AACzB;AACA,SAASuE,eAAeA,CAACd,IAAI,EAAE;EAC3BA,IAAI,CAACc,eAAe,CAAC,CAAC;EACtBd,IAAI,CAACuH,WAAW,GAAGvH,IAAI,CAAC2B,cAAc,GAAG3B,IAAI,CAACyB,MAAM,GAAGC,SAAS;AACpE;AACA,SAASlD,kBAAkBA,CAACwB,IAAI,EAAE;EAC9BA,IAAI,CAACxB,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAACuB,IAAI,EAAE;EAC1BA,IAAI,CAACvB,cAAc,CAAC,CAAC;AACzB;AACA,SAASmF,aAAaA,CAAC5D,IAAI,EAAE;EACzBA,IAAI,CAAC4D,aAAa,CAAC,CAAC;AACxB;AACA,SAASkB,mBAAmBA,CAACyF,KAAK,EAAE;EAChCA,KAAK,CAAC0C,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAASzD,YAAYA,CAAC0D,MAAM,EAAElM,KAAK,EAAEmM,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAGtR,GAAG,CAACkF,KAAK,CAACoM,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EAC7CD,MAAM,CAACG,KAAK,GAAGvR,GAAG,CAACkF,KAAK,CAACqM,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EACrCD,MAAM,CAACjB,MAAM,GAAGjL,KAAK,CAACiL,MAAM;EAC5BiB,MAAM,CAACI,WAAW,GAAGtM,KAAK,CAACsM,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAAC9C,GAAG,GAAGtO,GAAG,CAAC0R,IAAI,CAACpD,GAAG,EAAEqD,EAAE,CAACrD,GAAG,EAAE+C,CAAC,CAAC;EACrCD,MAAM,CAAC7C,GAAG,GAAGvO,GAAG,CAAC0R,IAAI,CAACnD,GAAG,EAAEoD,EAAE,CAACpD,GAAG,EAAE8C,CAAC,CAAC;AACzC;AACA,SAAS1D,MAAMA,CAACyD,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAACrP,CAAC,EAAE2P,IAAI,CAAC3P,CAAC,EAAE4P,EAAE,CAAC5P,CAAC,EAAEsP,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAACpP,CAAC,EAAE0P,IAAI,CAAC1P,CAAC,EAAE2P,EAAE,CAAC3P,CAAC,EAAEqP,CAAC,CAAC;AACtC;AACA,SAAS/D,mBAAmBA,CAACpJ,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAAC0J,eAAe,IAAI1J,IAAI,CAAC0J,eAAe,CAACwC,WAAW,KAAKxK,SAAS;AAClF;AACA,MAAMK,uBAAuB,GAAG;EAC5B2L,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,SAASrJ,cAAcA,CAACtE,IAAI,EAAEtD,SAAS,EAAE;EACrC;AACJ;AACA;AACA;EACI,IAAIkR,UAAU,GAAG5N,IAAI,CAAClB,IAAI;EAC1B,KAAK,IAAII,CAAC,GAAGc,IAAI,CAACjB,IAAI,CAACI,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC5C,IAAIyG,OAAO,CAAC3F,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC,CAACgB,QAAQ,CAAC,EAAE;MAChC0N,UAAU,GAAG5N,IAAI,CAACjB,IAAI,CAACG,CAAC,CAAC;MACzB;IACJ;EACJ;EACA,MAAM2O,aAAa,GAAGD,UAAU,IAAIA,UAAU,KAAK5N,IAAI,CAAClB,IAAI,GAAG8O,UAAU,CAAC1N,QAAQ,GAAG4N,QAAQ;EAC7F,MAAMC,OAAO,GAAGF,aAAa,CAACG,aAAa,CAAC,wBAAwBtR,SAAS,IAAI,CAAC;EAClF,IAAIqR,OAAO,EACP/N,IAAI,CAACC,KAAK,CAAC8N,OAAO,EAAE,IAAI,CAAC;AACjC;AACA,SAASE,SAASA,CAACxB,IAAI,EAAE;EACrBA,IAAI,CAACrC,GAAG,GAAG8D,IAAI,CAACC,KAAK,CAAC1B,IAAI,CAACrC,GAAG,CAAC;EAC/BqC,IAAI,CAACpC,GAAG,GAAG6D,IAAI,CAACC,KAAK,CAAC1B,IAAI,CAACpC,GAAG,CAAC;AACnC;AACA,SAAS5D,QAAQA,CAACG,GAAG,EAAE;EACnBqH,SAAS,CAACrH,GAAG,CAAC/I,CAAC,CAAC;EAChBoQ,SAAS,CAACrH,GAAG,CAAC9I,CAAC,CAAC;AACpB;AACA,SAASmM,yBAAyBA,CAACC,aAAa,EAAEhF,QAAQ,EAAE3E,MAAM,EAAE;EAChE,OAAQ2J,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAACvP,MAAM,CAACO,WAAW,CAACgK,QAAQ,CAAC,EAAEhK,WAAW,CAACqF,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AAEA,SAASrE,oBAAoB,EAAEqR,OAAO,EAAE/D,YAAY,EAAEC,MAAM,EAAElL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}