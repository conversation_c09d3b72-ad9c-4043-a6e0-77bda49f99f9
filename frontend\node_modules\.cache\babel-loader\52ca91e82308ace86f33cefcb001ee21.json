{"ast": null, "code": "'use strict';\n\nvar data = require('./data.cjs.js');\nvar cssVar = require('./utils/css-var.cjs.js');\nvar utils = require('@motionone/utils');\nvar transforms = require('./utils/transforms.cjs.js');\nvar easing = require('./utils/easing.cjs.js');\nvar featureDetection = require('./utils/feature-detection.cjs.js');\nvar keyframes = require('./utils/keyframes.cjs.js');\nvar style = require('./style.cjs.js');\nvar getStyleName = require('./utils/get-style-name.cjs.js');\nvar stopAnimation = require('./utils/stop-animation.cjs.js');\nvar getUnit = require('./utils/get-unit.cjs.js');\nfunction getDevToolsRecord() {\n  return window.__MOTION_DEV_TOOLS_RECORD;\n}\nfunction animateStyle(element, key, keyframesDefinition, options = {}, AnimationPolyfill) {\n  const record = getDevToolsRecord();\n  const isRecording = options.record !== false && record;\n  let animation;\n  let {\n    duration = utils.defaults.duration,\n    delay = utils.defaults.delay,\n    endDelay = utils.defaults.endDelay,\n    repeat = utils.defaults.repeat,\n    easing: easing$1 = utils.defaults.easing,\n    persist = false,\n    direction,\n    offset,\n    allowWebkitAcceleration = false,\n    autoplay = true\n  } = options;\n  const data$1 = data.getAnimationData(element);\n  const valueIsTransform = transforms.isTransform(key);\n  let canAnimateNatively = featureDetection.supports.waapi();\n  /**\n   * If this is an individual transform, we need to map its\n   * key to a CSS variable and update the element's transform style\n   */\n  valueIsTransform && transforms.addTransformToElement(element, key);\n  const name = getStyleName.getStyleName(key);\n  const motionValue = data.getMotionValue(data$1.values, name);\n  /**\n   * Get definition of value, this will be used to convert numerical\n   * keyframes into the default value type.\n   */\n  const definition = transforms.transformDefinitions.get(name);\n  /**\n   * Stop the current animation, if any. Because this will trigger\n   * commitStyles (DOM writes) and we might later trigger DOM reads,\n   * this is fired now and we return a factory function to create\n   * the actual animation that can get called in batch,\n   */\n  stopAnimation.stopAnimation(motionValue.animation, !(utils.isEasingGenerator(easing$1) && motionValue.generator) && options.record !== false);\n  /**\n   * Batchable factory function containing all DOM reads.\n   */\n  return () => {\n    const readInitialValue = () => {\n      var _a, _b;\n      return (_b = (_a = style.style.get(element, name)) !== null && _a !== void 0 ? _a : definition === null || definition === void 0 ? void 0 : definition.initialValue) !== null && _b !== void 0 ? _b : 0;\n    };\n    /**\n     * Replace null values with the previous keyframe value, or read\n     * it from the DOM if it's the first keyframe.\n     */\n    let keyframes$1 = keyframes.hydrateKeyframes(keyframes.keyframesList(keyframesDefinition), readInitialValue);\n    /**\n     * Detect unit type of keyframes.\n     */\n    const toUnit = getUnit.getUnitConverter(keyframes$1, definition);\n    if (utils.isEasingGenerator(easing$1)) {\n      const custom = easing$1.createAnimation(keyframes$1, key !== \"opacity\", readInitialValue, name, motionValue);\n      easing$1 = custom.easing;\n      keyframes$1 = custom.keyframes || keyframes$1;\n      duration = custom.duration || duration;\n    }\n    /**\n     * If this is a CSS variable we need to register it with the browser\n     * before it can be animated natively. We also set it with setProperty\n     * rather than directly onto the element.style object.\n     */\n    if (cssVar.isCssVar(name)) {\n      if (featureDetection.supports.cssRegisterProperty()) {\n        cssVar.registerCssVariable(name);\n      } else {\n        canAnimateNatively = false;\n      }\n    }\n    /**\n     * If we've been passed a custom easing function, and this browser\n     * does **not** support linear() easing, and the value is a transform\n     * (and thus a pure number) we can still support the custom easing\n     * by falling back to the animation polyfill.\n     */\n    if (valueIsTransform && !featureDetection.supports.linearEasing() && (utils.isFunction(easing$1) || utils.isEasingList(easing$1) && easing$1.some(utils.isFunction))) {\n      canAnimateNatively = false;\n    }\n    /**\n     * If we can animate this value with WAAPI, do so.\n     */\n    if (canAnimateNatively) {\n      /**\n       * Convert numbers to default value types. Currently this only supports\n       * transforms but it could also support other value types.\n       */\n      if (definition) {\n        keyframes$1 = keyframes$1.map(value => utils.isNumber(value) ? definition.toDefaultUnit(value) : value);\n      }\n      /**\n       * If this browser doesn't support partial/implicit keyframes we need to\n       * explicitly provide one.\n       */\n      if (keyframes$1.length === 1 && (!featureDetection.supports.partialKeyframes() || isRecording)) {\n        keyframes$1.unshift(readInitialValue());\n      }\n      const animationOptions = {\n        delay: utils.time.ms(delay),\n        duration: utils.time.ms(duration),\n        endDelay: utils.time.ms(endDelay),\n        easing: !utils.isEasingList(easing$1) ? easing.convertEasing(easing$1, duration) : undefined,\n        direction,\n        iterations: repeat + 1,\n        fill: \"both\"\n      };\n      animation = element.animate({\n        [name]: keyframes$1,\n        offset,\n        easing: utils.isEasingList(easing$1) ? easing$1.map(thisEasing => easing.convertEasing(thisEasing, duration)) : undefined\n      }, animationOptions);\n      /**\n       * Polyfill finished Promise in browsers that don't support it\n       */\n      if (!animation.finished) {\n        animation.finished = new Promise((resolve, reject) => {\n          animation.onfinish = resolve;\n          animation.oncancel = reject;\n        });\n      }\n      const target = keyframes$1[keyframes$1.length - 1];\n      animation.finished.then(() => {\n        if (persist) return;\n        // Apply styles to target\n        style.style.set(element, name, target);\n        // Ensure fill modes don't persist\n        animation.cancel();\n      }).catch(utils.noop);\n      /**\n       * This forces Webkit to run animations on the main thread by exploiting\n       * this condition:\n       * https://trac.webkit.org/browser/webkit/trunk/Source/WebCore/platform/graphics/ca/GraphicsLayerCA.cpp?rev=281238#L1099\n       *\n       * This fixes Webkit's timing bugs, like accelerated animations falling\n       * out of sync with main thread animations and massive delays in starting\n       * accelerated animations in WKWebView.\n       */\n      if (!allowWebkitAcceleration) animation.playbackRate = 1.000001;\n      /**\n       * If we can't animate the value natively then we can fallback to the numbers-only\n       * polyfill for transforms.\n       */\n    } else if (AnimationPolyfill && valueIsTransform) {\n      /**\n       * If any keyframe is a string (because we measured it from the DOM), we need to convert\n       * it into a number before passing to the Animation polyfill.\n       */\n      keyframes$1 = keyframes$1.map(value => typeof value === \"string\" ? parseFloat(value) : value);\n      /**\n       * If we only have a single keyframe, we need to create an initial keyframe by reading\n       * the current value from the DOM.\n       */\n      if (keyframes$1.length === 1) {\n        keyframes$1.unshift(parseFloat(readInitialValue()));\n      }\n      animation = new AnimationPolyfill(latest => {\n        style.style.set(element, name, toUnit ? toUnit(latest) : latest);\n      }, keyframes$1, Object.assign(Object.assign({}, options), {\n        duration,\n        easing: easing$1\n      }));\n    } else {\n      const target = keyframes$1[keyframes$1.length - 1];\n      style.style.set(element, name, definition && utils.isNumber(target) ? definition.toDefaultUnit(target) : target);\n    }\n    if (isRecording) {\n      record(element, key, keyframes$1, {\n        duration,\n        delay: delay,\n        easing: easing$1,\n        repeat,\n        offset\n      }, \"motion-one\");\n    }\n    motionValue.setAnimation(animation);\n    if (animation && !autoplay) animation.pause();\n    return animation;\n  };\n}\nexports.animateStyle = animateStyle;", "map": {"version": 3, "names": ["data", "require", "cssVar", "utils", "transforms", "easing", "featureDetection", "keyframes", "style", "getStyleName", "stopAnimation", "getUnit", "getDevToolsRecord", "window", "__MOTION_DEV_TOOLS_RECORD", "animateStyle", "element", "key", "keyframesDefinition", "options", "AnimationPolyfill", "record", "isRecording", "animation", "duration", "defaults", "delay", "endDelay", "repeat", "easing$1", "persist", "direction", "offset", "allowWebkitAcceleration", "autoplay", "data$1", "getAnimationData", "valueIsTransform", "isTransform", "canAnimateNatively", "supports", "waapi", "addTransformToElement", "name", "motionValue", "getMotionValue", "values", "definition", "transformDefinitions", "get", "isEasingGenerator", "generator", "readInitialValue", "_a", "_b", "initialValue", "keyframes$1", "hydrateKeyframes", "keyframesList", "toUnit", "getUnitConverter", "custom", "createAnimation", "isCssVar", "cssRegisterProperty", "registerCssVariable", "linearEasing", "isFunction", "isEasingList", "some", "map", "value", "isNumber", "toDefaultUnit", "length", "partialKeyframes", "unshift", "animationOptions", "time", "ms", "convertEasing", "undefined", "iterations", "fill", "animate", "thisEasing", "finished", "Promise", "resolve", "reject", "onfinish", "oncancel", "target", "then", "set", "cancel", "catch", "noop", "playbackRate", "parseFloat", "latest", "Object", "assign", "setAnimation", "pause", "exports"], "sources": ["D:/Projects/cable_project/customer-management/frontend/node_modules/@motionone/dom/dist/animate/animate-style.cjs.js"], "sourcesContent": ["'use strict';\n\nvar data = require('./data.cjs.js');\nvar cssVar = require('./utils/css-var.cjs.js');\nvar utils = require('@motionone/utils');\nvar transforms = require('./utils/transforms.cjs.js');\nvar easing = require('./utils/easing.cjs.js');\nvar featureDetection = require('./utils/feature-detection.cjs.js');\nvar keyframes = require('./utils/keyframes.cjs.js');\nvar style = require('./style.cjs.js');\nvar getStyleName = require('./utils/get-style-name.cjs.js');\nvar stopAnimation = require('./utils/stop-animation.cjs.js');\nvar getUnit = require('./utils/get-unit.cjs.js');\n\nfunction getDevToolsRecord() {\n    return window.__MOTION_DEV_TOOLS_RECORD;\n}\nfunction animateStyle(element, key, keyframesDefinition, options = {}, AnimationPolyfill) {\n    const record = getDevToolsRecord();\n    const isRecording = options.record !== false && record;\n    let animation;\n    let { duration = utils.defaults.duration, delay = utils.defaults.delay, endDelay = utils.defaults.endDelay, repeat = utils.defaults.repeat, easing: easing$1 = utils.defaults.easing, persist = false, direction, offset, allowWebkitAcceleration = false, autoplay = true, } = options;\n    const data$1 = data.getAnimationData(element);\n    const valueIsTransform = transforms.isTransform(key);\n    let canAnimateNatively = featureDetection.supports.waapi();\n    /**\n     * If this is an individual transform, we need to map its\n     * key to a CSS variable and update the element's transform style\n     */\n    valueIsTransform && transforms.addTransformToElement(element, key);\n    const name = getStyleName.getStyleName(key);\n    const motionValue = data.getMotionValue(data$1.values, name);\n    /**\n     * Get definition of value, this will be used to convert numerical\n     * keyframes into the default value type.\n     */\n    const definition = transforms.transformDefinitions.get(name);\n    /**\n     * Stop the current animation, if any. Because this will trigger\n     * commitStyles (DOM writes) and we might later trigger DOM reads,\n     * this is fired now and we return a factory function to create\n     * the actual animation that can get called in batch,\n     */\n    stopAnimation.stopAnimation(motionValue.animation, !(utils.isEasingGenerator(easing$1) && motionValue.generator) &&\n        options.record !== false);\n    /**\n     * Batchable factory function containing all DOM reads.\n     */\n    return () => {\n        const readInitialValue = () => { var _a, _b; return (_b = (_a = style.style.get(element, name)) !== null && _a !== void 0 ? _a : definition === null || definition === void 0 ? void 0 : definition.initialValue) !== null && _b !== void 0 ? _b : 0; };\n        /**\n         * Replace null values with the previous keyframe value, or read\n         * it from the DOM if it's the first keyframe.\n         */\n        let keyframes$1 = keyframes.hydrateKeyframes(keyframes.keyframesList(keyframesDefinition), readInitialValue);\n        /**\n         * Detect unit type of keyframes.\n         */\n        const toUnit = getUnit.getUnitConverter(keyframes$1, definition);\n        if (utils.isEasingGenerator(easing$1)) {\n            const custom = easing$1.createAnimation(keyframes$1, key !== \"opacity\", readInitialValue, name, motionValue);\n            easing$1 = custom.easing;\n            keyframes$1 = custom.keyframes || keyframes$1;\n            duration = custom.duration || duration;\n        }\n        /**\n         * If this is a CSS variable we need to register it with the browser\n         * before it can be animated natively. We also set it with setProperty\n         * rather than directly onto the element.style object.\n         */\n        if (cssVar.isCssVar(name)) {\n            if (featureDetection.supports.cssRegisterProperty()) {\n                cssVar.registerCssVariable(name);\n            }\n            else {\n                canAnimateNatively = false;\n            }\n        }\n        /**\n         * If we've been passed a custom easing function, and this browser\n         * does **not** support linear() easing, and the value is a transform\n         * (and thus a pure number) we can still support the custom easing\n         * by falling back to the animation polyfill.\n         */\n        if (valueIsTransform &&\n            !featureDetection.supports.linearEasing() &&\n            (utils.isFunction(easing$1) || (utils.isEasingList(easing$1) && easing$1.some(utils.isFunction)))) {\n            canAnimateNatively = false;\n        }\n        /**\n         * If we can animate this value with WAAPI, do so.\n         */\n        if (canAnimateNatively) {\n            /**\n             * Convert numbers to default value types. Currently this only supports\n             * transforms but it could also support other value types.\n             */\n            if (definition) {\n                keyframes$1 = keyframes$1.map((value) => utils.isNumber(value) ? definition.toDefaultUnit(value) : value);\n            }\n            /**\n             * If this browser doesn't support partial/implicit keyframes we need to\n             * explicitly provide one.\n             */\n            if (keyframes$1.length === 1 &&\n                (!featureDetection.supports.partialKeyframes() || isRecording)) {\n                keyframes$1.unshift(readInitialValue());\n            }\n            const animationOptions = {\n                delay: utils.time.ms(delay),\n                duration: utils.time.ms(duration),\n                endDelay: utils.time.ms(endDelay),\n                easing: !utils.isEasingList(easing$1)\n                    ? easing.convertEasing(easing$1, duration)\n                    : undefined,\n                direction,\n                iterations: repeat + 1,\n                fill: \"both\",\n            };\n            animation = element.animate({\n                [name]: keyframes$1,\n                offset,\n                easing: utils.isEasingList(easing$1)\n                    ? easing$1.map((thisEasing) => easing.convertEasing(thisEasing, duration))\n                    : undefined,\n            }, animationOptions);\n            /**\n             * Polyfill finished Promise in browsers that don't support it\n             */\n            if (!animation.finished) {\n                animation.finished = new Promise((resolve, reject) => {\n                    animation.onfinish = resolve;\n                    animation.oncancel = reject;\n                });\n            }\n            const target = keyframes$1[keyframes$1.length - 1];\n            animation.finished\n                .then(() => {\n                if (persist)\n                    return;\n                // Apply styles to target\n                style.style.set(element, name, target);\n                // Ensure fill modes don't persist\n                animation.cancel();\n            })\n                .catch(utils.noop);\n            /**\n             * This forces Webkit to run animations on the main thread by exploiting\n             * this condition:\n             * https://trac.webkit.org/browser/webkit/trunk/Source/WebCore/platform/graphics/ca/GraphicsLayerCA.cpp?rev=281238#L1099\n             *\n             * This fixes Webkit's timing bugs, like accelerated animations falling\n             * out of sync with main thread animations and massive delays in starting\n             * accelerated animations in WKWebView.\n             */\n            if (!allowWebkitAcceleration)\n                animation.playbackRate = 1.000001;\n            /**\n             * If we can't animate the value natively then we can fallback to the numbers-only\n             * polyfill for transforms.\n             */\n        }\n        else if (AnimationPolyfill && valueIsTransform) {\n            /**\n             * If any keyframe is a string (because we measured it from the DOM), we need to convert\n             * it into a number before passing to the Animation polyfill.\n             */\n            keyframes$1 = keyframes$1.map((value) => typeof value === \"string\" ? parseFloat(value) : value);\n            /**\n             * If we only have a single keyframe, we need to create an initial keyframe by reading\n             * the current value from the DOM.\n             */\n            if (keyframes$1.length === 1) {\n                keyframes$1.unshift(parseFloat(readInitialValue()));\n            }\n            animation = new AnimationPolyfill((latest) => {\n                style.style.set(element, name, toUnit ? toUnit(latest) : latest);\n            }, keyframes$1, Object.assign(Object.assign({}, options), { duration,\n                easing: easing$1 }));\n        }\n        else {\n            const target = keyframes$1[keyframes$1.length - 1];\n            style.style.set(element, name, definition && utils.isNumber(target)\n                ? definition.toDefaultUnit(target)\n                : target);\n        }\n        if (isRecording) {\n            record(element, key, keyframes$1, {\n                duration,\n                delay: delay,\n                easing: easing$1,\n                repeat,\n                offset,\n            }, \"motion-one\");\n        }\n        motionValue.setAnimation(animation);\n        if (animation && !autoplay)\n            animation.pause();\n        return animation;\n    };\n}\n\nexports.animateStyle = animateStyle;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;AACnC,IAAIC,MAAM,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAC9C,IAAIE,KAAK,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIG,UAAU,GAAGH,OAAO,CAAC,2BAA2B,CAAC;AACrD,IAAII,MAAM,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AAC7C,IAAIK,gBAAgB,GAAGL,OAAO,CAAC,kCAAkC,CAAC;AAClE,IAAIM,SAAS,GAAGN,OAAO,CAAC,0BAA0B,CAAC;AACnD,IAAIO,KAAK,GAAGP,OAAO,CAAC,gBAAgB,CAAC;AACrC,IAAIQ,YAAY,GAAGR,OAAO,CAAC,+BAA+B,CAAC;AAC3D,IAAIS,aAAa,GAAGT,OAAO,CAAC,+BAA+B,CAAC;AAC5D,IAAIU,OAAO,GAAGV,OAAO,CAAC,yBAAyB,CAAC;AAEhD,SAASW,iBAAiBA,CAAA,EAAG;EACzB,OAAOC,MAAM,CAACC,yBAAyB;AAC3C;AACA,SAASC,YAAYA,CAACC,OAAO,EAAEC,GAAG,EAAEC,mBAAmB,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,iBAAiB,EAAE;EACtF,MAAMC,MAAM,GAAGT,iBAAiB,CAAC,CAAC;EAClC,MAAMU,WAAW,GAAGH,OAAO,CAACE,MAAM,KAAK,KAAK,IAAIA,MAAM;EACtD,IAAIE,SAAS;EACb,IAAI;IAAEC,QAAQ,GAAGrB,KAAK,CAACsB,QAAQ,CAACD,QAAQ;IAAEE,KAAK,GAAGvB,KAAK,CAACsB,QAAQ,CAACC,KAAK;IAAEC,QAAQ,GAAGxB,KAAK,CAACsB,QAAQ,CAACE,QAAQ;IAAEC,MAAM,GAAGzB,KAAK,CAACsB,QAAQ,CAACG,MAAM;IAAEvB,MAAM,EAAEwB,QAAQ,GAAG1B,KAAK,CAACsB,QAAQ,CAACpB,MAAM;IAAEyB,OAAO,GAAG,KAAK;IAAEC,SAAS;IAAEC,MAAM;IAAEC,uBAAuB,GAAG,KAAK;IAAEC,QAAQ,GAAG;EAAM,CAAC,GAAGf,OAAO;EACvR,MAAMgB,MAAM,GAAGnC,IAAI,CAACoC,gBAAgB,CAACpB,OAAO,CAAC;EAC7C,MAAMqB,gBAAgB,GAAGjC,UAAU,CAACkC,WAAW,CAACrB,GAAG,CAAC;EACpD,IAAIsB,kBAAkB,GAAGjC,gBAAgB,CAACkC,QAAQ,CAACC,KAAK,CAAC,CAAC;EAC1D;AACJ;AACA;AACA;EACIJ,gBAAgB,IAAIjC,UAAU,CAACsC,qBAAqB,CAAC1B,OAAO,EAAEC,GAAG,CAAC;EAClE,MAAM0B,IAAI,GAAGlC,YAAY,CAACA,YAAY,CAACQ,GAAG,CAAC;EAC3C,MAAM2B,WAAW,GAAG5C,IAAI,CAAC6C,cAAc,CAACV,MAAM,CAACW,MAAM,EAAEH,IAAI,CAAC;EAC5D;AACJ;AACA;AACA;EACI,MAAMI,UAAU,GAAG3C,UAAU,CAAC4C,oBAAoB,CAACC,GAAG,CAACN,IAAI,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA;EACIjC,aAAa,CAACA,aAAa,CAACkC,WAAW,CAACrB,SAAS,EAAE,EAAEpB,KAAK,CAAC+C,iBAAiB,CAACrB,QAAQ,CAAC,IAAIe,WAAW,CAACO,SAAS,CAAC,IAC5GhC,OAAO,CAACE,MAAM,KAAK,KAAK,CAAC;EAC7B;AACJ;AACA;EACI,OAAO,MAAM;IACT,MAAM+B,gBAAgB,GAAGA,CAAA,KAAM;MAAE,IAAIC,EAAE,EAAEC,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG7C,KAAK,CAACA,KAAK,CAACyC,GAAG,CAACjC,OAAO,EAAE2B,IAAI,CAAC,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGN,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACQ,YAAY,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAAE,CAAC;IACvP;AACR;AACA;AACA;IACQ,IAAIE,WAAW,GAAGjD,SAAS,CAACkD,gBAAgB,CAAClD,SAAS,CAACmD,aAAa,CAACxC,mBAAmB,CAAC,EAAEkC,gBAAgB,CAAC;IAC5G;AACR;AACA;IACQ,MAAMO,MAAM,GAAGhD,OAAO,CAACiD,gBAAgB,CAACJ,WAAW,EAAET,UAAU,CAAC;IAChE,IAAI5C,KAAK,CAAC+C,iBAAiB,CAACrB,QAAQ,CAAC,EAAE;MACnC,MAAMgC,MAAM,GAAGhC,QAAQ,CAACiC,eAAe,CAACN,WAAW,EAAEvC,GAAG,KAAK,SAAS,EAAEmC,gBAAgB,EAAET,IAAI,EAAEC,WAAW,CAAC;MAC5Gf,QAAQ,GAAGgC,MAAM,CAACxD,MAAM;MACxBmD,WAAW,GAAGK,MAAM,CAACtD,SAAS,IAAIiD,WAAW;MAC7ChC,QAAQ,GAAGqC,MAAM,CAACrC,QAAQ,IAAIA,QAAQ;IAC1C;IACA;AACR;AACA;AACA;AACA;IACQ,IAAItB,MAAM,CAAC6D,QAAQ,CAACpB,IAAI,CAAC,EAAE;MACvB,IAAIrC,gBAAgB,CAACkC,QAAQ,CAACwB,mBAAmB,CAAC,CAAC,EAAE;QACjD9D,MAAM,CAAC+D,mBAAmB,CAACtB,IAAI,CAAC;MACpC,CAAC,MACI;QACDJ,kBAAkB,GAAG,KAAK;MAC9B;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIF,gBAAgB,IAChB,CAAC/B,gBAAgB,CAACkC,QAAQ,CAAC0B,YAAY,CAAC,CAAC,KACxC/D,KAAK,CAACgE,UAAU,CAACtC,QAAQ,CAAC,IAAK1B,KAAK,CAACiE,YAAY,CAACvC,QAAQ,CAAC,IAAIA,QAAQ,CAACwC,IAAI,CAAClE,KAAK,CAACgE,UAAU,CAAE,CAAC,EAAE;MACnG5B,kBAAkB,GAAG,KAAK;IAC9B;IACA;AACR;AACA;IACQ,IAAIA,kBAAkB,EAAE;MACpB;AACZ;AACA;AACA;MACY,IAAIQ,UAAU,EAAE;QACZS,WAAW,GAAGA,WAAW,CAACc,GAAG,CAAEC,KAAK,IAAKpE,KAAK,CAACqE,QAAQ,CAACD,KAAK,CAAC,GAAGxB,UAAU,CAAC0B,aAAa,CAACF,KAAK,CAAC,GAAGA,KAAK,CAAC;MAC7G;MACA;AACZ;AACA;AACA;MACY,IAAIf,WAAW,CAACkB,MAAM,KAAK,CAAC,KACvB,CAACpE,gBAAgB,CAACkC,QAAQ,CAACmC,gBAAgB,CAAC,CAAC,IAAIrD,WAAW,CAAC,EAAE;QAChEkC,WAAW,CAACoB,OAAO,CAACxB,gBAAgB,CAAC,CAAC,CAAC;MAC3C;MACA,MAAMyB,gBAAgB,GAAG;QACrBnD,KAAK,EAAEvB,KAAK,CAAC2E,IAAI,CAACC,EAAE,CAACrD,KAAK,CAAC;QAC3BF,QAAQ,EAAErB,KAAK,CAAC2E,IAAI,CAACC,EAAE,CAACvD,QAAQ,CAAC;QACjCG,QAAQ,EAAExB,KAAK,CAAC2E,IAAI,CAACC,EAAE,CAACpD,QAAQ,CAAC;QACjCtB,MAAM,EAAE,CAACF,KAAK,CAACiE,YAAY,CAACvC,QAAQ,CAAC,GAC/BxB,MAAM,CAAC2E,aAAa,CAACnD,QAAQ,EAAEL,QAAQ,CAAC,GACxCyD,SAAS;QACflD,SAAS;QACTmD,UAAU,EAAEtD,MAAM,GAAG,CAAC;QACtBuD,IAAI,EAAE;MACV,CAAC;MACD5D,SAAS,GAAGP,OAAO,CAACoE,OAAO,CAAC;QACxB,CAACzC,IAAI,GAAGa,WAAW;QACnBxB,MAAM;QACN3B,MAAM,EAAEF,KAAK,CAACiE,YAAY,CAACvC,QAAQ,CAAC,GAC9BA,QAAQ,CAACyC,GAAG,CAAEe,UAAU,IAAKhF,MAAM,CAAC2E,aAAa,CAACK,UAAU,EAAE7D,QAAQ,CAAC,CAAC,GACxEyD;MACV,CAAC,EAAEJ,gBAAgB,CAAC;MACpB;AACZ;AACA;MACY,IAAI,CAACtD,SAAS,CAAC+D,QAAQ,EAAE;QACrB/D,SAAS,CAAC+D,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UAClDlE,SAAS,CAACmE,QAAQ,GAAGF,OAAO;UAC5BjE,SAAS,CAACoE,QAAQ,GAAGF,MAAM;QAC/B,CAAC,CAAC;MACN;MACA,MAAMG,MAAM,GAAGpC,WAAW,CAACA,WAAW,CAACkB,MAAM,GAAG,CAAC,CAAC;MAClDnD,SAAS,CAAC+D,QAAQ,CACbO,IAAI,CAAC,MAAM;QACZ,IAAI/D,OAAO,EACP;QACJ;QACAtB,KAAK,CAACA,KAAK,CAACsF,GAAG,CAAC9E,OAAO,EAAE2B,IAAI,EAAEiD,MAAM,CAAC;QACtC;QACArE,SAAS,CAACwE,MAAM,CAAC,CAAC;MACtB,CAAC,CAAC,CACGC,KAAK,CAAC7F,KAAK,CAAC8F,IAAI,CAAC;MACtB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAChE,uBAAuB,EACxBV,SAAS,CAAC2E,YAAY,GAAG,QAAQ;MACrC;AACZ;AACA;AACA;IACQ,CAAC,MACI,IAAI9E,iBAAiB,IAAIiB,gBAAgB,EAAE;MAC5C;AACZ;AACA;AACA;MACYmB,WAAW,GAAGA,WAAW,CAACc,GAAG,CAAEC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAG4B,UAAU,CAAC5B,KAAK,CAAC,GAAGA,KAAK,CAAC;MAC/F;AACZ;AACA;AACA;MACY,IAAIf,WAAW,CAACkB,MAAM,KAAK,CAAC,EAAE;QAC1BlB,WAAW,CAACoB,OAAO,CAACuB,UAAU,CAAC/C,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACvD;MACA7B,SAAS,GAAG,IAAIH,iBAAiB,CAAEgF,MAAM,IAAK;QAC1C5F,KAAK,CAACA,KAAK,CAACsF,GAAG,CAAC9E,OAAO,EAAE2B,IAAI,EAAEgB,MAAM,GAAGA,MAAM,CAACyC,MAAM,CAAC,GAAGA,MAAM,CAAC;MACpE,CAAC,EAAE5C,WAAW,EAAE6C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnF,OAAO,CAAC,EAAE;QAAEK,QAAQ;QAChEnB,MAAM,EAAEwB;MAAS,CAAC,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,MAAM+D,MAAM,GAAGpC,WAAW,CAACA,WAAW,CAACkB,MAAM,GAAG,CAAC,CAAC;MAClDlE,KAAK,CAACA,KAAK,CAACsF,GAAG,CAAC9E,OAAO,EAAE2B,IAAI,EAAEI,UAAU,IAAI5C,KAAK,CAACqE,QAAQ,CAACoB,MAAM,CAAC,GAC7D7C,UAAU,CAAC0B,aAAa,CAACmB,MAAM,CAAC,GAChCA,MAAM,CAAC;IACjB;IACA,IAAItE,WAAW,EAAE;MACbD,MAAM,CAACL,OAAO,EAAEC,GAAG,EAAEuC,WAAW,EAAE;QAC9BhC,QAAQ;QACRE,KAAK,EAAEA,KAAK;QACZrB,MAAM,EAAEwB,QAAQ;QAChBD,MAAM;QACNI;MACJ,CAAC,EAAE,YAAY,CAAC;IACpB;IACAY,WAAW,CAAC2D,YAAY,CAAChF,SAAS,CAAC;IACnC,IAAIA,SAAS,IAAI,CAACW,QAAQ,EACtBX,SAAS,CAACiF,KAAK,CAAC,CAAC;IACrB,OAAOjF,SAAS;EACpB,CAAC;AACL;AAEAkF,OAAO,CAAC1F,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}